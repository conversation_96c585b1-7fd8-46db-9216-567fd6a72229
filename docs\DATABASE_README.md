# 🗄️ Database Setup - Herbalicious E-commerce

## Overview
This project uses **Prisma ORM** with a **PostgreSQL database** hosted on **Xata**. The database is fully configured and ready for your e-commerce application.

## ✅ Current Status
- ✅ **Database Connected**: PostgreSQL on Xata
- ✅ **Prisma Configured**: Schema and client generated
- ✅ **Tables Created**: All e-commerce tables are set up
- ✅ **Sample Data**: Categories, products, and users populated
- ✅ **APIs Working**: REST endpoints for products and categories

## 📊 Database Schema

### Core Tables
- **Users** - Customer and admin accounts
- **Categories** - Product categories with hierarchy support
- **Products** - Product catalog with variants and images
- **Orders** - Order management with items and addresses
- **Reviews** - Product reviews and ratings
- **Addresses** - Customer shipping addresses
- **Wishlist** - Customer wishlist items
- **Settings** - Application configuration

### Sample Data Included
- **3 Categories**: Skincare, Hair Care, Body Care
- **4 Products**: Botanical Cleanser, Hydrating Serum, Hair Oil, Body Butter
- **2 Users**: Admin and sample customer

## 🚀 Quick Start

### Database Commands
```bash
# Generate Prisma client
npm run db:generate

# Push schema changes to database
npm run db:push

# Open Prisma Studio (database GUI)
npm run db:studio

# Seed database with sample data
npx tsx scripts/seed.ts
```

### API Endpoints
- `GET /api/categories` - List all categories
- `GET /api/products` - List all products

## 🔧 Configuration

### Environment Variables
```env
DATABASE_URL="postgresql://..."  # Your Xata database URL
```

### Prisma Schema Location
- Schema: `prisma/schema.prisma`
- Generated client: `node_modules/@prisma/client`
- Database utilities: `app/lib/db.ts`

## 📝 Usage Examples

### Using Prisma in API Routes
```typescript
import { prisma } from '@/app/lib/db';

// Get all products
const products = await prisma.product.findMany({
  include: {
    category: true,
    images: true,
  },
});

// Create a new product
const product = await prisma.product.create({
  data: {
    name: 'New Product',
    price: 29.99,
    categoryId: 'category-id',
  },
});
```

### Database Connection
The database connection is automatically managed through the Prisma client singleton in `app/lib/db.ts`.

## 🛠️ Development

### Adding New Tables
1. Update `prisma/schema.prisma`
2. Run `npm run db:push`
3. Run `npm run db:generate`

### Modifying Existing Tables
1. Update the schema
2. Push changes with `npm run db:push`
3. Regenerate client with `npm run db:generate`

## 📈 Monitoring

### Prisma Studio
Run `npm run db:studio` to open a visual database editor at `http://localhost:5555`

## 🔒 Security

- Database credentials stored in environment variables
- Prisma client with query logging in development
- Connection pooling handled automatically
- SQL injection protection through Prisma ORM

## 📚 Resources

- [Prisma Documentation](https://www.prisma.io/docs)
- [Xata Documentation](https://xata.io/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs)

---

Your database is now fully configured and ready for development! 🎉
