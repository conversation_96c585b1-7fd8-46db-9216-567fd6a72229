import { NextRequest, NextResponse } from 'next/server'; import { listR2Files } from '../../../lib/r2'; export async function GET(request: NextRequest) { try { const files = await listR2Files('uploads', 10); return NextResponse.json({ success: true, files: files.slice(0, 5), totalFiles: files.length }); } catch (error) { console.error('R2 debug error:', error); return NextResponse.json({ success: false, error: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 }); } }