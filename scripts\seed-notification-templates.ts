const { PrismaClient, NotificationType } = require('@prisma/client');

const prisma = new PrismaClient();

const notificationTemplates = [
  {
    name: 'order_placed',
    type: NotificationType.ORDER_PLACED,
    title: 'Order Placed Successfully',
    message: 'Your order #{orderNumber} has been placed successfully. We\'ll send you updates as your order progresses.',
    emailSubject: 'Order Confirmation - Herbalicious',
    emailTemplate: `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2d5a27; margin: 0;">Herbalicious</h1>
          <p style="color: #666; margin: 5px 0 0 0;">Natural Skincare Essentials</p>
        </div>
        
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #2d5a27;">
          <h2 style="color: #2d5a27; margin: 0 0 15px 0;">Order Placed Successfully!</h2>
          <p style="color: #333; line-height: 1.6; margin: 0;">Thank you for your order! We've received your order and will send you updates as it progresses.</p>
        </div>
        
        <div style="background: #fff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
          <h3 style="color: #333; margin: 0 0 15px 0;">Order Details</h3>
          <p><strong>Order Number:</strong> {orderNumber}</p>
          <p><strong>Order Total:</strong> {currency} {amount}</p>
          <p><strong>Items:</strong> {itemCount} item(s)</p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="{baseUrl}/orders/{orderId}" 
             style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
            View Order Details
          </a>
        </div>
        
        <div style="text-align: center; color: #999; font-size: 12px; margin-top: 30px;">
          <p>Thank you for choosing Herbalicious for your natural skincare needs!</p>
        </div>
      </div>
    `,
  },
  {
    name: 'order_shipped',
    type: NotificationType.ORDER_SHIPPED,
    title: 'Order Shipped',
    message: 'Great news! Your order #{orderNumber} has been shipped.',
    emailSubject: 'Your Order Has Shipped - Herbalicious',
    emailTemplate: `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2d5a27; margin: 0;">Herbalicious</h1>
          <p style="color: #666; margin: 5px 0 0 0;">Natural Skincare Essentials</p>
        </div>
        
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #2d5a27;">
          <h2 style="color: #2d5a27; margin: 0 0 15px 0;">Your Order Has Shipped! 📦</h2>
          <p style="color: #333; line-height: 1.6; margin: 0;">Great news! Your order is on its way to you.</p>
        </div>
        
        <div style="background: #fff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
          <h3 style="color: #333; margin: 0 0 15px 0;">Shipping Details</h3>
          <p><strong>Order Number:</strong> {orderNumber}</p>
          <p><strong>Estimated Delivery:</strong> {estimatedDelivery}</p>
          <p><strong>Estimated Delivery:</strong> {estimatedDelivery}</p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="{baseUrl}/orders/{orderId}" 
             style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;">
            Track Your Order
          </a>
        </div>
        
        <div style="text-align: center; color: #999; font-size: 12px; margin-top: 30px;">
          <p>We can't wait for you to experience your natural skincare products!</p>
        </div>
      </div>
    `,
  },
  {
    name: 'order_delivered',
    type: NotificationType.ORDER_DELIVERED,
    title: 'Order Delivered',
    message: 'Your order #{orderNumber} has been delivered successfully! We hope you love your natural skincare products.',
    emailSubject: 'Order Delivered - Herbalicious',
    emailTemplate: `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2d5a27; margin: 0;">Herbalicious</h1>
          <p style="color: #666; margin: 5px 0 0 0;">Natural Skincare Essentials</p>
        </div>
        
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #2d5a27;">
          <h2 style="color: #2d5a27; margin: 0 0 15px 0;">Order Delivered! 🎉</h2>
          <p style="color: #333; line-height: 1.6; margin: 0;">Your order has been delivered successfully. We hope you love your natural skincare products!</p>
        </div>
        
        <div style="background: #fff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
          <h3 style="color: #333; margin: 0 0 15px 0;">Delivery Details</h3>
          <p><strong>Order Number:</strong> {orderNumber}</p>
          <p><strong>Delivered At:</strong> {deliveredAt}</p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="{baseUrl}/orders/{orderId}" 
             style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;">
            View Order
          </a>
          <a href="{baseUrl}/reviews/new?order={orderId}" 
             style="background-color: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Leave a Review
          </a>
        </div>
        
        <div style="text-align: center; color: #999; font-size: 12px; margin-top: 30px;">
          <p>Thank you for choosing Herbalicious! We'd love to hear about your experience.</p>
        </div>
      </div>
    `,
  },
  {
    name: 'price_drop_alert',
    type: NotificationType.PRICE_DROP_ALERT,
    title: 'Price Drop Alert!',
    message: 'Great news! {productName} is now {discountPercentage}% off!',
    emailSubject: 'Price Drop Alert - {productName} - Herbalicious',
    emailTemplate: `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2d5a27; margin: 0;">Herbalicious</h1>
          <p style="color: #666; margin: 5px 0 0 0;">Natural Skincare Essentials</p>
        </div>
        
        <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #f59e0b;">
          <h2 style="color: #92400e; margin: 0 0 15px 0;">🔥 Price Drop Alert!</h2>
          <p style="color: #333; line-height: 1.6; margin: 0;">A product in your wishlist is now on sale!</p>
        </div>
        
        <div style="background: #fff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
          <h3 style="color: #333; margin: 0 0 15px 0;">{productName}</h3>
          <p style="color: #ef4444; font-size: 18px; margin: 0;"><strong>Was:</strong> <span style="text-decoration: line-through;">{currency} {oldPrice}</span></p>
          <p style="color: #16a34a; font-size: 24px; margin: 5px 0;"><strong>Now:</strong> {currency} {newPrice}</p>
          <p style="color: #f59e0b; font-size: 16px; margin: 0;"><strong>Save {discountPercentage}%!</strong></p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="{baseUrl}/products/{productId}" 
             style="background-color: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; font-size: 16px;">
            Shop Now - Limited Time!
          </a>
        </div>
        
        <div style="text-align: center; color: #999; font-size: 12px; margin-top: 30px;">
          <p>This price drop alert was sent because this product is in your wishlist.</p>
          <p>Hurry, this offer may not last long!</p>
        </div>
      </div>
    `,
  },
  {
    name: 'review_request',
    type: NotificationType.REVIEW_REQUEST,
    title: 'How was your experience?',
    message: 'We\'d love to hear about your experience with your recent purchase. Your review helps other customers make informed decisions!',
    emailSubject: 'How was your experience? - Herbalicious',
    emailTemplate: `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2d5a27; margin: 0;">Herbalicious</h1>
          <p style="color: #666; margin: 5px 0 0 0;">Natural Skincare Essentials</p>
        </div>
        
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #2d5a27;">
          <h2 style="color: #2d5a27; margin: 0 0 15px 0;">How was your experience? ⭐</h2>
          <p style="color: #333; line-height: 1.6; margin: 0;">We hope you're loving your natural skincare products! Your feedback helps us improve and helps other customers make informed decisions.</p>
        </div>
        
        <div style="background: #fff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
          <h3 style="color: #333; margin: 0 0 15px 0;">Order #{orderNumber}</h3>
          <p style="color: #666; margin: 0;">Products: {productNames}</p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="{baseUrl}/reviews/new?order={orderId}" 
             style="background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Write a Review
          </a>
        </div>
        
        <div style="text-align: center; color: #999; font-size: 12px; margin-top: 30px;">
          <p>Your honest review helps other customers discover the best natural skincare products.</p>
        </div>
      </div>
    `,
  },
];

async function seedNotificationTemplates() {
  console.log('Seeding notification templates...');

  for (const template of notificationTemplates) {
    try {
      await prisma.notificationTemplate.upsert({
        where: { name: template.name },
        update: template,
        create: template,
      });
      console.log(`✓ Created/updated template: ${template.name}`);
    } catch (error) {
      console.error(`✗ Error creating template ${template.name}:`, error);
    }
  }

  console.log('Notification templates seeded successfully!');
}

if (require.main === module) {
  seedNotificationTemplates()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

module.exports = { seedNotificationTemplates };
