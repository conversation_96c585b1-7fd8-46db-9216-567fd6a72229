import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../lib/auth';
import { prisma } from '../../../lib/db';
import { handleApiError, AuthenticationError, ValidationError, NotFoundError, asyncHandler } from '../../../lib/errors';
import { logger } from '../../../lib/logger';
import { orderNotifications } from '../../../lib/notification-helpers';

const updateOrderSchema = z.object({
  status: z.enum(['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELLED', 'REFUNDED']).optional(),
  paymentStatus: z.enum(['PENDING', 'PAID', 'FAILED', 'REFUNDED']).optional(),
  trackingNumber: z.string().optional(),
  notes: z.string().optional()
});

// GET /api/orders/[orderId] - Get single order details
export const GET = asyncHandler(async (request: NextRequest, { params }: { params: { orderId: string } }) => {
  logger.apiRequest('GET', `/api/orders/${params.orderId}`);

  // Check authentication
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new AuthenticationError('Authentication required');
  }

  const isAdmin = session.user.role === 'ADMIN';

  try {
    // Build where clause - non-admin users can only see their own orders
    // Support both order ID and order number
    const whereClause: any = {
      OR: [
        { id: params.orderId },
        { orderNumber: params.orderId }
      ]
    };
    if (!isAdmin) {
      whereClause.AND = { userId: session.user.id };
    }

    const order = await prisma.order.findFirst({
      where: whereClause,
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
                price: true,
                images: true
              }
            }
          }
        },
        address: true,
        user: isAdmin ? {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true
          }
        } : false,
        couponUsages: {
          include: {
            coupon: {
              select: {
                id: true,
                code: true,
                name: true
              }
            }
          }
        }
      }
    });

    if (!order) {
      throw new NotFoundError('Order not found');
    }

    logger.info('Order retrieved successfully', {
      orderId: params.orderId,
      userId: session.user.id,
      isAdmin
    });

    return NextResponse.json({
      success: true,
      order
    });

  } catch (error) {
    logger.error('Failed to retrieve order', error as Error);
    throw error;
  }
});

// PATCH /api/orders/[orderId] - Update order (admin only)
export const PATCH = asyncHandler(async (request: NextRequest, { params }: { params: { orderId: string } }) => {
  logger.apiRequest('PATCH', `/api/orders/${params.orderId}`);

  // Check authentication and admin role
  const session = await getServerSession(authOptions);
  if (!session?.user?.id || session.user.role !== 'ADMIN') {
    throw new AuthenticationError('Admin access required');
  }

  const body = await request.json();
  const validatedData = updateOrderSchema.parse(body);

  // Validate that tracking number is provided when changing status to SHIPPED
  if (validatedData.status === 'SHIPPED' && !validatedData.trackingNumber) {
    throw new ValidationError('Tracking number is required when marking order as shipped');
  }

  try {
    // Get current order - support both ID and order number
    const currentOrder = await prisma.order.findFirst({
      where: {
        OR: [
          { id: params.orderId },
          { orderNumber: params.orderId }
        ]
      },
      include: {
        user: true
      }
    });

    if (!currentOrder) {
      throw new NotFoundError('Order not found');
    }

    // Update order
    const updatedOrder = await prisma.order.update({
      where: { id: currentOrder.id },
      data: {
        ...(validatedData.status && { status: validatedData.status }),
        ...(validatedData.paymentStatus && { paymentStatus: validatedData.paymentStatus }),
        ...(validatedData.trackingNumber && { trackingNumber: validatedData.trackingNumber }),
        ...(validatedData.notes && { notes: validatedData.notes }),
        updatedAt: new Date()
      },
      include: {
        items: {
          include: {
            product: true
          }
        },
        address: true,
        user: true
      }
    });

    // Send notifications based on status change
    if (validatedData.status && validatedData.status !== currentOrder.status) {
      try {
        switch (validatedData.status) {
          case 'CONFIRMED':
            await orderNotifications.orderConfirmed(currentOrder.userId, {
              orderId: updatedOrder.id,
              orderNumber: updatedOrder.orderNumber
            });
            break;
          case 'PROCESSING':
            await orderNotifications.orderProcessing(currentOrder.userId, {
              orderId: updatedOrder.id,
              orderNumber: updatedOrder.orderNumber
            });
            break;
          case 'SHIPPED':
            await orderNotifications.orderShipped(currentOrder.userId, {
              orderId: updatedOrder.id,
              orderNumber: updatedOrder.orderNumber,
              estimatedDelivery: (updatedOrder as any).estimatedDelivery || undefined
            });
            break;
          case 'DELIVERED':
            await orderNotifications.orderDelivered(currentOrder.userId, {
              orderId: updatedOrder.id,
              orderNumber: updatedOrder.orderNumber
            });
            // Update payment status to PAID for COD orders
            if (updatedOrder.paymentMethod === 'COD' && updatedOrder.paymentStatus !== 'PAID') {
              await prisma.order.update({
                where: { id: currentOrder.id },
                data: { paymentStatus: 'PAID' }
              });
            }
            break;
          case 'CANCELLED':
            await orderNotifications.orderCancelled(currentOrder.userId, {
              orderId: updatedOrder.id,
              orderNumber: updatedOrder.orderNumber,
              reason: validatedData.notes
            });
            break;
          case 'REFUNDED':
            await orderNotifications.orderCancelled(currentOrder.userId, {
              orderId: updatedOrder.id,
              orderNumber: updatedOrder.orderNumber,
              reason: validatedData.notes || 'Order has been refunded',
              refundAmount: updatedOrder.total,
              currency: updatedOrder.currency
            });
            break;
        }
      } catch (notificationError) {
        logger.error('Failed to send status update notification', notificationError as Error);
        // Don't fail the update if notification fails
      }
    }

    logger.info('Order updated successfully', {
      orderId: params.orderId,
      updates: validatedData,
      adminId: session.user.id
    });

    return NextResponse.json({
      success: true,
      order: updatedOrder
    });

  } catch (error) {
    logger.error('Failed to update order', error as Error);
    throw error;
  }
});

// DELETE /api/orders/[orderId] - Cancel order
export const DELETE = asyncHandler(async (request: NextRequest, { params }: { params: { orderId: string } }) => {
  logger.apiRequest('DELETE', `/api/orders/${params.orderId}`);

  // Check authentication
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new AuthenticationError('Authentication required');
  }

  const isAdmin = session.user.role === 'ADMIN';

  try {
    // Get order - support both ID and order number
    const order = await prisma.order.findFirst({
      where: {
        AND: [
          {
            OR: [
              { id: params.orderId },
              { orderNumber: params.orderId }
            ]
          },
          ...(!isAdmin ? [{ userId: session.user.id }] : [])
        ]
      }
    });

    if (!order) {
      throw new NotFoundError('Order not found');
    }

    // Check if order can be cancelled
    if (!['PENDING', 'CONFIRMED'].includes(order.status)) {
      throw new ValidationError('Order cannot be cancelled in current status');
    }

    // Update order status to cancelled
    const cancelledOrder = await prisma.order.update({
      where: { id: order.id },
      data: {
        status: 'CANCELLED',
        updatedAt: new Date()
      }
    });

    // Send cancellation notification
    try {
      await orderNotifications.orderCancelled(order.userId, {
        orderId: order.id,
        orderNumber: order.orderNumber,
        reason: 'Cancelled by ' + (isAdmin ? 'admin' : 'customer')
      });
    } catch (notificationError) {
      logger.error('Failed to send cancellation notification', notificationError as Error);
    }

    logger.info('Order cancelled successfully', {
      orderId: params.orderId,
      userId: session.user.id,
      isAdmin
    });

    return NextResponse.json({
      success: true,
      message: 'Order cancelled successfully'
    });

  } catch (error) {
    logger.error('Failed to cancel order', error as Error);
    throw error;
  }
});