import { NextResponse } from 'next/server';
import { prisma } from '@/app/lib/db';

// GET /api/dashboard/stats - Get dashboard statistics
export async function GET() {
  try {
    // Get counts for all main entities
    const [
      totalProducts,
      totalCategories,
      totalUsers,
      totalCustomers,
      totalOrders,
      activeProducts,
      featuredProducts,
    ] = await Promise.all([
      prisma.product.count(),
      prisma.category.count(),
      prisma.user.count(),
      prisma.user.count({ where: { role: 'CUSTOMER' } }),
      prisma.order.count(),
      prisma.product.count({ where: { isActive: true } }),
      prisma.product.count({ where: { isFeatured: true } }),
    ]);

    // Get recent products
    const recentProducts = await prisma.product.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        category: true,
        images: {
          take: 1,
          orderBy: {
            position: 'asc',
          },
        },
      },
    });

    // Get recent users
    const recentUsers = await prisma.user.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Calculate some basic stats
    const stats = {
      overview: {
        totalProducts,
        totalCategories,
        totalUsers,
        totalCustomers,
        totalOrders,
        activeProducts,
        featuredProducts,
      },
      recent: {
        products: recentProducts,
        users: recentUsers,
      },
      growth: {
        // These would be calculated based on time periods in a real app
        productsGrowth: '+12.5%',
        categoriesGrowth: '+5.2%',
        usersGrowth: '+8.1%',
        ordersGrowth: '+15.3%',
      },
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch dashboard statistics' },
      { status: 500 }
    );
  }
}
