'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { 
  ArrowLeft, 
  Bell, 
  BellOff,
  CheckCheck,
  ShoppingBag,
  Heart,
  Star,
  MessageSquare,
  AlertCircle,
  Loader2,
  Package,
  Truck,
  CheckCircle,
  XCircle,
  TrendingDown,
  Sparkles,
  Clock,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import Layout from '../components/Layout';
import { useNotifications } from '../context/NotificationContext';

const NotificationsPage = () => {
  const router = useRouter();
  const { data: session, status } = useSession();
  const { notifications, unreadCount, loading, error, fetchNotifications, markAsRead, markAllAsRead } = useNotifications();
  
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // Fetch notifications
  const fetchNotificationsWithPagination = async (page = 1) => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
      });

      const response = await fetch(`/api/notifications?${params}`);
      const data = await response.json();

      if (data.success) {
        setTotalPages(data.data.pagination.totalPages);
        setTotalCount(data.data.pagination.totalCount);
        setCurrentPage(page);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    }
  };

  useEffect(() => {
    if (session?.user?.id) {
      fetchNotificationsWithPagination(1);
    }
  }, [session?.user?.id]);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'ORDER_PLACED':
        return <ShoppingBag className="w-5 h-5" />;
      case 'ORDER_CONFIRMED':
        return <CheckCircle className="w-5 h-5" />;
      case 'ORDER_PROCESSING':
        return <Package className="w-5 h-5" />;
      case 'ORDER_SHIPPED':
        return <Truck className="w-5 h-5" />;
      case 'ORDER_DELIVERED':
        return <CheckCircle className="w-5 h-5" />;
      case 'ORDER_CANCELLED':
        return <XCircle className="w-5 h-5" />;
      case 'WISHLIST_ADDED':
      case 'WISHLIST_REMOVED':
        return <Heart className="w-5 h-5" />;
      case 'PRICE_DROP_ALERT':
        return <TrendingDown className="w-5 h-5" />;
      case 'REVIEW_REQUEST':
      case 'REVIEW_SUBMITTED':
        return <Star className="w-5 h-5" />;
      case 'ADMIN_MESSAGE':
      case 'BROADCAST':
        return <MessageSquare className="w-5 h-5" />;
      case 'PROMOTIONAL':
        return <Sparkles className="w-5 h-5" />;
      default:
        return <Bell className="w-5 h-5" />;
    }
  };

  const getNotificationStyle = (type: string) => {
    switch (type) {
      case 'ORDER_PLACED':
      case 'ORDER_CONFIRMED':
      case 'ORDER_PROCESSING':
        return {
          iconBg: 'bg-green-600',
          iconColor: 'text-white',
          badge: 'bg-green-100 text-green-700 border-green-200'
        };
      case 'ORDER_SHIPPED':
      case 'ORDER_DELIVERED':
        return {
          iconBg: 'bg-green-700',
          iconColor: 'text-white',
          badge: 'bg-green-100 text-green-800 border-green-200'
        };
      case 'ORDER_CANCELLED':
        return {
          iconBg: 'bg-gray-600',
          iconColor: 'text-white',
          badge: 'bg-gray-100 text-gray-700 border-gray-200'
        };
      case 'PRICE_DROP_ALERT':
        return {
          iconBg: 'bg-green-600',
          iconColor: 'text-white',
          badge: 'bg-green-100 text-green-700 border-green-200'
        };
      case 'WISHLIST_ADDED':
      case 'WISHLIST_REMOVED':
        return {
          iconBg: 'bg-green-500',
          iconColor: 'text-white',
          badge: 'bg-green-50 text-green-700 border-green-200'
        };
      case 'REVIEW_REQUEST':
      case 'REVIEW_SUBMITTED':
        return {
          iconBg: 'bg-green-600',
          iconColor: 'text-white',
          badge: 'bg-green-100 text-green-700 border-green-200'
        };
      case 'ADMIN_MESSAGE':
      case 'BROADCAST':
      case 'PROMOTIONAL':
        return {
          iconBg: 'bg-green-700',
          iconColor: 'text-white',
          badge: 'bg-green-100 text-green-800 border-green-200'
        };
      default:
        return {
          iconBg: 'bg-gray-500',
          iconColor: 'text-white',
          badge: 'bg-gray-100 text-gray-700 border-gray-200'
        };
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const formatNotificationType = (type: string) => {
    const typeMap: { [key: string]: string } = {
      'ORDER_PLACED': 'Order Placed',
      'ORDER_CONFIRMED': 'Order Confirmed',
      'ORDER_PROCESSING': 'Processing',
      'ORDER_SHIPPED': 'Shipped',
      'ORDER_DELIVERED': 'Delivered',
      'ORDER_CANCELLED': 'Cancelled',
      'WISHLIST_ADDED': 'Wishlist',
      'WISHLIST_REMOVED': 'Wishlist',
      'PRICE_DROP_ALERT': 'Price Drop',
      'REVIEW_REQUEST': 'Review Request',
      'REVIEW_SUBMITTED': 'Review',
      'ADMIN_MESSAGE': 'Message',
      'BROADCAST': 'Announcement',
      'PROMOTIONAL': 'Promotion',
      'SYSTEM': 'System'
    };
    return typeMap[type] || type.replace('_', ' ');
  };

  const handleNotificationClick = async (notification: any) => {
    if (!notification.isRead) {
      await markAsRead(notification.id);
    }
    
    // Navigate based on notification type
    if (notification.type.startsWith('ORDER_')) {
      if (notification.data?.orderId) {
        router.push(`/order-history`);
      }
    } else if (notification.type === 'PRICE_DROP_ALERT' && notification.data?.productId) {
      router.push(`/product/${notification.data.productId}`);
    }
  };

  if (status === 'loading') {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="w-8 h-8 animate-spin text-green-600" />
        </div>
      </Layout>
    );
  }

  if (!session?.user) {
    return null;
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <button
              onClick={() => router.back()}
              className="inline-flex items-center space-x-2 text-gray-600 hover:text-gray-900 mb-6 group"
            >
              <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform" />
              <span className="font-medium">Back</span>
            </button>
            
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-bold text-gray-900 mb-2">Notifications</h1>
                <p className="text-lg text-gray-600">
                  Stay updated with your orders and activities
                </p>
              </div>
              
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="flex items-center space-x-2 px-6 py-3 bg-white text-green-600 hover:text-green-700 hover:bg-green-50 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 font-medium"
                >
                  <CheckCheck className="w-5 h-5" />
                  <span>Mark all read</span>
                </button>
              )}
            </div>
            
            {/* Stats Cards */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
              <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total</p>
                    <p className="text-2xl font-bold text-gray-900">{totalCount}</p>
                  </div>
                  <Bell className="w-8 h-8 text-gray-400" />
                </div>
              </div>
              
              <div className="bg-green-600 rounded-2xl p-4 shadow-sm text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-green-100">Unread</p>
                    <p className="text-2xl font-bold">{unreadCount}</p>
                  </div>
                  <div className="relative">
                    <Bell className="w-8 h-8 text-green-200" />
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-white rounded-full animate-pulse"></div>
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">This Week</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {notifications.filter(n => {
                        const date = new Date(n.createdAt);
                        const weekAgo = new Date();
                        weekAgo.setDate(weekAgo.getDate() - 7);
                        return date > weekAgo;
                      }).length}
                    </p>
                  </div>
                  <Sparkles className="w-8 h-8 text-gray-400" />
                </div>
              </div>
              
              <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Today</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {notifications.filter(n => {
                        const date = new Date(n.createdAt);
                        const today = new Date();
                        return date.toDateString() === today.toDateString();
                      }).length}
                    </p>
                  </div>
                  <AlertCircle className="w-8 h-8 text-gray-400" />
                </div>
              </div>
            </div>
          </div>

          {/* Notifications List */}
          <div className="space-y-4">
            {loading ? (
              <div className="bg-white rounded-2xl shadow-sm p-12 text-center">
                <Loader2 className="w-12 h-12 animate-spin text-green-600 mx-auto mb-4" />
                <p className="text-gray-600 text-lg">Loading your notifications...</p>
              </div>
            ) : error ? (
              <div className="bg-white rounded-2xl shadow-sm p-12 text-center">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <AlertCircle className="w-8 h-8 text-red-500" />
                </div>
                <p className="text-gray-900 font-semibold text-lg mb-2">Oops! Something went wrong</p>
                <p className="text-gray-600 mb-6">{error}</p>
                <button 
                  onClick={() => fetchNotificationsWithPagination(currentPage)}
                  className="px-6 py-3 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors font-medium"
                >
                  Try again
                </button>
              </div>
            ) : notifications.length === 0 ? (
              <div className="bg-white rounded-2xl shadow-sm p-16 text-center">
                <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <BellOff className="w-12 h-12 text-gray-400" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">No notifications yet</h3>
                <p className="text-gray-600 text-lg mb-8">We'll notify you when something important happens</p>
                <button
                  onClick={() => router.push('/shop')}
                  className="px-8 py-3 bg-green-600 text-white rounded-full hover:bg-green-700 transition-colors font-medium"
                >
                  Start Shopping
                </button>
              </div>
            ) : (
              <>
                {notifications.map((notification) => {
                  const style = getNotificationStyle(notification.type);
                  return (
                    <div
                      key={notification.id}
                      onClick={() => handleNotificationClick(notification)}
                      className={`bg-white rounded-2xl shadow-sm hover:shadow-lg transition-all duration-200 cursor-pointer overflow-hidden group ${
                        !notification.isRead ? 'ring-2 ring-green-500 ring-offset-2' : ''
                      }`}
                    >
                      <div className="p-6">
                        <div className="flex items-start space-x-4">
                          <div className={`w-14 h-14 rounded-full ${style.iconBg} flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-200`}>
                            <div className={style.iconColor}>
                              {getNotificationIcon(notification.type)}
                            </div>
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex-1">
                                <h3 className="text-lg font-semibold text-gray-900 mb-1 group-hover:text-green-600 transition-colors">
                                  {notification.title}
                                </h3>
                                <div className="flex items-center space-x-3 mb-3">
                                  <span className={`px-3 py-1 text-xs font-medium rounded-full border ${style.badge}`}>
                                    {formatNotificationType(notification.type)}
                                  </span>
                                </div>
                              </div>
                              {!notification.isRead && (
                                <div className="w-3 h-3 bg-green-600 rounded-full ml-4 mt-1 flex-shrink-0 animate-pulse"></div>
                              )}
                            </div>
                            
                            <p className="text-gray-600 mb-3 leading-relaxed">
                              {notification.message}
                            </p>
                            
                            {/* Additional data display */}
                            {notification.data && (
                              <div className="mb-3">
                                {notification.data.orderNumber && (
                                  <p className="text-sm text-gray-500">
                                    Order #{notification.data.orderNumber}
                                  </p>
                                )}
                                {notification.data.productName && (
                                  <p className="text-sm text-gray-500">
                                    Product: {notification.data.productName}
                                  </p>
                                )}
                                {notification.data.amount && notification.data.currency && (
                                  <p className="text-sm font-medium text-gray-700">
                                    Amount: {notification.data.currency} {notification.data.amount}
                                  </p>
                                )}
                              </div>
                            )}
                            
                            <div className="flex items-center space-x-2 text-sm text-gray-500">
                              <Clock className="w-4 h-4" />
                              <span>{formatTimeAgo(notification.createdAt)}</span>
                              {notification.isRead && (
                                <>
                                  <span className="text-gray-400">•</span>
                                  <span className="text-green-600">Read</span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </>
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-8 bg-white rounded-2xl shadow-sm p-6">
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-600">
                  Showing {((currentPage - 1) * 20) + 1} to {Math.min(currentPage * 20, totalCount)} of {totalCount} notifications
                </p>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => fetchNotificationsWithPagination(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronLeft className="w-5 h-5" />
                  </button>
                  
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }
                      
                      return (
                        <button
                          key={i}
                          onClick={() => fetchNotificationsWithPagination(pageNum)}
                          className={`w-10 h-10 rounded-lg font-medium transition-colors ${
                            currentPage === pageNum
                              ? 'bg-green-600 text-white'
                              : 'text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}
                  </div>
                  
                  <button
                    onClick={() => fetchNotificationsWithPagination(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronRight className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default NotificationsPage;
