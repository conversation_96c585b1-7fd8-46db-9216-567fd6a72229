import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../../lib/auth';
import { prisma } from '../../../../lib/db';
import { handleApiError, AuthenticationError, AuthorizationError, NotFoundError, ConflictError, ValidationError, asyncHandler } from '../../../../lib/errors';
import { logger } from '../../../../lib/logger';

export const GET = asyncHandler(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const productId = params.id;
  logger.apiRequest('GET', `/api/products/${productId}/variations`);

  // Fetch all variations for the product
  const variations = await prisma.productVariant.findMany({
    where: {
      productId: productId
    },
    orderBy: [
      { name: 'asc' },
      { value: 'asc' }
    ]
  });

  logger.info('Product variations fetched', {
    productId,
    count: variations.length
  });

  return NextResponse.json({
    success: true,
    data: variations
  });
});

export const POST = asyncHandler(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const productId = params.id;
  logger.apiRequest('POST', `/api/products/${productId}/variations`);

  // Check authentication
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    throw new AuthenticationError();
  }
  
  if (session.user.role !== 'ADMIN') {
    throw new AuthorizationError();
  }

  const body = await request.json();
  const { name, value, price, pricingMode } = body;

  // Validate required fields
  if (!name || !value) {
    throw new ValidationError('Name and value are required');
  }

  // Validate pricing mode
  const validPricingModes: Array<'REPLACE' | 'INCREMENT' | 'FIXED'> = ['REPLACE', 'INCREMENT', 'FIXED'];
  const validatedPricingMode = pricingMode && validPricingModes.includes(pricingMode)
    ? pricingMode as 'REPLACE' | 'INCREMENT' | 'FIXED'
    : 'REPLACE';

  // Check if product exists
  const product = await prisma.product.findUnique({
    where: { id: productId }
  });

  if (!product) {
    throw new NotFoundError('Product');
  }

  // Check if variation with same name and value already exists
  const existingVariation = await prisma.productVariant.findFirst({
    where: {
      productId: productId,
      name: name,
      value: value
    }
  });

  if (existingVariation) {
    throw new ConflictError('Variation with this name and value already exists');
  }

  // Parse and validate price
  let parsedPrice: number | null = null;
  if (price !== undefined && price !== null && price !== '') {
    const numPrice = typeof price === 'string' ? parseFloat(price) : Number(price);
    if (!isNaN(numPrice) && isFinite(numPrice)) {
      parsedPrice = numPrice;
    }
  }

  // Create the variation
  const variation = await prisma.productVariant.create({
    data: {
      name: name.trim(),
      value: value.trim(),
      price: parsedPrice,
      pricingMode: validatedPricingMode,
      productId: productId
    }
  });

  logger.info('Product variation created', {
    productId,
    variationId: variation.id,
    name,
    value,
    userId: session.user.id
  });

  return NextResponse.json({
    success: true,
    data: variation,
    message: 'Variation created successfully'
  });
});
