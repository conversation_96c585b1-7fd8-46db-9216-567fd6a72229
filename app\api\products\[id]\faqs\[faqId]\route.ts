import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/db';

// GET /api/products/[id]/faqs/[faqId] - Get a specific FAQ
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string; faqId: string } }
) {
  try {
    const faq = await prisma.productFAQ.findFirst({
      where: {
        id: params.faqId,
        productId: params.id,
      },
    });

    if (!faq) {
      return NextResponse.json(
        { success: false, error: 'FAQ not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: faq,
    });
  } catch (error) {
    console.error('Error fetching FAQ:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch FAQ' },
      { status: 500 }
    );
  }
}

// PUT /api/products/[id]/faqs/[faqId] - Update a specific FAQ
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; faqId: string } }
) {
  try {
    const body = await request.json();
    const { question, answer, position, isActive } = body;

    const faq = await prisma.productFAQ.update({
      where: {
        id: params.faqId,
      },
      data: {
        ...(question && { question }),
        ...(answer && { answer }),
        ...(position !== undefined && { position }),
        ...(isActive !== undefined && { isActive }),
      },
    });

    return NextResponse.json({
      success: true,
      data: faq,
      message: 'FAQ updated successfully',
    });
  } catch (error) {
    console.error('Error updating FAQ:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update FAQ' },
      { status: 500 }
    );
  }
}

// DELETE /api/products/[id]/faqs/[faqId] - Delete a specific FAQ
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; faqId: string } }
) {
  try {
    await prisma.productFAQ.delete({
      where: {
        id: params.faqId,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'FAQ deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting FAQ:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete FAQ' },
      { status: 500 }
    );
  }
}
