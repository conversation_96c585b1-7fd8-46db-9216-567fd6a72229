'use client';

import React from 'react';
import Link from 'next/link';
import { ArrowRight, Sparkles } from 'lucide-react';

interface HeroProps {
  title?: string;
  subtitle?: string;
  ctaText?: string;
  ctaLink?: string;
  secondaryCtaText?: string;
  secondaryCtaLink?: string;
  badgeText?: string;
  backgroundColor?: string;
  trustIndicators?: {
    value1?: string;
    label1?: string;
    value2?: string;
    label2?: string;
    value3?: string;
    label3?: string;
    value4?: string;
    label4?: string;
  };
}

const Hero: React.FC<HeroProps> = ({
  title = "Natural Skincare Essentials",
  subtitle = "Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin",
  ctaText = "Shop Collection",
  ctaLink = "/shop",
  secondaryCtaText = "View Categories",
  secondaryCtaLink = "/categories",
  badgeText = "New Collection",
  backgroundColor = "#f0fdf4",
  trustIndicators = {
    value1: "100%",
    label1: "Natural",
    value2: "5K+",
    label2: "Happy Lifetime Customers",
    value3: "25+",
    label3: "Products",
    value4: "4.8★",
    label4: "Rating"
  }
}) => {
  return (
    <div 
      className="relative px-4 py-12 lg:px-8 lg:py-16"
      style={{ backgroundColor }}
    >
      <div className="max-w-4xl mx-auto text-center">
        {/* Badge */}
        <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mb-6">
          <Sparkles className="w-3 h-3 mr-1" />
          {badgeText}
        </div>

        {/* Title */}
        <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 leading-tight">
          {title}
        </h1>

        {/* Subtitle */}
        <p className="text-lg md:text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
          {subtitle}
        </p>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link
            href={ctaLink}
            className="inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-full hover:bg-green-700 transition-colors duration-200 shadow-lg hover:shadow-xl"
          >
            {ctaText}
            <ArrowRight className="ml-2 w-4 h-4" />
          </Link>
          
          <Link
            href={secondaryCtaLink}
            className="inline-flex items-center px-6 py-3 border-2 border-green-600 text-green-600 font-semibold rounded-full hover:bg-green-50 transition-colors duration-200"
          >
            {secondaryCtaText}
          </Link>
        </div>

        {/* Trust Indicators */}
        <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
          <div>
            <div className="text-2xl font-bold text-gray-900">{trustIndicators.value1}</div>
            <div className="text-sm text-gray-600">{trustIndicators.label1}</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-gray-900">{trustIndicators.value2}</div>
            <div className="text-sm text-gray-600">{trustIndicators.label2}</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-gray-900">{trustIndicators.value3}</div>
            <div className="text-sm text-gray-600">{trustIndicators.label3}</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-gray-900">{trustIndicators.value4}</div>
            <div className="text-sm text-gray-600">{trustIndicators.label4}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
