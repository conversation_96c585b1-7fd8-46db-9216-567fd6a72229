const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create categories
  console.log('Creating categories...');
  const skincare = await prisma.category.create({
    data: {
      name: 'Skincare',
      slug: 'skincare',
      description: 'Natural skincare products for healthy, glowing skin',
      isActive: true,
    },
  });

  const haircare = await prisma.category.create({
    data: {
      name: 'Hair Care',
      slug: 'hair-care',
      description: 'Nourishing hair care products for all hair types',
      isActive: true,
    },
  });

  const bodycare = await prisma.category.create({
    data: {
      name: 'Body Care',
      slug: 'body-care',
      description: 'Luxurious body care essentials',
      isActive: true,
    },
  });

  // Create products
  console.log('Creating products...');
  const products = [
    {
      name: 'Botanical Cleanser',
      slug: 'botanical-cleanser',
      description: 'A gentle, plant-based cleanser that removes impurities while maintaining your skin\'s natural moisture barrier. Formulated with organic chamomile and green tea extracts.',
      shortDescription: 'Gentle plant-based cleanser',
      price: 28.99,
      comparePrice: 35.99,
      categoryId: skincare.id,
      isFeatured: true,
      images: [
        {
          url: 'https://pub-43388d25eeb34a1a8b3d0b0042c2bb9a.r2.dev/botanical-cleanser.jpg',
          alt: 'Botanical Cleanser',
          position: 0,
        },
      ],
    },
    {
      name: 'Hydrating Face Serum',
      slug: 'hydrating-face-serum',
      description: 'Intensive hydration serum with hyaluronic acid and vitamin E. Penetrates deeply to restore moisture and improve skin elasticity.',
      shortDescription: 'Intensive hydration serum',
      price: 45.99,
      comparePrice: 55.99,
      categoryId: skincare.id,
      isFeatured: true,
      images: [
        {
          url: 'https://pub-43388d25eeb34a1a8b3d0b0042c2bb9a.r2.dev/hydrating-serum.jpg',
          alt: 'Hydrating Face Serum',
          position: 0,
        },
      ],
    },
    {
      name: 'Nourishing Hair Oil',
      slug: 'nourishing-hair-oil',
      description: 'Rich blend of argan, jojoba, and rosehip oils to nourish and strengthen hair from root to tip. Perfect for all hair types.',
      shortDescription: 'Rich nourishing hair oil',
      price: 32.99,
      comparePrice: 42.99,
      categoryId: haircare.id,
      isFeatured: false,
      images: [
        {
          url: 'https://pub-43388d25eeb34a1a8b3d0b0042c2bb9a.r2.dev/hair-oil.jpg',
          alt: 'Nourishing Hair Oil',
          position: 0,
        },
      ],
    },
    {
      name: 'Luxurious Body Butter',
      slug: 'luxurious-body-butter',
      description: 'Ultra-rich body butter with shea butter and coconut oil. Provides long-lasting moisture for soft, smooth skin.',
      shortDescription: 'Ultra-rich body butter',
      price: 24.99,
      comparePrice: 29.99,
      categoryId: bodycare.id,
      isFeatured: false,
      images: [
        {
          url: 'https://pub-43388d25eeb34a1a8b3d0b0042c2bb9a.r2.dev/body-butter.jpg',
          alt: 'Luxurious Body Butter',
          position: 0,
        },
      ],
    },
  ];

  // Create products with images
  for (const productData of products) {
    const { images, ...product } = productData;
    
    await prisma.product.create({
      data: {
        ...product,
        images: {
          create: images,
        },
      },
    });
  }

  // Create users
  console.log('Creating users...');
  const adminPassword = await bcrypt.hash('admin123', 12);
  const customerPassword = await bcrypt.hash('customer123', 12);

  const adminUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'ADMIN',
      password: adminPassword,
    },
  });

  const customerUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'John Doe',
      role: 'CUSTOMER',
      phone: '+1234567890',
      password: customerPassword,
    },
  });

  // Create sample orders for the customer to test dynamic profile data
  console.log('Creating sample orders...');
  const allProducts = await prisma.product.findMany();
  
  // Create 3 sample orders
  for (let i = 1; i <= 3; i++) {
    const orderNumber = `ORD-${Date.now()}-${i}`;
    const product = allProducts[i - 1] || allProducts[0];
    const quantity = Math.floor(Math.random() * 3) + 1;
    const total = product.price * quantity;

    await prisma.order.create({
      data: {
        orderNumber,
        userId: customerUser.id,
        status: i === 1 ? 'DELIVERED' : i === 2 ? 'SHIPPED' : 'PENDING',
        paymentStatus: 'PAID',
        paymentMethod: 'card',
        subtotal: total,
        total,
        currency: 'USD',
        items: {
          create: {
            productId: product.id,
            quantity,
            price: product.price,
            total,
          },
        },
        address: {
          create: {
            firstName: 'John',
            lastName: 'Doe',
            address1: '123 Main Street',
            city: 'New York',
            state: 'NY',
            postalCode: '10001',
            country: 'United States',
            phone: '+1234567890',
          },
        },
        createdAt: new Date(Date.now() - (i * 7 * 24 * 60 * 60 * 1000)), // Orders from different weeks
      },
    });
  }

  // Create sample address for the customer
  console.log('Creating sample address...');
  await prisma.address.create({
    data: {
      userId: customerUser.id,
      firstName: 'John',
      lastName: 'Doe',
      address1: '123 Main Street',
      address2: 'Apt 4B',
      city: 'New York',
      state: 'NY',
      postalCode: '10001',
      country: 'United States',
      phone: '+1234567890',
      isDefault: true,
    },
  });

  // Create sample reviews
  console.log('Creating sample reviews...');
  await prisma.review.create({
    data: {
      userId: customerUser.id,
      productId: allProducts[0].id,
      rating: 5,
      title: 'Amazing product!',
      content: 'This cleanser is fantastic. My skin feels so clean and soft.',
      status: 'APPROVED',
    },
  });

  // Create sample wishlist items
  console.log('Creating sample wishlist...');
  await prisma.wishlistItem.create({
    data: {
      userId: customerUser.id,
      productId: allProducts[1].id,
    },
  });

  console.log('✅ Database seeded successfully!');
  console.log(`Created:`);
  console.log(`- 3 categories`);
  console.log(`- ${products.length} products`);
  console.log(`- 2 users`);
  console.log(`- 3 sample orders`);
  console.log(`- 1 address`);
  console.log(`- 1 review`);
  console.log(`- 1 wishlist item`);
  console.log('');
  console.log('🔑 Login Credentials:');
  console.log('Admin: <EMAIL> / admin123');
  console.log('Customer: <EMAIL> / customer123');
  console.log('');
  console.log('📊 Test Data:');
  console.log('- Customer has 3 orders with real totals');
  console.log('- Customer has 1 address, 1 review, 1 wishlist item');
  console.log('- Profile will show dynamic data instead of hardcoded values');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });