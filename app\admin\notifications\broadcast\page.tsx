'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, MessageSquare, AlertTriangle, CheckCircle, Users } from 'lucide-react';

const BroadcastNotification = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userCount, setUserCount] = useState<number | null>(null);

  const [formData, setFormData] = useState({
    title: '',
    content: '',
    type: 'BROADCAST',
    sendEmail: true,
    sendInApp: true,
  });

  React.useEffect(() => {
    fetchUserCount();
  }, []);

  const fetchUserCount = async () => {
    try {
      const response = await fetch('/api/admin/users?limit=1');
      const data = await response.json();
      if (data.success) {
        setUserCount(data.data.pagination.totalCount);
      }
    } catch (error) {
      console.error('Error fetching user count:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim() || !formData.content.trim()) {
      setError('Please provide both title and content');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/admin/notifications/broadcast', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess(true);
        setTimeout(() => {
          router.push('/admin/notifications');
        }, 3000);
      } else {
        setError(data.error || 'Failed to send broadcast notification');
      }
    } catch (error) {
      console.error('Error sending broadcast:', error);
      setError('Failed to send broadcast notification');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
          <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-green-900 mb-2">Broadcast Sent Successfully!</h2>
          <p className="text-green-700">Your broadcast notification has been sent to all users.</p>
          <p className="text-sm text-green-600 mt-2">Redirecting to notifications dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="flex items-center space-x-4 mb-6">
        <button
          onClick={() => router.back()}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-5 h-5 text-gray-600" />
        </button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Broadcast Notification</h1>
          <p className="text-gray-600 mt-1">Send a notification to all users</p>
        </div>
      </div>

      {/* Warning */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 flex items-start space-x-3">
        <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
        <div>
          <h3 className="font-medium text-yellow-900">Broadcast to All Users</h3>
          <p className="text-yellow-700 text-sm mt-1">
            This will send a notification to {userCount ? userCount.toLocaleString() : '...'} users. 
            Please ensure your message is appropriate for all customers.
          </p>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-center space-x-2">
          <AlertTriangle className="w-5 h-5 text-red-600" />
          <span className="text-red-700">{error}</span>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
            <MessageSquare className="w-5 h-5" />
            <span>Broadcast Message</span>
          </h2>

          {/* Title */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
              placeholder="Broadcast notification title"
              required
            />
          </div>

          {/* Content */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Content *
            </label>
            <textarea
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
              placeholder="Broadcast notification content"
              required
            />
          </div>

          {/* Delivery Options */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-700">Delivery Options</h3>
            
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="sendEmail"
                checked={formData.sendEmail}
                onChange={(e) => setFormData(prev => ({ ...prev, sendEmail: e.target.checked }))}
                className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
              />
              <label htmlFor="sendEmail" className="text-sm text-gray-700">
                Send via Email
              </label>
            </div>
            
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="sendInApp"
                checked={formData.sendInApp}
                onChange={(e) => setFormData(prev => ({ ...prev, sendInApp: e.target.checked }))}
                className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
              />
              <label htmlFor="sendInApp" className="text-sm text-gray-700">
                Send In-App Notification
              </label>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            <MessageSquare className="w-4 h-4" />
            <span>{loading ? 'Sending...' : `Broadcast to ${userCount ? userCount.toLocaleString() : '...'} users`}</span>
          </button>
        </div>
      </form>
    </div>
  );
};

export default BroadcastNotification;
