import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/lib/db';

// GET /api/products/[id] - Get a specific product
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Try to find by ID first, then by slug
    let product = await prisma.product.findUnique({
      where: {
        id: params.id,
      },
      include: {
        category: true,
        productCategories: {
          include: {
            category: true,
          },
        },
        images: {
          orderBy: {
            position: 'asc',
          },
        },
        variants: true,
        reviews: {
          include: {
            user: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });

    // If not found by ID, try to find by slug
    if (!product) {
      product = await prisma.product.findUnique({
        where: {
          slug: params.id,
        },
        include: {
          category: true,
          productCategories: {
            include: {
              category: true,
            },
          },
          images: {
            orderBy: {
              position: 'asc',
            },
          },
          variants: true,
          reviews: {
            include: {
              user: true,
            },
            orderBy: {
              createdAt: 'desc',
            },
          },
        },
      });
    }

    if (!product) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: product,
    });
  } catch (error) {
    console.error('Error fetching product:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch product' },
      { status: 500 }
    );
  }
}

// PATCH /api/products/[id] - Update a product
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const {
      name,
      slug,
      description,
      shortDescription,
      price,
      comparePrice,
      sku,
      quantity,
      isFeatured,
      isActive,
      categoryId,
      categoryIds,
      images,
      variations = [],
    } = body;

    // Handle category updates
    const updateData: any = {
      ...(name && { name }),
      ...(slug && { slug }),
      ...(description && { description }),
      ...(shortDescription && { shortDescription }),
      ...(price && { price }),
      ...(comparePrice && { comparePrice }),
      ...(sku && { sku }),
      ...(quantity !== undefined && { quantity }),
      ...(isFeatured !== undefined && { isFeatured }),
      ...(isActive !== undefined && { isActive }),
      ...(categoryId && { categoryId }),
    };

    // If categoryIds is provided, update the many-to-many relationships
    if (categoryIds && Array.isArray(categoryIds)) {
      // First delete existing category relationships
      await prisma.productCategory.deleteMany({
        where: {
          productId: params.id,
        },
      });

      // Then create new relationships
      if (categoryIds.length > 0) {
        updateData.productCategories = {
          create: categoryIds.map((catId: string) => ({
            categoryId: catId,
          })),
        };
      }
    }

    // Handle images update
    if (images !== undefined) {
      // Delete existing images
      await prisma.productImage.deleteMany({
        where: { productId: params.id },
      });

      // Create new images
      if (images.length > 0) {
        updateData.images = {
          create: images.map((img: any, index: number) => ({
            url: img.url,
            alt: img.alt || name || 'Product image',
            position: index,
          })),
        };
      }
    }

    // Handle variations update
    if (variations !== undefined) {
      // Delete existing variations
      await prisma.productVariant.deleteMany({
        where: { productId: params.id },
      });

      // Create new variations
      if (variations.length > 0) {
        updateData.variants = {
          create: variations.map((variation: any) => ({
            name: variation.name,
            value: variation.value,
            price: variation.price || null,
          })),
        };
      }
    }

    const product = await prisma.product.update({
      where: {
        id: params.id,
      },
      data: updateData,
      include: {
        category: true,
        productCategories: {
          include: {
            category: true,
          },
        },
        images: {
          orderBy: {
            position: 'asc',
          },
        },
        variants: true,
      },
    });

    return NextResponse.json({
      success: true,
      data: product,
      message: 'Product updated successfully',
    });
  } catch (error) {
    console.error('Error updating product:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update product' },
      { status: 500 }
    );
  }
}

// PUT /api/products/[id] - Update a product (same as PATCH for compatibility)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return PATCH(request, { params });
}

// DELETE /api/products/[id] - Delete a product
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if product exists in any orders
    const orderItems = await prisma.orderItem.findMany({
      where: {
        productId: params.id,
      },
      include: {
        order: {
          select: {
            id: true,
            status: true,
          },
        },
      },
    });

    if (orderItems.length > 0) {
      // If product is in orders, soft delete by setting isActive to false
      const updatedProduct = await prisma.product.update({
        where: {
          id: params.id,
        },
        data: {
          isActive: false,
          name: `[DELETED] ${new Date().toISOString().split('T')[0]} - ` + (await prisma.product.findUnique({
            where: { id: params.id },
            select: { name: true }
          }))?.name,
        },
      });

      return NextResponse.json({
        success: true,
        message: 'Product has been deactivated (soft deleted) because it exists in order history. It will no longer appear in the store.',
        type: 'soft_delete',
        orderCount: orderItems.length,
      });
    }

    // If no order references, proceed with hard delete
    // Delete related records first
    await prisma.$transaction(async (tx) => {
      // Delete product images
      await tx.productImage.deleteMany({
        where: { productId: params.id },
      });

      // Delete product variants
      await tx.productVariant.deleteMany({
        where: { productId: params.id },
      });

      // Delete product categories (many-to-many relationships)
      await tx.productCategory.deleteMany({
        where: { productId: params.id },
      });

      // Delete product reviews
      await tx.review.deleteMany({
        where: { productId: params.id },
      });

      // Delete product FAQs
      await tx.productFAQ.deleteMany({
        where: { productId: params.id },
      });

      // Delete wishlist items
      await tx.wishlistItem.deleteMany({
        where: { productId: params.id },
      });

      // Finally delete the product
      await tx.product.delete({
        where: { id: params.id },
      });
    });

    return NextResponse.json({
      success: true,
      message: 'Product deleted successfully',
      type: 'hard_delete',
    });
  } catch (error) {
    console.error('Error deleting product:', error);
    
    // Check if it's a foreign key constraint error
    if (error instanceof Error && error.message.includes('Foreign key constraint')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Cannot delete product because it is referenced in orders or other records. The product has been deactivated instead.',
          type: 'constraint_error'
        },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to delete product' },
      { status: 500 }
    );
  }
}
