import { Category } from '../types';

// Utility function to get all categories for a product
export function getProductCategories(product: any): Category[] {
  const categories: Category[] = [];
  
  // Add categories from many-to-many relationship
  if (product.productCategories && product.productCategories.length > 0) {
    product.productCategories.forEach((pc: any) => {
      if (pc.category) {
        categories.push({
          id: pc.category.id,
          name: pc.category.name,
          slug: pc.category.slug,
        });
      }
    });
  }
  
  // Add primary category if not already included and exists
  if (product.category && !categories.some(cat => cat.id === product.category.id)) {
    categories.push({
      id: product.category.id,
      name: product.category.name,
      slug: product.category.slug,
    });
  }
  
  return categories;
}

// Utility function to check if a product belongs to a specific category
export function productBelongsToCategory(product: any, categorySlug: string): boolean {
  const categories = getProductCategories(product);
  return categories.some(cat => cat.slug === categorySlug);
}

// Utility function to get category names as a string
export function getCategoryNamesString(product: any, separator: string = ', '): string {
  const categories = getProductCategories(product);
  return categories.map(cat => cat.name).join(separator);
}

// Utility function to get the primary category (for backward compatibility)
export function getPrimaryCategory(product: any): Category | null {
  // First try to get from the primary category field
  if (product.category) {
    return {
      id: product.category.id,
      name: product.category.name,
      slug: product.category.slug,
    };
  }
  
  // Fallback to first category from many-to-many relationship
  if (product.productCategories && product.productCategories.length > 0) {
    const firstCategory = product.productCategories[0].category;
    return {
      id: firstCategory.id,
      name: firstCategory.name,
      slug: firstCategory.slug,
    };
  }
  
  return null;
}
