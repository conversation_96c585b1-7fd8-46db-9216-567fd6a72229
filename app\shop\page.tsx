import { Suspense } from 'react'
import Layout from '../components/Layout'
import Shop from '../components/pages/Shop'

// Force dynamic rendering for this page since it uses search params
export const dynamic = 'force-dynamic'

function ShopContent() {
  return <Shop />
}

export default function ShopPage() {
  return (
    <Layout>
      <Suspense fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">Loading products...</p>
          </div>
        </div>
      }>
        <ShopContent />
      </Suspense>
    </Layout>
  )
}