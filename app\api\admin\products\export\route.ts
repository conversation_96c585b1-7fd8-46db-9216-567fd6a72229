import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../../lib/auth';
import { prisma } from '../../../../lib/db';

/**
 * GET /api/admin/products/export
 * Export all products as JSON for backup purposes
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is admin
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Fetch all products with related data
    const products = await prisma.product.findMany({
      include: {
        images: true,
        category: true,
        reviews: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        faqs: true,
        variants: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  
    // Prepare export data in import-compatible format
    const exportData = {
      exportDate: new Date().toISOString(),
      totalProducts: products.length,
      products: products.map((product: any) => ({
        name: product.name,
        slug: product.slug,
        description: product.description || '',
        shortDescription: product.shortDescription || '',
        price: product.price || 0,
        comparePrice: product.comparePrice || null,
        isFeatured: product.isFeatured || false,
        isActive: product.isActive !== false,
        categoryNames: product.category ? [product.category.name] : [],
        images: product.images ? product.images.map((image: any) => ({
          url: image.url,
          alt: image.alt || product.name,
          position: image.position || 0,
        })) : [],
        variations: product.variants ? product.variants.map((variant: any) => ({
          name: variant.name,
          value: variant.value,
          price: variant.price || 0,
        })) : [],
        metaTitle: product.metaTitle || '',
        metaDescription: product.metaDescription || '',
        metaKeywords: product.metaKeywords || '',
        tags: product.tags || [],
      })),
    };

    // Check if CSV format is requested
    const format = request.nextUrl.searchParams.get('format') || 'json';
    
    if (format === 'csv') {
      // Generate CSV format
      const csvHeaders = ['Name', 'Slug', 'Description', 'Short Description', 'Price', 'Compare Price', 'Category', 'Categories', 'Featured', 'Active', 'Images', 'Variations'];
      
      const csvRows = products.map((product: any) => {
        const images = product.images ? product.images.map((img: any) => img.url).join(';') : '';
        const variations = product.variants ? JSON.stringify(product.variants.map((v: any) => ({
          name: v.name,
          value: v.value,
          price: v.price || 0
        }))) : '';
        
        return [
          `"${product.name.replace(/"/g, '""')}"`,
          `"${product.slug?.replace(/"/g, '""') || ''}"`,
          `"${(product.description || '').replace(/"/g, '""')}"`,
          `"${(product.shortDescription || '').replace(/"/g, '""')}"`,
          product.price || 0,
          product.comparePrice || '',
          product.category?.name || '',
          product.category?.name || '',
          product.isFeatured ? 'yes' : 'no',
          product.isActive ? 'yes' : 'no',
          `"${images}"`,
          `"${variations.replace(/"/g, '""')}"`
        ].join(',');
      });
      
      const csvContent = [csvHeaders.join(','), ...csvRows].join('\n');
      const filename = `products-backup-${new Date().toISOString().split('T')[0]}.csv`;
      
      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Cache-Control': 'no-cache',
        },
      });
    }
    
    // Default JSON format
    const filename = `products-backup-${new Date().toISOString().split('T')[0]}.json`;
    
    return new NextResponse(JSON.stringify(exportData, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-cache',
      },
    });

  } catch (error) {
    console.error('Error exporting products:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to export products' },
      { status: 500 }
    );
  }
}