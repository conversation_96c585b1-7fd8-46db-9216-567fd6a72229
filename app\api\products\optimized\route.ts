import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const sort = searchParams.get('sort') || 'newest';
    const priceMin = searchParams.get('priceMin');
    const priceMax = searchParams.get('priceMax');

    const skip = (page - 1) * limit;

    // Build optimized where clause
    const where: any = {
      isActive: true,
    };

    // Efficient category filtering
    if (category && category !== 'all') {
      where.OR = [
        { category: { slug: category } },
        { productCategories: { some: { category: { slug: category } } } }
      ];
    }

    // Efficient search filtering
    if (search) {
      const searchConditions = [
        { name: { contains: search, mode: 'insensitive' } },
        { shortDescription: { contains: search, mode: 'insensitive' } }
      ];

      if (where.OR) {
        // Combine category and search filters using AND logic
        where.AND = [
          { OR: where.OR },
          { OR: searchConditions }
        ];
        delete where.OR;
      } else {
        where.OR = searchConditions;
      }
    }

    // Price range filtering
    if (priceMin || priceMax) {
      where.price = {};
      if (priceMin) where.price.gte = parseFloat(priceMin);
      if (priceMax) where.price.lte = parseFloat(priceMax);
    }

    // Optimized ordering
    let orderBy: any = { createdAt: 'desc' };
    switch (sort) {
      case 'name_asc':
        orderBy = { name: 'asc' };
        break;
      case 'name_desc':
        orderBy = { name: 'desc' };
        break;
      case 'price_asc':
        orderBy = { price: 'asc' };
        break;
      case 'price_desc':
        orderBy = { price: 'desc' };
        break;
      case 'newest':
        orderBy = { createdAt: 'desc' };
        break;
      case 'oldest':
        orderBy = { createdAt: 'asc' };
        break;
    }

    // Use efficient query with selective includes
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          category: true,
          productCategories: {
            include: {
              category: true,
            },
          },
          images: {
            orderBy: { position: 'asc' },
            take: 1,
          },
          variants: {
            orderBy: { price: 'asc' },
            take: 3,
          },
          _count: {
            select: { reviews: true }
          }
        },
        orderBy,
        skip,
        take: limit,
      }),
      prisma.product.count({ where }),
    ]);

    // Transform data for frontend
    const transformedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      slug: product.slug,
      shortDescription: product.shortDescription,
      price: product.price,
      comparePrice: product.comparePrice,
      isFeatured: product.isFeatured,
      createdAt: product.createdAt,
      category: product.category,
      categories: [
        product.category,
        ...product.productCategories.map(pc => pc.category)
      ].filter(Boolean),
      image: product.images[0] || null,
      reviewCount: product._count.reviews,
      variants: product.variants,
    }));

    return NextResponse.json({
      success: true,
      data: transformedProducts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}