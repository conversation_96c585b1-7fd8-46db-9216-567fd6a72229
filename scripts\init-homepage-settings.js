const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function initHomepageSettings() {
  try {
    // Check if homepage settings already exist
    const existingSettings = await prisma.homepageSetting.findUnique({
      where: {
        id: 'homepage-settings',
      },
    });

    if (existingSettings) {
      console.log('Homepage settings already exist');
    } else {
      // Create default homepage settings
      const settings = await prisma.homepageSetting.create({
        data: {
          id: 'homepage-settings',
          bannerText: 'Summer Special Offer - Up to 50% Off',
          bannerCtaText: 'Shop Now',
          bannerCtaLink: '/shop',
          isActive: true,
        },
      });

      console.log('Homepage settings created successfully:', settings);
    }

    // Get a product to set as featured
    const product = await prisma.product.findFirst({
      where: {
        isActive: true,
      },
    });

    if (product) {
      // Update the homepage settings to include the featured product
      const updatedSettings = await prisma.homepageSetting.update({
        where: {
          id: 'homepage-settings',
        },
        data: {
          productOfTheMonthId: product.id,
        },
      });

      // Also mark the product as featured
      await prisma.product.update({
        where: {
          id: product.id,
        },
        data: {
          isFeatured: true,
        },
      });

      console.log('Featured product set successfully:', product.name);
    } else {
      console.log('No active products found to set as featured');
    }
  } catch (error) {
    console.error('Error creating homepage settings:', error);
  } finally {
    await prisma.$disconnect();
  }
}

initHomepageSettings();