import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../lib/auth';
import { prisma } from '../../lib/db';
import { CouponCreateInput } from '../../types';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const { searchParams } = new URL(request.url);
    
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const isActiveParam = searchParams.get('active');
    const showInModuleParam = searchParams.get('showInModule');
    const type = searchParams.get('type');
    const userId = searchParams.get('userId');
    
    const skip = (page - 1) * limit;
    
    const where: any = {};
    
    // Only apply filters if explicitly requested
    if (isActiveParam === 'true') {
      where.isActive = true;
    } else if (isActiveParam === 'false') {
      where.isActive = false;
    }
    
    if (showInModuleParam === 'true') {
      where.showInModule = true;
    } else if (showInModuleParam === 'false') {
      where.showInModule = false;
    }
    
    if (type) {
      where.type = type;
    }
    
    // For regular users, only show active coupons with additional filters
    if (!session?.user || (session.user as any).role !== 'ADMIN') {
      where.isActive = true;
      where.validFrom = { lte: new Date() };
      where.OR = [
        { validUntil: null },
        { validUntil: { gte: new Date() } }
      ];
    }
    // For admins, don't apply any automatic filters - they should see all coupons
    
    const [coupons, total] = await Promise.all([
      prisma.coupon.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          usages: userId ? {
            where: { userId },
            select: { id: true, usedAt: true }
          } : false
        }
      }),
      prisma.coupon.count({ where })
    ]);
    
    return NextResponse.json({
      coupons,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching coupons:', error);
    return NextResponse.json(
      { error: 'Failed to fetch coupons' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || (session.user as any).role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const data: CouponCreateInput = await request.json();
    
    // Validate required fields
    if (!data.code || !data.name || !data.type || !data.discountType || data.discountValue === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Check if coupon code already exists
    const existingCoupon = await prisma.coupon.findUnique({
      where: { code: data.code.toUpperCase() }
    });
    
    if (existingCoupon) {
      return NextResponse.json(
        { error: 'Coupon code already exists' },
        { status: 400 }
      );
    }
    
    const coupon = await prisma.coupon.create({
      data: {
        code: data.code.toUpperCase(),
        name: data.name,
        description: data.description,
        type: data.type,
        discountType: data.discountType,
        discountValue: data.discountValue,
        minimumAmount: data.minimumAmount,
        maximumDiscount: data.maximumDiscount,
        usageLimit: data.usageLimit,
        userUsageLimit: data.userUsageLimit,
        isActive: data.isActive ?? true,
        isStackable: data.isStackable ?? false,
        showInModule: data.showInModule ?? false,
        validFrom: data.validFrom ? new Date(data.validFrom) : new Date(),
        validUntil: data.validUntil ? new Date(data.validUntil) : null,
        applicableProducts: data.applicableProducts || [],
        applicableCategories: data.applicableCategories || [],
        excludedProducts: data.excludedProducts || [],
        excludedCategories: data.excludedCategories || [],
        customerSegments: data.customerSegments || []
      }
    });
    
    return NextResponse.json(coupon, { status: 201 });
  } catch (error) {
    console.error('Error creating coupon:', error);
    return NextResponse.json(
      { error: 'Failed to create coupon' },
      { status: 500 }
    );
  }
}