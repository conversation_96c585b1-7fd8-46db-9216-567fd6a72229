/**
 * Image utilities for handling default images and fallbacks
 */

// Default product image - a professional herbal/natural product placeholder
export const DEFAULT_PRODUCT_IMAGE = '/images/default-product.jpg';

// Default avatar image
export const DEFAULT_AVATAR_IMAGE = '/images/default-avatar.jpg';

// Default category image
export const DEFAULT_CATEGORY_IMAGE = '/images/default-category.jpg';

/**
 * Get product image with fallback
 */
export function getProductImage(imageUrl?: string | null): string {
  if (!imageUrl || imageUrl.includes('placeholder') || imageUrl.includes('api/placeholder')) {
    return DEFAULT_PRODUCT_IMAGE;
  }
  return imageUrl;
}

/**
 * Get avatar image with fallback
 */
export function getAvatarImage(imageUrl?: string | null): string {
  if (!imageUrl || imageUrl.includes('placeholder')) {
    return DEFAULT_AVATAR_IMAGE;
  }
  return imageUrl;
}

/**
 * Get category image with fallback
 */
export function getCategoryImage(imageUrl?: string | null): string {
  if (!imageUrl || imageUrl.includes('placeholder')) {
    return DEFAULT_CATEGORY_IMAGE;
  }
  return imageUrl;
}

/**
 * Generate a placeholder image URL for development
 * In production, this should return actual default images
 */
export function generatePlaceholderImage(width: number, height: number, text?: string): string {
  // For development, you can use a service like picsum or create SVG placeholders
  // For production, return actual default images
  if (process.env.NODE_ENV === 'development') {
    const displayText = text ? encodeURIComponent(text) : 'Product';
    return `https://via.placeholder.com/${width}x${height}/10B981/FFFFFF?text=${displayText}`;
  }
  
  // In production, return appropriate default images based on dimensions
  if (width <= 50 && height <= 50) {
    return DEFAULT_AVATAR_IMAGE;
  }
  return DEFAULT_PRODUCT_IMAGE;
}

/**
 * Image validation utilities
 */
export function isValidImageUrl(url: string): boolean {
  if (!url) return false;
  
  // Check for valid image extensions
  const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg)$/i;
  
  // Check for placeholder URLs that should be replaced
  const placeholderPatterns = [
    /placeholder/i,
    /api\/placeholder/i,
    /via\.placeholder/i,
    /picsum\.photos/i
  ];
  
  // If it's a placeholder, it's not a valid production image
  if (placeholderPatterns.some(pattern => pattern.test(url))) {
    return false;
  }
  
  // Check if it has a valid image extension or is a data URL
  return imageExtensions.test(url) || url.startsWith('data:image/');
}

/**
 * Get optimized image URL for different sizes
 */
export function getOptimizedImageUrl(
  originalUrl: string, 
  width?: number, 
  height?: number,
  quality: number = 80
): string {
  // If it's a placeholder or invalid URL, return default
  if (!isValidImageUrl(originalUrl)) {
    return generatePlaceholderImage(width || 400, height || 400);
  }
  
  // For external URLs, return as-is (could be enhanced with image optimization service)
  if (originalUrl.startsWith('http')) {
    return originalUrl;
  }
  
  // For local images, could be enhanced with Next.js Image optimization
  return originalUrl;
}