import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../lib/auth';
import { prisma } from '../../../lib/db';
import { CartItem, CouponValidationResult } from '../../../types';

interface ValidateRequest {
  couponCode: string;
  cartItems: CartItem[];
  userId?: string;
  subtotal: number;
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const { couponCode, cartItems, userId, subtotal }: ValidateRequest = await request.json();

    if (!couponCode || !cartItems || subtotal === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const result = await validateCoupon(couponCode, cartItems, subtotal, userId);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error validating coupon:', error);
    return NextResponse.json(
      { error: 'Failed to validate coupon' },
      { status: 500 }
    );
  }
}

async function isFlashSaleActive(): Promise<boolean> {
  try {
    const settings = await prisma.homepageSetting.findMany({
      orderBy: {
        createdAt: 'desc',
      },
      take: 1,
    });

    if (settings.length === 0) {
      return false;
    }

    const setting = settings[0];
    
    // Check if flash sale is enabled
    if (!setting.showFlashSale) {
      return false;
    }

    // Check if flash sale has an end date and it's still valid
    if (setting.flashSaleEndDate) {
      const now = new Date();
      const endDate = new Date(setting.flashSaleEndDate);
      
      // If flash sale has ended, return false
      if (endDate < now) {
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Error checking flash sale status:', error);
    return false;
  }
}

async function validateCoupon(
  couponCode: string,
  cartItems: CartItem[],
  subtotal: number,
  userId?: string
): Promise<CouponValidationResult> {
  // Check if flash sale is active
  const flashSaleActive = await isFlashSaleActive();
  if (flashSaleActive) {
    return {
      isValid: false,
      discountAmount: 0,
      errorMessage: 'Coupons cannot be used during flash sale periods'
    };
  }

  // Find the coupon
  const coupon = await prisma.coupon.findUnique({
    where: { code: couponCode.toUpperCase() },
    include: {
      usages: userId ? {
        where: { userId }
      } : false
    }
  });

  if (!coupon) {
    return {
      isValid: false,
      discountAmount: 0,
      errorMessage: 'Coupon code not found'
    };
  }

  // Check if coupon is active
  if (!coupon.isActive) {
    return {
      isValid: false,
      discountAmount: 0,
      errorMessage: 'This coupon is no longer active'
    };
  }

  // Check validity dates
  const now = new Date();
  if (coupon.validFrom > now) {
    return {
      isValid: false,
      discountAmount: 0,
      errorMessage: 'This coupon is not yet valid'
    };
  }

  if (coupon.validUntil && coupon.validUntil < now) {
    return {
      isValid: false,
      discountAmount: 0,
      errorMessage: 'This coupon has expired'
    };
  }

  // Check usage limits
  if (coupon.usageLimit && coupon.usageCount >= coupon.usageLimit) {
    return {
      isValid: false,
      discountAmount: 0,
      errorMessage: 'This coupon has reached its usage limit'
    };
  }

  // Check user usage limit
  if (userId && coupon.userUsageLimit) {
    const userUsageCount = Array.isArray(coupon.usages) ? coupon.usages.length : 0;
    if (userUsageCount >= coupon.userUsageLimit) {
      return {
        isValid: false,
        discountAmount: 0,
        errorMessage: 'You have already used this coupon the maximum number of times'
      };
    }
  }

  // Check minimum amount
  if (coupon.minimumAmount && subtotal < coupon.minimumAmount) {
    return {
      isValid: false,
      discountAmount: 0,
      errorMessage: `Minimum order amount of ₹${coupon.minimumAmount} required`
    };
  }

  // Check product/category applicability
  const applicabilityCheck = checkApplicability(coupon, cartItems);
  if (!applicabilityCheck.isApplicable) {
    return {
      isValid: false,
      discountAmount: 0,
      errorMessage: applicabilityCheck.message
    };
  }

  // Calculate discount
  const discountAmount = calculateDiscount(coupon, cartItems, subtotal, applicabilityCheck.applicableAmount);

  return {
    isValid: true,
    discountAmount,
    coupon: {
      ...coupon,
      description: coupon.description || undefined,
      minimumAmount: coupon.minimumAmount || undefined,
      maximumDiscount: coupon.maximumDiscount || undefined,
      usageLimit: coupon.usageLimit || undefined,
      userUsageLimit: coupon.userUsageLimit || undefined,
      validUntil: coupon.validUntil?.toISOString() || undefined,
      validFrom: coupon.validFrom.toISOString(),
      createdAt: coupon.createdAt.toISOString(),
      updatedAt: coupon.updatedAt.toISOString()
    }
  };
}

function checkApplicability(coupon: any, cartItems: CartItem[]) {
  const productIds = cartItems.map(item => item.product.id);
  const categoryIds = cartItems.flatMap(item => 
    item.product.categories?.map(cat => cat.id) || []
  );

  // Check excluded products
  if (coupon.excludedProducts.length > 0) {
    const hasExcludedProduct = productIds.some(id => coupon.excludedProducts.includes(id));
    if (hasExcludedProduct) {
      return {
        isApplicable: false,
        message: 'This coupon cannot be applied to some items in your cart',
        applicableAmount: 0
      };
    }
  }

  // Check excluded categories
  if (coupon.excludedCategories.length > 0) {
    const hasExcludedCategory = categoryIds.some(id => coupon.excludedCategories.includes(id));
    if (hasExcludedCategory) {
      return {
        isApplicable: false,
        message: 'This coupon cannot be applied to some categories in your cart',
        applicableAmount: 0
      };
    }
  }

  let applicableAmount = 0;

  switch (coupon.type) {
    case 'STORE_WIDE':
      applicableAmount = cartItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
      break;

    case 'PRODUCT_SPECIFIC':
      if (coupon.applicableProducts.length === 0) {
        return {
          isApplicable: false,
          message: 'No applicable products specified for this coupon',
          applicableAmount: 0
        };
      }
      
      const applicableItems = cartItems.filter(item => 
        coupon.applicableProducts.includes(item.product.id)
      );
      
      if (applicableItems.length === 0) {
        return {
          isApplicable: false,
          message: 'This coupon is not applicable to any items in your cart',
          applicableAmount: 0
        };
      }
      
      applicableAmount = applicableItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
      break;

    case 'CATEGORY_SPECIFIC':
      if (coupon.applicableCategories.length === 0) {
        return {
          isApplicable: false,
          message: 'No applicable categories specified for this coupon',
          applicableAmount: 0
        };
      }
      
      const categoryApplicableItems = cartItems.filter(item => 
        item.product.categories?.some(cat => coupon.applicableCategories.includes(cat.id))
      );
      
      if (categoryApplicableItems.length === 0) {
        return {
          isApplicable: false,
          message: 'This coupon is not applicable to any categories in your cart',
          applicableAmount: 0
        };
      }
      
      applicableAmount = categoryApplicableItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
      break;

    case 'BUY_X_GET_Y':
      // For Buy X Get Y, we need to check if the cart has the required buy items
      const buyProductIds = coupon.buyProducts || [];
      const buyCategoryIds = coupon.buyCategories || [];
      
      // Check if cart has required buy items
      const buyItems = cartItems.filter(item => {
        const inBuyProducts = buyProductIds.includes(item.product.id);
        const inBuyCategories = item.product.categories?.some(cat => buyCategoryIds.includes(cat.id)) || false;
        return inBuyProducts || inBuyCategories;
      });

      const totalBuyQuantity = buyItems.reduce((sum, item) => sum + item.quantity, 0);
      
      if (totalBuyQuantity < (coupon.buyQuantity || 1)) {
        return {
          isApplicable: false,
          message: `You need to buy at least ${coupon.buyQuantity} qualifying items to use this coupon`,
          applicableAmount: 0
        };
      }

      // For Buy X Get Y, the applicable amount is the total cart value
      applicableAmount = cartItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
      break;

    default:
      applicableAmount = cartItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
  }

  return {
    isApplicable: true,
    message: '',
    applicableAmount
  };
}

function calculateDiscount(coupon: any, cartItems: CartItem[], subtotal: number, applicableAmount: number): number {
  let discount = 0;

  switch (coupon.discountType) {
    case 'PERCENTAGE':
      discount = (applicableAmount * coupon.discountValue) / 100;
      break;

    case 'FIXED_AMOUNT':
      discount = Math.min(coupon.discountValue, applicableAmount);
      break;

    case 'FREE_SHIPPING':
      // This would typically be handled in shipping calculation
      discount = 0;
      break;

    case 'BUY_X_GET_Y':
      // Calculate Buy X Get Y discount
      const buyQuantity = coupon.buyQuantity || 1;
      const getQuantity = coupon.getQuantity || 1;
      const buyProductIds = coupon.buyProducts || [];
      const buyCategoryIds = coupon.buyCategories || [];
      const getProductIds = coupon.getProducts || [];
      const getCategoryIds = coupon.getCategories || [];
      const maxApplications = coupon.maxApplications || 1;
      
      // Find buy items
      const buyItems = cartItems.filter(item => {
        const inBuyProducts = buyProductIds.includes(item.product.id);
        const inBuyCategories = item.product.categories?.some(cat => buyCategoryIds.includes(cat.id)) || false;
        return inBuyProducts || inBuyCategories;
      });

      // Find get items
      const getItems = cartItems.filter(item => {
        const inGetProducts = getProductIds.includes(item.product.id);
        const inGetCategories = item.product.categories?.some(cat => getCategoryIds.includes(cat.id)) || false;
        return inGetProducts || inGetCategories;
      });

      // Calculate how many times the offer can be applied
      const totalBuyQuantity = buyItems.reduce((sum, item) => sum + item.quantity, 0);
      const timesApplicable = Math.min(
        Math.floor(totalBuyQuantity / buyQuantity),
        maxApplications
      );

      if (timesApplicable > 0 && getItems.length > 0) {
        // Sort get items by price (descending) to give discount on most expensive items first
        const sortedGetItems = [...getItems].sort((a, b) => b.product.price - a.product.price);
        
        let remainingGetQuantity = getQuantity * timesApplicable;
        
        for (const item of sortedGetItems) {
          if (remainingGetQuantity <= 0) break;
          
          const quantityToDiscount = Math.min(item.quantity, remainingGetQuantity);
          
          if (coupon.discountApplication === 'FREE') {
            discount += item.product.price * quantityToDiscount;
          } else if (coupon.discountApplication === 'PERCENTAGE') {
            discount += (item.product.price * quantityToDiscount * (coupon.getDiscountValue || 0)) / 100;
          } else if (coupon.discountApplication === 'FIXED_AMOUNT') {
            discount += Math.min(coupon.getDiscountValue || 0, item.product.price) * quantityToDiscount;
          }
          
          remainingGetQuantity -= quantityToDiscount;
        }
      }
      break;

    default:
      discount = 0;
  }

  // Apply maximum discount limit
  if (coupon.maximumDiscount && discount > coupon.maximumDiscount) {
    discount = coupon.maximumDiscount;
  }

  return Math.round(discount * 100) / 100; // Round to 2 decimal places
}