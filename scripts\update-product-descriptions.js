const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Product short descriptions mapping
const shortDescriptions = {
  'Anti-Pimple Pack': 'Targets acne and blemishes for clear, smooth skin.',
  'Anti Ageing Gel Creme': 'Fights signs of ageing and restores youthful glow.',
  'Baby Hair Oil': 'Gentle nourishment for soft, healthy baby hair.',
  'Beard Oil': 'Softens beard and promotes fuller, healthier growth.',
  'Body Polishing & Glow Massage Oil': 'Enhances skin radiance with deep nourishment.',
  'Bridal Face Ubtan': 'Brightens and preps skin for a radiant bridal glow.',
  'Chiksa': 'Traditional skincare blend for deep cleansing and glow.',
  'Deep Clean & Polishing Face Scrub': 'Exfoliates to remove dead skin and impurities.',
  'Eyebrow Thickening & Lash Lengthening Oil': 'Strengthens brows and lashes naturally.',
  'Face Elixir Oil': 'Revives dull skin with intense hydration and glow.',
  'Face Ubtan': 'Natural cleanser for glowing, refreshed skin.',
  'Glitter Lip Balm': 'Moisturizes lips with a subtle glittery shine.',
  'Gold Dust Skin Tonic': 'Refreshes skin with a golden glow boost.',
  'Hair Growth & Shine Mask': 'Nourishes roots for stronger, shinier hair.',
  'Kumkumadi Face Oil': 'Ayurvedic elixir for glowing, even-toned skin.',
  'Luxury Moisturizing Milk Cream': 'Rich hydration for soft, supple skin.',
  'Maharani Hair Oil': 'Royal blend for stronger, longer, healthier hair.',
  'Neem Face Wash': 'Clears acne and purifies with neem power.',
  'Papaya Face Wash': 'Gently exfoliates and brightens dull skin.',
  'Royal Facial Kit': 'Complete facial care for radiant, refreshed skin.',
  'Saffron Gel': 'Brightens complexion with the richness of saffron.',
  'Saffron Night Repair Cream': 'Repairs and nourishes skin overnight.',
  '24K Night Care': 'Luxurious night duo for glowing, youthful skin.',
  'Snow Face Cleanser': 'Refreshes and deeply cleanses skin gently.',
  'Strawberry Silk Booster': 'Boosts skin softness and fruity radiance.',
  'Supermane Hair Oil': 'Strengthens hair and controls hair fall naturally.',
  'Under-Eye Gel': 'Reduces dark circles and puffiness.',
  'Vitamin-C Serum': 'Brightens skin tone and fades dark spots.'
};

async function updateProductDescriptions() {
  try {
    console.log('🚀 Updating product short descriptions in database...\n');

    // Get all products
    const products = await prisma.product.findMany({
      select: {
        id: true,
        name: true,
        shortDescription: true
      }
    });

    console.log(`Found ${products.length} products in database\n`);

    let updatedCount = 0;

    for (const product of products) {
      const productName = product.name?.trim();
      
      if (!productName) {
        console.warn(`⚠️  Skipping product with no name: ${product.id}`);
        continue;
      }

      // Check if product already has a short description
      if (product.shortDescription && product.shortDescription.trim()) {
        console.log(`⏭️  Skipping "${productName}" - already has description`);
        continue;
      }

      // Find matching short description
      let shortDescription = '';
      
      // Exact match first
      if (shortDescriptions[productName]) {
        shortDescription = shortDescriptions[productName];
      } else {
        // Try partial matches
        const matchingKey = Object.keys(shortDescriptions).find(key => 
          key.toLowerCase().includes(productName.toLowerCase()) ||
          productName.toLowerCase().includes(key.toLowerCase())
        );
        
        if (matchingKey) {
          shortDescription = shortDescriptions[matchingKey];
        }
      }

      if (shortDescription) {
        try {
          await prisma.product.update({
            where: { id: product.id },
            data: { shortDescription }
          });
          
          updatedCount++;
          console.log(`✅ Updated: "${productName}" -> "${shortDescription}"`);
        } catch (error) {
          console.error(`❌ Failed to update "${productName}":`, error.message);
        }
      } else {
        console.log(`⚠️  No description found for: "${productName}"`);
      }
    }

    console.log('\n📊 Update Summary:');
    console.log(`✅ Products updated: ${updatedCount}`);
    console.log(`📝 Total products: ${products.length}`);

  } catch (error) {
    console.error('❌ Error updating product descriptions:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the update
updateProductDescriptions();
