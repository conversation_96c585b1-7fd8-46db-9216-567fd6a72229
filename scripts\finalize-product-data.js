const fs = require('fs');
const path = require('path');

// Product short descriptions mapping
const shortDescriptions = {
  'Anti-Pimple Pack': 'Targets acne and blemishes for clear, smooth skin.',
  'Anti Ageing Gel Creme': 'Fights signs of ageing and restores youthful glow.',
  'Baby Hair Oil': 'Gentle nourishment for soft, healthy baby hair.',
  'Beard Oil': 'Softens beard and promotes fuller, healthier growth.',
  'Body Polishing & Glow Massage Oil': 'Enhances skin radiance with deep nourishment.',
  'Bridal Face Ubtan': 'Brightens and preps skin for a radiant bridal glow.',
  'Chiksa': 'Traditional skincare blend for deep cleansing and glow.',
  'Deep Clean & Polishing Face Scrub': 'Exfoliates to remove dead skin and impurities.',
  'Eyebrow Thickening & Lash Lengthening Oil': 'Strengthens brows and lashes naturally.',
  'Face Elixir Oil': 'Revives dull skin with intense hydration and glow.',
  'Face Ubtan': 'Natural cleanser for glowing, refreshed skin.',
  'Glitter Lip Balm': 'Moisturizes lips with a subtle glittery shine.',
  'Gold Dust Skin Tonic': 'Refreshes skin with a golden glow boost.',
  'Hair Growth & Shine Mask': 'Nourishes roots for stronger, shinier hair.',
  'Kumkumadi Face Oil': 'Ayurvedic elixir for glowing, even-toned skin.',
  'Luxury Moisturizing Milk Cream': 'Rich hydration for soft, supple skin.',
  'Maharani Hair Oil': 'Royal blend for stronger, longer, healthier hair.',
  'Neem Face Wash': 'Clears acne and purifies with neem power.',
  'Papaya Face Wash': 'Gently exfoliates and brightens dull skin.',
  'Royal Facial Kit': 'Complete facial care for radiant, refreshed skin.',
  'Saffron Gel': 'Brightens complexion with the richness of saffron.',
  'Saffron Night Repair Cream': 'Repairs and nourishes skin overnight.',
  '24K Night Care': 'Luxurious night duo for glowing, youthful skin.',
  'Snow Face Cleanser': 'Refreshes and deeply cleanses skin gently.',
  'Strawberry Silk Booster': 'Boosts skin softness and fruity radiance.',
  'Supermane Hair Oil': 'Strengthens hair and controls hair fall naturally.',
  'Under-Eye Gel': 'Reduces dark circles and puffiness.',
  'Vitamin-C Serum': 'Brightens skin tone and fades dark spots.'
};

async function finalizeProductData() {
  try {
    console.log('🚀 Finalizing product data with short descriptions and proper structure...\n');

    // Read the current products file
    const productsPath = path.join(process.cwd(), 'csvjson-updated.json');
    if (!fs.existsSync(productsPath)) {
      console.error('❌ Products file not found:', productsPath);
      process.exit(1);
    }

    const fileData = JSON.parse(fs.readFileSync(productsPath, 'utf8'));
    const productsData = fileData.products || fileData;

    if (!Array.isArray(productsData)) {
      console.error('❌ Invalid products file format. Expected an array.');
      process.exit(1);
    }

    let updatedCount = 0;

    // Update products with short descriptions and proper structure
    const updatedProducts = productsData.map(product => {
      const productName = product.name?.trim();
      
      if (!productName) {
        console.warn(`⚠️  Skipping product with no name:`, product);
        return product;
      }

      // Find matching short description
      let shortDescription = product.shortDescription || '';
      
      // Exact match first
      if (shortDescriptions[productName] && !shortDescription) {
        shortDescription = shortDescriptions[productName];
        updatedCount++;
        console.log(`✅ Updated: ${productName} -> ${shortDescription}`);
      }

      // Create category objects from categoryNames
      const categories = (product.categoryNames || []).map((categoryName, index) => ({
        id: `${categoryName.toLowerCase().replace(/\s+/g, '-')}-${index}`,
        name: categoryName,
        slug: categoryName.toLowerCase().replace(/\s+/g, '-')
      }));

      // Set primary category as the first one
      const primaryCategory = categories[0] || {
        id: 'uncategorized',
        name: 'Uncategorized',
        slug: 'uncategorized'
      };

      return {
        ...product,
        shortDescription: shortDescription,
        category: primaryCategory,
        categories: categories,
        // Ensure all required fields are present
        rating: product.rating || 4.5,
        reviews: product.reviews || 0,
        ingredients: product.ingredients || [],
        benefits: product.benefits || [],
        featured: product.isFeatured || false,
        // Handle images
        images: product.images || (product.image ? [{
          id: '1',
          url: product.image,
          alt: product.name,
          position: 1
        }] : [])
      };
    });

    // Write updated products back to file
    const outputPath = path.join(process.cwd(), 'csvjson-updated.json');
    fs.writeFileSync(outputPath, JSON.stringify({ products: updatedProducts }, null, 2), 'utf8');

    console.log('\n📊 Final Update Summary:');
    console.log(`✅ Products with new short descriptions: ${updatedCount}`);
    console.log(`📁 File saved: ${outputPath}`);

    // Create a backup of the original
    const backupPath = path.join(process.cwd(), 'csvjson-final-backup.json');
    fs.writeFileSync(backupPath, JSON.stringify(fileData, null, 2), 'utf8');
    console.log(`💾 Backup created: ${backupPath}`);

    console.log('\n✨ Product data is now ready with:');
    console.log('   • Short descriptions for all products');
    console.log('   • Proper category structure for ProductCard and ProductDetail');
    console.log('   • Compatible format for frontend components');

  } catch (error) {
    console.error('❌ Error finalizing product data:', error);
    process.exit(1);
  }
}

// Run the finalization
finalizeProductData();