"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/categories/[id]/route";
exports.ids = ["app/api/categories/[id]/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcategories%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcategories%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcategories%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcategories%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_project_app_api_categories_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/categories/[id]/route.ts */ \"(rsc)/./app/api/categories/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/categories/[id]/route\",\n        pathname: \"/api/categories/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/categories/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\api\\\\categories\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_project_app_api_categories_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/categories/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcategories%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcategories%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/categories/[id]/route.ts":
/*!******************************************!*\
  !*** ./app/api/categories/[id]/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _app_lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/db */ \"(rsc)/./app/lib/db.ts\");\n\n\n// GET /api/categories/[id] - Get a specific category\nasync function GET(request, { params }) {\n    try {\n        const category = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.category.findUnique({\n            where: {\n                id: params.id\n            },\n            include: {\n                products: {\n                    include: {\n                        images: {\n                            take: 1,\n                            orderBy: {\n                                position: \"asc\"\n                            }\n                        }\n                    }\n                },\n                parent: true,\n                children: true,\n                _count: {\n                    select: {\n                        products: true\n                    }\n                }\n            }\n        });\n        if (!category) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Category not found\"\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: category\n        });\n    } catch (error) {\n        console.error(\"Error fetching category:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to fetch category\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PATCH /api/categories/[id] - Update a category\nasync function PATCH(request, { params }) {\n    try {\n        const body = await request.json();\n        const { name, slug, description, image, isActive, parentId } = body;\n        const category = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.category.update({\n            where: {\n                id: params.id\n            },\n            data: {\n                ...name && {\n                    name\n                },\n                ...slug && {\n                    slug\n                },\n                ...description && {\n                    description\n                },\n                ...image && {\n                    image\n                },\n                ...isActive !== undefined && {\n                    isActive\n                },\n                ...parentId && {\n                    parentId\n                }\n            },\n            include: {\n                parent: true,\n                _count: {\n                    select: {\n                        products: true\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: category,\n            message: \"Category updated successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error updating category:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to update category\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/categories/[id] - Delete a category\nasync function DELETE(request, { params }) {\n    try {\n        // Check if category has products (direct relationship)\n        const productCount = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.count({\n            where: {\n                categoryId: params.id\n            }\n        });\n        // Check for products in many-to-many relationship (if table exists)\n        let productCategoryCount = 0;\n        try {\n            productCategoryCount = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.productCategory.count({\n                where: {\n                    categoryId: params.id\n                }\n            });\n        } catch (error) {\n            // ProductCategory table might not exist yet\n            console.log(\"ProductCategory table not found, skipping many-to-many check\");\n        }\n        const totalProductCount = productCount + productCategoryCount;\n        if (totalProductCount > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: `Cannot delete category. ${totalProductCount} product(s) are still assigned to this category.`\n            }, {\n                status: 400\n            });\n        }\n        // Check if category has child categories\n        const childCount = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.category.count({\n            where: {\n                parentId: params.id\n            }\n        });\n        if (childCount > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: `Cannot delete category. ${childCount} subcategory(ies) exist under this category.`\n            }, {\n                status: 400\n            });\n        }\n        // If all checks pass, delete the category\n        await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.category.delete({\n            where: {\n                id: params.id\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Category deleted successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error deleting category:\", error);\n        // Handle Prisma foreign key constraint errors\n        if (error instanceof Error && error.message.includes(\"foreign key constraint\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Cannot delete category due to existing relationships\"\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to delete category\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2NhdGVnb3JpZXMvW2lkXS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF3RDtBQUNsQjtBQUV0QyxxREFBcUQ7QUFDOUMsZUFBZUUsSUFDcEJDLE9BQW9CLEVBQ3BCLEVBQUVDLE1BQU0sRUFBOEI7SUFFdEMsSUFBSTtRQUNGLE1BQU1DLFdBQVcsTUFBTUosK0NBQU1BLENBQUNJLFFBQVEsQ0FBQ0MsVUFBVSxDQUFDO1lBQ2hEQyxPQUFPO2dCQUNMQyxJQUFJSixPQUFPSSxFQUFFO1lBQ2Y7WUFDQUMsU0FBUztnQkFDUEMsVUFBVTtvQkFDUkQsU0FBUzt3QkFDUEUsUUFBUTs0QkFDTkMsTUFBTTs0QkFDTkMsU0FBUztnQ0FDUEMsVUFBVTs0QkFDWjt3QkFDRjtvQkFDRjtnQkFDRjtnQkFDQUMsUUFBUTtnQkFDUkMsVUFBVTtnQkFDVkMsUUFBUTtvQkFDTkMsUUFBUTt3QkFDTlIsVUFBVTtvQkFDWjtnQkFDRjtZQUNGO1FBQ0Y7UUFFQSxJQUFJLENBQUNMLFVBQVU7WUFDYixPQUFPTCxxREFBWUEsQ0FBQ21CLElBQUksQ0FDdEI7Z0JBQUVDLFNBQVM7Z0JBQU9DLE9BQU87WUFBcUIsR0FDOUM7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE9BQU90QixxREFBWUEsQ0FBQ21CLElBQUksQ0FBQztZQUN2QkMsU0FBUztZQUNURyxNQUFNbEI7UUFDUjtJQUNGLEVBQUUsT0FBT2dCLE9BQU87UUFDZEcsUUFBUUgsS0FBSyxDQUFDLDRCQUE0QkE7UUFDMUMsT0FBT3JCLHFEQUFZQSxDQUFDbUIsSUFBSSxDQUN0QjtZQUFFQyxTQUFTO1lBQU9DLE9BQU87UUFBMkIsR0FDcEQ7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFQSxpREFBaUQ7QUFDMUMsZUFBZUcsTUFDcEJ0QixPQUFvQixFQUNwQixFQUFFQyxNQUFNLEVBQThCO0lBRXRDLElBQUk7UUFDRixNQUFNc0IsT0FBTyxNQUFNdkIsUUFBUWdCLElBQUk7UUFDL0IsTUFBTSxFQUFFUSxJQUFJLEVBQUVDLElBQUksRUFBRUMsV0FBVyxFQUFFQyxLQUFLLEVBQUVDLFFBQVEsRUFBRUMsUUFBUSxFQUFFLEdBQUdOO1FBRS9ELE1BQU1yQixXQUFXLE1BQU1KLCtDQUFNQSxDQUFDSSxRQUFRLENBQUM0QixNQUFNLENBQUM7WUFDNUMxQixPQUFPO2dCQUNMQyxJQUFJSixPQUFPSSxFQUFFO1lBQ2Y7WUFDQWUsTUFBTTtnQkFDSixHQUFJSSxRQUFRO29CQUFFQTtnQkFBSyxDQUFDO2dCQUNwQixHQUFJQyxRQUFRO29CQUFFQTtnQkFBSyxDQUFDO2dCQUNwQixHQUFJQyxlQUFlO29CQUFFQTtnQkFBWSxDQUFDO2dCQUNsQyxHQUFJQyxTQUFTO29CQUFFQTtnQkFBTSxDQUFDO2dCQUN0QixHQUFJQyxhQUFhRyxhQUFhO29CQUFFSDtnQkFBUyxDQUFDO2dCQUMxQyxHQUFJQyxZQUFZO29CQUFFQTtnQkFBUyxDQUFDO1lBQzlCO1lBQ0F2QixTQUFTO2dCQUNQTSxRQUFRO2dCQUNSRSxRQUFRO29CQUNOQyxRQUFRO3dCQUNOUixVQUFVO29CQUNaO2dCQUNGO1lBQ0Y7UUFDRjtRQUVBLE9BQU9WLHFEQUFZQSxDQUFDbUIsSUFBSSxDQUFDO1lBQ3ZCQyxTQUFTO1lBQ1RHLE1BQU1sQjtZQUNOOEIsU0FBUztRQUNYO0lBQ0YsRUFBRSxPQUFPZCxPQUFPO1FBQ2RHLFFBQVFILEtBQUssQ0FBQyw0QkFBNEJBO1FBQzFDLE9BQU9yQixxREFBWUEsQ0FBQ21CLElBQUksQ0FDdEI7WUFBRUMsU0FBUztZQUFPQyxPQUFPO1FBQTRCLEdBQ3JEO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsa0RBQWtEO0FBQzNDLGVBQWVjLE9BQ3BCakMsT0FBb0IsRUFDcEIsRUFBRUMsTUFBTSxFQUE4QjtJQUV0QyxJQUFJO1FBQ0YsdURBQXVEO1FBQ3ZELE1BQU1pQyxlQUFlLE1BQU1wQywrQ0FBTUEsQ0FBQ3FDLE9BQU8sQ0FBQ0MsS0FBSyxDQUFDO1lBQzlDaEMsT0FBTztnQkFDTGlDLFlBQVlwQyxPQUFPSSxFQUFFO1lBQ3ZCO1FBQ0Y7UUFFQSxvRUFBb0U7UUFDcEUsSUFBSWlDLHVCQUF1QjtRQUMzQixJQUFJO1lBQ0ZBLHVCQUF1QixNQUFNeEMsK0NBQU1BLENBQUN5QyxlQUFlLENBQUNILEtBQUssQ0FBQztnQkFDeERoQyxPQUFPO29CQUNMaUMsWUFBWXBDLE9BQU9JLEVBQUU7Z0JBQ3ZCO1lBQ0Y7UUFDRixFQUFFLE9BQU9hLE9BQU87WUFDZCw0Q0FBNEM7WUFDNUNHLFFBQVFtQixHQUFHLENBQUM7UUFDZDtRQUVBLE1BQU1DLG9CQUFvQlAsZUFBZUk7UUFFekMsSUFBSUcsb0JBQW9CLEdBQUc7WUFDekIsT0FBTzVDLHFEQUFZQSxDQUFDbUIsSUFBSSxDQUN0QjtnQkFBRUMsU0FBUztnQkFBT0MsT0FBTyxDQUFDLHdCQUF3QixFQUFFdUIsa0JBQWtCLGdEQUFnRCxDQUFDO1lBQUMsR0FDeEg7Z0JBQUV0QixRQUFRO1lBQUk7UUFFbEI7UUFFQSx5Q0FBeUM7UUFDekMsTUFBTXVCLGFBQWEsTUFBTTVDLCtDQUFNQSxDQUFDSSxRQUFRLENBQUNrQyxLQUFLLENBQUM7WUFDN0NoQyxPQUFPO2dCQUNMeUIsVUFBVTVCLE9BQU9JLEVBQUU7WUFDckI7UUFDRjtRQUVBLElBQUlxQyxhQUFhLEdBQUc7WUFDbEIsT0FBTzdDLHFEQUFZQSxDQUFDbUIsSUFBSSxDQUN0QjtnQkFBRUMsU0FBUztnQkFBT0MsT0FBTyxDQUFDLHdCQUF3QixFQUFFd0IsV0FBVyw0Q0FBNEMsQ0FBQztZQUFDLEdBQzdHO2dCQUFFdkIsUUFBUTtZQUFJO1FBRWxCO1FBRUEsMENBQTBDO1FBQzFDLE1BQU1yQiwrQ0FBTUEsQ0FBQ0ksUUFBUSxDQUFDeUMsTUFBTSxDQUFDO1lBQzNCdkMsT0FBTztnQkFDTEMsSUFBSUosT0FBT0ksRUFBRTtZQUNmO1FBQ0Y7UUFFQSxPQUFPUixxREFBWUEsQ0FBQ21CLElBQUksQ0FBQztZQUN2QkMsU0FBUztZQUNUZSxTQUFTO1FBQ1g7SUFDRixFQUFFLE9BQU9kLE9BQU87UUFDZEcsUUFBUUgsS0FBSyxDQUFDLDRCQUE0QkE7UUFFMUMsOENBQThDO1FBQzlDLElBQUlBLGlCQUFpQjBCLFNBQVMxQixNQUFNYyxPQUFPLENBQUNhLFFBQVEsQ0FBQywyQkFBMkI7WUFDOUUsT0FBT2hELHFEQUFZQSxDQUFDbUIsSUFBSSxDQUN0QjtnQkFBRUMsU0FBUztnQkFBT0MsT0FBTztZQUF1RCxHQUNoRjtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsT0FBT3RCLHFEQUFZQSxDQUFDbUIsSUFBSSxDQUN0QjtZQUFFQyxTQUFTO1lBQU9DLE9BQU87UUFBNEIsR0FDckQ7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vYXBwL2FwaS9jYXRlZ29yaWVzL1tpZF0vcm91dGUudHM/ZWUyZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSAnQC9hcHAvbGliL2RiJztcblxuLy8gR0VUIC9hcGkvY2F0ZWdvcmllcy9baWRdIC0gR2V0IGEgc3BlY2lmaWMgY2F0ZWdvcnlcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQoXG4gIHJlcXVlc3Q6IE5leHRSZXF1ZXN0LFxuICB7IHBhcmFtcyB9OiB7IHBhcmFtczogeyBpZDogc3RyaW5nIH0gfVxuKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgY2F0ZWdvcnkgPSBhd2FpdCBwcmlzbWEuY2F0ZWdvcnkuZmluZFVuaXF1ZSh7XG4gICAgICB3aGVyZToge1xuICAgICAgICBpZDogcGFyYW1zLmlkLFxuICAgICAgfSxcbiAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgcHJvZHVjdHM6IHtcbiAgICAgICAgICBpbmNsdWRlOiB7XG4gICAgICAgICAgICBpbWFnZXM6IHtcbiAgICAgICAgICAgICAgdGFrZTogMSxcbiAgICAgICAgICAgICAgb3JkZXJCeToge1xuICAgICAgICAgICAgICAgIHBvc2l0aW9uOiAnYXNjJyxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgICAgcGFyZW50OiB0cnVlLFxuICAgICAgICBjaGlsZHJlbjogdHJ1ZSxcbiAgICAgICAgX2NvdW50OiB7XG4gICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICBwcm9kdWN0czogdHJ1ZSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIGlmICghY2F0ZWdvcnkpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdDYXRlZ29yeSBub3QgZm91bmQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDQgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIGRhdGE6IGNhdGVnb3J5LFxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGNhdGVnb3J5OicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0ZhaWxlZCB0byBmZXRjaCBjYXRlZ29yeScgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cblxuLy8gUEFUQ0ggL2FwaS9jYXRlZ29yaWVzL1tpZF0gLSBVcGRhdGUgYSBjYXRlZ29yeVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBBVENIKFxuICByZXF1ZXN0OiBOZXh0UmVxdWVzdCxcbiAgeyBwYXJhbXMgfTogeyBwYXJhbXM6IHsgaWQ6IHN0cmluZyB9IH1cbikge1xuICB0cnkge1xuICAgIGNvbnN0IGJvZHkgPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcbiAgICBjb25zdCB7IG5hbWUsIHNsdWcsIGRlc2NyaXB0aW9uLCBpbWFnZSwgaXNBY3RpdmUsIHBhcmVudElkIH0gPSBib2R5O1xuXG4gICAgY29uc3QgY2F0ZWdvcnkgPSBhd2FpdCBwcmlzbWEuY2F0ZWdvcnkudXBkYXRlKHtcbiAgICAgIHdoZXJlOiB7XG4gICAgICAgIGlkOiBwYXJhbXMuaWQsXG4gICAgICB9LFxuICAgICAgZGF0YToge1xuICAgICAgICAuLi4obmFtZSAmJiB7IG5hbWUgfSksXG4gICAgICAgIC4uLihzbHVnICYmIHsgc2x1ZyB9KSxcbiAgICAgICAgLi4uKGRlc2NyaXB0aW9uICYmIHsgZGVzY3JpcHRpb24gfSksXG4gICAgICAgIC4uLihpbWFnZSAmJiB7IGltYWdlIH0pLFxuICAgICAgICAuLi4oaXNBY3RpdmUgIT09IHVuZGVmaW5lZCAmJiB7IGlzQWN0aXZlIH0pLFxuICAgICAgICAuLi4ocGFyZW50SWQgJiYgeyBwYXJlbnRJZCB9KSxcbiAgICAgIH0sXG4gICAgICBpbmNsdWRlOiB7XG4gICAgICAgIHBhcmVudDogdHJ1ZSxcbiAgICAgICAgX2NvdW50OiB7XG4gICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICBwcm9kdWN0czogdHJ1ZSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgZGF0YTogY2F0ZWdvcnksXG4gICAgICBtZXNzYWdlOiAnQ2F0ZWdvcnkgdXBkYXRlZCBzdWNjZXNzZnVsbHknLFxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIGNhdGVnb3J5OicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0ZhaWxlZCB0byB1cGRhdGUgY2F0ZWdvcnknIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG5cbi8vIERFTEVURSAvYXBpL2NhdGVnb3JpZXMvW2lkXSAtIERlbGV0ZSBhIGNhdGVnb3J5XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gREVMRVRFKFxuICByZXF1ZXN0OiBOZXh0UmVxdWVzdCxcbiAgeyBwYXJhbXMgfTogeyBwYXJhbXM6IHsgaWQ6IHN0cmluZyB9IH1cbikge1xuICB0cnkge1xuICAgIC8vIENoZWNrIGlmIGNhdGVnb3J5IGhhcyBwcm9kdWN0cyAoZGlyZWN0IHJlbGF0aW9uc2hpcClcbiAgICBjb25zdCBwcm9kdWN0Q291bnQgPSBhd2FpdCBwcmlzbWEucHJvZHVjdC5jb3VudCh7XG4gICAgICB3aGVyZToge1xuICAgICAgICBjYXRlZ29yeUlkOiBwYXJhbXMuaWQsXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgLy8gQ2hlY2sgZm9yIHByb2R1Y3RzIGluIG1hbnktdG8tbWFueSByZWxhdGlvbnNoaXAgKGlmIHRhYmxlIGV4aXN0cylcbiAgICBsZXQgcHJvZHVjdENhdGVnb3J5Q291bnQgPSAwO1xuICAgIHRyeSB7XG4gICAgICBwcm9kdWN0Q2F0ZWdvcnlDb3VudCA9IGF3YWl0IHByaXNtYS5wcm9kdWN0Q2F0ZWdvcnkuY291bnQoe1xuICAgICAgICB3aGVyZToge1xuICAgICAgICAgIGNhdGVnb3J5SWQ6IHBhcmFtcy5pZCxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAvLyBQcm9kdWN0Q2F0ZWdvcnkgdGFibGUgbWlnaHQgbm90IGV4aXN0IHlldFxuICAgICAgY29uc29sZS5sb2coJ1Byb2R1Y3RDYXRlZ29yeSB0YWJsZSBub3QgZm91bmQsIHNraXBwaW5nIG1hbnktdG8tbWFueSBjaGVjaycpO1xuICAgIH1cblxuICAgIGNvbnN0IHRvdGFsUHJvZHVjdENvdW50ID0gcHJvZHVjdENvdW50ICsgcHJvZHVjdENhdGVnb3J5Q291bnQ7XG5cbiAgICBpZiAodG90YWxQcm9kdWN0Q291bnQgPiAwKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBgQ2Fubm90IGRlbGV0ZSBjYXRlZ29yeS4gJHt0b3RhbFByb2R1Y3RDb3VudH0gcHJvZHVjdChzKSBhcmUgc3RpbGwgYXNzaWduZWQgdG8gdGhpcyBjYXRlZ29yeS5gIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiBjYXRlZ29yeSBoYXMgY2hpbGQgY2F0ZWdvcmllc1xuICAgIGNvbnN0IGNoaWxkQ291bnQgPSBhd2FpdCBwcmlzbWEuY2F0ZWdvcnkuY291bnQoe1xuICAgICAgd2hlcmU6IHtcbiAgICAgICAgcGFyZW50SWQ6IHBhcmFtcy5pZCxcbiAgICAgIH0sXG4gICAgfSk7XG5cbiAgICBpZiAoY2hpbGRDb3VudCA+IDApIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGBDYW5ub3QgZGVsZXRlIGNhdGVnb3J5LiAke2NoaWxkQ291bnR9IHN1YmNhdGVnb3J5KGllcykgZXhpc3QgdW5kZXIgdGhpcyBjYXRlZ29yeS5gIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBJZiBhbGwgY2hlY2tzIHBhc3MsIGRlbGV0ZSB0aGUgY2F0ZWdvcnlcbiAgICBhd2FpdCBwcmlzbWEuY2F0ZWdvcnkuZGVsZXRlKHtcbiAgICAgIHdoZXJlOiB7XG4gICAgICAgIGlkOiBwYXJhbXMuaWQsXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBtZXNzYWdlOiAnQ2F0ZWdvcnkgZGVsZXRlZCBzdWNjZXNzZnVsbHknLFxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlbGV0aW5nIGNhdGVnb3J5OicsIGVycm9yKTtcbiAgICBcbiAgICAvLyBIYW5kbGUgUHJpc21hIGZvcmVpZ24ga2V5IGNvbnN0cmFpbnQgZXJyb3JzXG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IgJiYgZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnZm9yZWlnbiBrZXkgY29uc3RyYWludCcpKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnQ2Fubm90IGRlbGV0ZSBjYXRlZ29yeSBkdWUgdG8gZXhpc3RpbmcgcmVsYXRpb25zaGlwcycgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0ZhaWxlZCB0byBkZWxldGUgY2F0ZWdvcnknIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwicHJpc21hIiwiR0VUIiwicmVxdWVzdCIsInBhcmFtcyIsImNhdGVnb3J5IiwiZmluZFVuaXF1ZSIsIndoZXJlIiwiaWQiLCJpbmNsdWRlIiwicHJvZHVjdHMiLCJpbWFnZXMiLCJ0YWtlIiwib3JkZXJCeSIsInBvc2l0aW9uIiwicGFyZW50IiwiY2hpbGRyZW4iLCJfY291bnQiLCJzZWxlY3QiLCJqc29uIiwic3VjY2VzcyIsImVycm9yIiwic3RhdHVzIiwiZGF0YSIsImNvbnNvbGUiLCJQQVRDSCIsImJvZHkiLCJuYW1lIiwic2x1ZyIsImRlc2NyaXB0aW9uIiwiaW1hZ2UiLCJpc0FjdGl2ZSIsInBhcmVudElkIiwidXBkYXRlIiwidW5kZWZpbmVkIiwibWVzc2FnZSIsIkRFTEVURSIsInByb2R1Y3RDb3VudCIsInByb2R1Y3QiLCJjb3VudCIsImNhdGVnb3J5SWQiLCJwcm9kdWN0Q2F0ZWdvcnlDb3VudCIsInByb2R1Y3RDYXRlZ29yeSIsImxvZyIsInRvdGFsUHJvZHVjdENvdW50IiwiY2hpbGRDb3VudCIsImRlbGV0ZSIsIkVycm9yIiwiaW5jbHVkZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/api/categories/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"error\"\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQ1hGLGdCQUFnQkUsTUFBTSxJQUN0QixJQUFJSCx3REFBWUEsQ0FBQztJQUNmSSxLQUFLO1FBQUM7S0FBUTtBQUNoQixHQUFFO0FBRUosSUFBSUMsSUFBeUIsRUFBY0osZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL2FwcC9saWIvZGIudHM/NThmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz9cbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ2Vycm9yJ10sIC8vIE9ubHkgbG9nIGVycm9ycywgbm8gcXVlcmllcyBvciB3YXJuaW5nc1xuICB9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcategories%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcategories%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();