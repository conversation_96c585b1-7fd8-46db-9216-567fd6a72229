globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/admin/page.tsx":{"*":{"id":"(ssr)/./app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/layout.tsx":{"*":{"id":"(ssr)/./app/admin/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/context/CartContext.tsx":{"*":{"id":"(ssr)/./app/context/CartContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/context/FlashSaleContext.tsx":{"*":{"id":"(ssr)/./app/context/FlashSaleContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/context/NotificationContext.tsx":{"*":{"id":"(ssr)/./app/context/NotificationContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/context/SessionProvider.tsx":{"*":{"id":"(ssr)/./app/context/SessionProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/categories/page.tsx":{"*":{"id":"(ssr)/./app/admin/categories/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/products/page.tsx":{"*":{"id":"(ssr)/./app/admin/products/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/Layout.tsx":{"*":{"id":"(ssr)/./app/components/Layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/pages/Home.tsx":{"*":{"id":"(ssr)/./app/components/pages/Home.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/pages/Shop.tsx":{"*":{"id":"(ssr)/./app/components/pages/Shop.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/pages/ProductDetail.tsx":{"*":{"id":"(ssr)/./app/components/pages/ProductDetail.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/pages/Profile.tsx":{"*":{"id":"(ssr)/./app/components/pages/Profile.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/homepage/page.tsx":{"*":{"id":"(ssr)/./app/admin/homepage/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\page.tsx":{"id":"(app-pages-browser)/./app/admin/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\layout.tsx":{"id":"(app-pages-browser)/./app/admin/layout.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\app\\context\\CartContext.tsx":{"id":"(app-pages-browser)/./app/context/CartContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\app\\context\\FlashSaleContext.tsx":{"id":"(app-pages-browser)/./app/context/FlashSaleContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\app\\context\\NotificationContext.tsx":{"id":"(app-pages-browser)/./app/context/NotificationContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\app\\context\\SessionProvider.tsx":{"id":"(app-pages-browser)/./app/context/SessionProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\categories\\page.tsx":{"id":"(app-pages-browser)/./app/admin/categories/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\products\\page.tsx":{"id":"(app-pages-browser)/./app/admin/products/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\app\\components\\Layout.tsx":{"id":"(app-pages-browser)/./app/components/Layout.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Home.tsx":{"id":"(app-pages-browser)/./app/components/pages/Home.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Shop.tsx":{"id":"(app-pages-browser)/./app/components/pages/Shop.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\ProductDetail.tsx":{"id":"(app-pages-browser)/./app/components/pages/ProductDetail.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\app\\components\\pages\\Profile.tsx":{"id":"(app-pages-browser)/./app/components/pages/Profile.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\project\\app\\admin\\homepage\\page.tsx":{"id":"(app-pages-browser)/./app/admin/homepage/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\project\\":[],"C:\\Users\\<USER>\\Desktop\\project\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Desktop\\project\\app\\page":[]}}