import { NextRequest, NextResponse } from 'next/server';
import { logger } from '../../../lib/logger';
import { asyncHandler } from '../../../lib/errors';

export const GET = asyncHandler(async (request: NextRequest) => {
  logger.apiRequest('GET', '/api/payments/config');

  try {
    // Check if required environment variables are set
    const requiredEnvVars = [
      'RAZORPAY_KEY_ID',
      'RAZORPAY_KEY_SECRET',
      'NEXT_PUBLIC_RAZORPAY_KEY_ID'
    ];

    const envStatus = requiredEnvVars.reduce((acc, varName) => {
      acc[varName] = !!process.env[varName];
      return acc;
    }, {} as Record<string, boolean>);

    const allConfigured = Object.values(envStatus).every(Boolean);

    return NextResponse.json({
      success: true,
      configured: allConfigured,
      environment_variables: envStatus,
      public_key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID || null,
      environment: process.env.NODE_ENV,
      message: allConfigured 
        ? 'Razorpay payment integration is properly configured' 
        : 'Some environment variables are missing'
    });

  } catch (error) {
    logger.error('Payment config check failed', error as Error);
    
    return NextResponse.json({
      success: false,
      configured: false,
      error: 'Failed to check payment configuration',
      details: (error as Error).message
    }, { status: 500 });
  }
});