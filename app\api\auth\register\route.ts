import { NextRequest, NextResponse } from 'next/server';
import * as bcrypt from 'bcryptjs';
import { z } from 'zod';
import { prisma } from '@/app/lib/db';
import { authLimiter, withRateLimit } from '@/app/lib/rate-limit';
import { sendWelcomeEmail } from '@/app/lib/email';
import { handleApiError, ConflictError, asyncHandler } from '@/app/lib/errors';
import { logger } from '@/app/lib/logger';

const registerSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
});

export const POST = asyncHandler(async (request: NextRequest) => {
  const startTime = Date.now();
  logger.apiRequest('POST', '/api/auth/register');

  // Apply rate limiting (5 registration attempts per 15 minutes per IP)
  await withRateLimit(request, authLimiter, 5);

  const body = await request.json();
  
  // Validate input
  const validatedData = registerSchema.parse(body);
  const { name, email, password } = validatedData;

  logger.info('Registration attempt', { email, name });

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email }
  });

  if (existingUser) {
    logger.authFailure(email, 'registration', 'User already exists');
    throw new ConflictError('User with this email already exists');
  }

  // Hash password
  const saltRounds = 12;
  const hashedPassword = await bcrypt.hash(password, saltRounds);

  // Create user
  const user = await prisma.user.create({
    data: {
      name,
      email,
      password: hashedPassword,
      role: 'CUSTOMER', // Default role
    },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      createdAt: true,
    }
  });

  logger.authSuccess(user.id, 'registration', { email, name });

  // Send welcome email (don't block registration if email fails)
  try {
    await sendWelcomeEmail(email, name);
    logger.emailSent(email, 'Welcome Email', 'welcome');
  } catch (emailError) {
    logger.emailError(email, 'Welcome Email', emailError as Error);
  }

  const duration = Date.now() - startTime;
  logger.performance('user_registration', duration);

  return NextResponse.json(
    {
      success: true,
      message: 'User created successfully',
      user
    },
    { status: 201 }
  );
});