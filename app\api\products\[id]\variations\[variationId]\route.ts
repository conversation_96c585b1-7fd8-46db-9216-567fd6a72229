import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../../../lib/auth';
import { prisma } from '../../../../../lib/db';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; variationId: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: productId, variationId } = params;
    const body = await request.json();
    const { name, value, price, pricingMode } = body;

    // Validate required fields
    if (!name || !value) {
      return NextResponse.json(
        { error: 'Name and value are required' },
        { status: 400 }
      );
    }

    // Validate pricing mode
    const validPricingModes: Array<'REPLACE' | 'INCREMENT' | 'FIXED'> = ['REPLACE', 'INCREMENT', 'FIXED'];
    const validatedPricingMode = pricingMode && validPricingModes.includes(pricingMode)
      ? pricingMode as 'REPLACE' | 'INCREMENT' | 'FIXED'
      : 'INCREMENT';

    // Check if variation exists
    const existingVariation = await prisma.productVariant.findUnique({
      where: { id: variationId }
    });

    if (!existingVariation) {
      return NextResponse.json(
        { error: 'Variation not found' },
        { status: 404 }
      );
    }

    // Check if variation belongs to the specified product
    if (existingVariation.productId !== productId) {
      return NextResponse.json(
        { error: 'Variation does not belong to this product' },
        { status: 400 }
      );
    }

    // Check if another variation with same name and value already exists (excluding current one)
    const duplicateVariation = await prisma.productVariant.findFirst({
      where: {
        productId: productId,
        name: name,
        value: value,
        id: { not: variationId }
      }
    });

    if (duplicateVariation) {
      return NextResponse.json(
        { error: 'Another variation with this name and value already exists' },
        { status: 400 }
      );
    }

    // Parse and validate price
    let parsedPrice: number | null = null;
    if (price !== undefined && price !== null && price !== '') {
      const numPrice = typeof price === 'string' ? parseFloat(price) : Number(price);
      if (!isNaN(numPrice) && isFinite(numPrice)) {
        parsedPrice = numPrice;
      }
    }

    // Update the variation
    const updateData: any = {
      name: name.trim(),
      value: value.trim(),
      price: parsedPrice,
      pricingMode: validatedPricingMode
    };

    const updatedVariation = await prisma.productVariant.update({
      where: { id: variationId },
      data: updateData
    });

    return NextResponse.json({
      success: true,
      data: updatedVariation,
      message: 'Variation updated successfully'
    });

  } catch (error) {
    console.error('Error updating variation:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update variation' 
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; variationId: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: productId, variationId } = params;

    // Check if variation exists
    const existingVariation = await prisma.productVariant.findUnique({
      where: { id: variationId }
    });

    if (!existingVariation) {
      return NextResponse.json(
        { error: 'Variation not found' },
        { status: 404 }
      );
    }

    // Check if variation belongs to the specified product
    if (existingVariation.productId !== productId) {
      return NextResponse.json(
        { error: 'Variation does not belong to this product' },
        { status: 400 }
      );
    }

    // Delete the variation
    await prisma.productVariant.delete({
      where: { id: variationId }
    });

    return NextResponse.json({
      success: true,
      message: 'Variation deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting variation:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete variation' 
      },
      { status: 500 }
    );
  }
}
