import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/lib/auth';

// GET /api/users/[id]/stats - Get user statistics
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated and accessing their own data or is admin
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const isAdmin = (session.user as any)?.role === 'ADMIN';
    const isOwnProfile = session.user.id === params.id;

    if (!isAdmin && !isOwnProfile) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get user with basic info
    const user = await prisma.user.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        name: true,
        email: true,
        createdAt: true,
        role: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Get order statistics
    const orderStats = await prisma.order.aggregate({
      where: {
        userId: params.id,
        paymentStatus: 'PAID', // Only count paid orders
      },
      _count: {
        id: true,
      },
      _sum: {
        total: true,
      },
    });

    // Get additional counts
    const [reviewCount, wishlistCount, addressCount] = await Promise.all([
      prisma.review.count({
        where: { userId: params.id },
      }),
      prisma.wishlistItem.count({
        where: { userId: params.id },
      }),
      prisma.address.count({
        where: { userId: params.id },
      }),
    ]);

    // Get recent orders
    const recentOrders = await prisma.order.findMany({
      where: { userId: params.id },
      orderBy: { createdAt: 'desc' },
      take: 5,
      select: {
        id: true,
        orderNumber: true,
        status: true,
        total: true,
        createdAt: true,
      },
    });

    const stats = {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        joinDate: user.createdAt,
        role: user.role,
      },
      orders: {
        total: orderStats._count.id || 0,
        totalSpent: orderStats._sum.total || 0,
        recent: recentOrders,
      },
      counts: {
        reviews: reviewCount,
        wishlist: wishlistCount,
        addresses: addressCount,
      },
      accountStatus: 'ACTIVE', // Could be enhanced with more logic
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('Error fetching user stats:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user statistics' },
      { status: 500 }
    );
  }
}