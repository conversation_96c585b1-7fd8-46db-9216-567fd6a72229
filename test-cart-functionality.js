// Test script to verify cart functionality
// Run this in browser console when the app is running

// Test 1: Add different variants of the same product
console.log('=== Testing Cart Functionality ===');

// Mock product data
const testProduct = {
  id: 'test-product-1',
  name: 'Test Herbal Product',
  price: 100,
  shortDescription: 'Test product for cart functionality',
  category: 'skincare',
  featured: false,
  ingredients: ['Test ingredient'],
  benefits: ['Test benefit'],
  rating: 4.5,
  reviews: 10
};

// Test adding different variants
console.log('Test 1: Adding different variants...');

// Add first variant
window.cartTest = {
  addItem: (product, variants) => {
    const { useCart } = require('./app/context/CartContext');
    const { dispatch } = useCart();
    
    dispatch({
      type: 'ADD_ITEM',
      payload: product,
      selectedVariants: variants
    });
  },
  
  removeItem: (variantKey) => {
    const { useCart } = require('./app/context/CartContext');
    const { dispatch } = useCart();
    
    dispatch({
      type: 'REMOVE_ITEM',
      payload: variantKey
    });
  },
  
  getCart: () => {
    const { useCart } = require('./app/context/CartContext');
    const { state } = useCart();
    return state;
  }
};

console.log('Cart functionality test setup complete');
console.log('To test manually:');
console.log('1. Add a product with variant A');
console.log('2. Add same product with variant B');
console.log('3. Verify they appear as separate items');
console.log('4. Test delete functionality for each variant');