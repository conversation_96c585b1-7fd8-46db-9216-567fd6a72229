import { Suspense } from 'react'
import Layout from '../components/Layout'
import OrderConfirmation from '../components/pages/OrderConfirmation'

// Force dynamic rendering for this page since it uses search params
export const dynamic = 'force-dynamic'

function OrderConfirmationContent() {
  return <OrderConfirmation />
}

export default function OrderConfirmationPage() {
  return (
    <Layout>
      <Suspense fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">Loading order details...</p>
          </div>
        </div>
      }>
        <OrderConfirmationContent />
      </Suspense>
    </Layout>
  )
}