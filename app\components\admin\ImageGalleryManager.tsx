'use client';

import React, { useState } from 'react';
import { Plus, X, Upload, Move, Eye } from 'lucide-react';
import ImageSelector from './ImageSelector';

interface ProductImage {
  id?: string;
  url: string;
  alt: string;
  position: number;
}

interface ImageGalleryManagerProps {
  images: ProductImage[];
  onChange: (images: ProductImage[]) => void;
  productName?: string;
}

const ImageGalleryManager: React.FC<ImageGalleryManagerProps> = ({
  images,
  onChange,
  productName = 'Product'
}) => {
  const [showImageSelector, setShowImageSelector] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

  const addImage = (url: string) => {
    const newImage: ProductImage = {
      url,
      alt: `${productName} - Image ${images.length + 1}`,
      position: images.length
    };
    onChange([...images, newImage]);
    setShowImageSelector(false);
  };

  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    // Reorder positions
    const reorderedImages = newImages.map((img, i) => ({
      ...img,
      position: i
    }));
    onChange(reorderedImages);
  };

  const updateImageAlt = (index: number, alt: string) => {
    const newImages = [...images];
    newImages[index] = { ...newImages[index], alt };
    onChange(newImages);
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    if (fromIndex === toIndex) return;
    
    const newImages = [...images];
    const [movedImage] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, movedImage);
    
    // Update positions
    const reorderedImages = newImages.map((img, i) => ({
      ...img,
      position: i
    }));
    
    onChange(reorderedImages);
  };

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    if (draggedIndex !== null) {
      moveImage(draggedIndex, dropIndex);
      setDraggedIndex(null);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Product Images</h3>
        <button
          type="button"
          onClick={() => setShowImageSelector(true)}
          className="bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 text-sm"
        >
          <Plus className="w-4 h-4" />
          <span>Add Image</span>
        </button>
      </div>

      {images.length === 0 ? (
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 mb-4">No images added yet</p>
          <button
            type="button"
            onClick={() => setShowImageSelector(true)}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
          >
            Add First Image
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {images.map((image, index) => (
            <div
              key={index}
              draggable
              onDragStart={(e) => handleDragStart(e, index)}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, index)}
              className={`relative group border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md transition-shadow ${
                draggedIndex === index ? 'opacity-50' : ''
              }`}
            >
              {/* Position Badge */}
              <div className="absolute top-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded z-10">
                {index === 0 ? 'Featured' : `#${index + 1}`}
              </div>

              {/* Move Handle */}
              <div className="absolute top-2 right-2 bg-black bg-opacity-75 text-white p-1 rounded cursor-move opacity-0 group-hover:opacity-100 transition-opacity z-10">
                <Move className="w-4 h-4" />
              </div>

              {/* Image */}
              <div className="aspect-square relative">
                <img
                  src={image.url}
                  alt={image.alt}
                  className="w-full h-full object-cover"
                />
                
                {/* Overlay Actions */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-2">
                    <button
                      type="button"
                      onClick={() => window.open(image.url, '_blank')}
                      className="bg-white text-gray-700 p-2 rounded-full hover:bg-gray-100 transition-colors"
                      title="View full size"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    <button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="bg-red-600 text-white p-2 rounded-full hover:bg-red-700 transition-colors"
                      title="Remove image"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Alt Text Input */}
              <div className="p-3">
                <input
                  type="text"
                  value={image.alt}
                  onChange={(e) => updateImageAlt(index, e.target.value)}
                  placeholder="Image description (alt text)"
                  className="w-full text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-green-500"
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Image Selector Modal */}
      {showImageSelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl">
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-semibold">Select Image</h3>
              <button
                onClick={() => setShowImageSelector(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <div className="p-4">
              <ImageSelector
                value=""
                onChange={addImage}
                label=""
                placeholder="Select or enter image URL"
              />
            </div>
          </div>
        </div>
      )}

      {/* Instructions */}
      {images.length > 0 && (
        <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
          <p className="font-medium mb-1">Tips:</p>
          <ul className="list-disc list-inside space-y-1">
            <li>The first image will be used as the featured image</li>
            <li>Drag and drop images to reorder them</li>
            <li>Add descriptive alt text for better SEO and accessibility</li>
            <li>Recommended image size: 800x800px or larger</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default ImageGalleryManager;
