import DOMPurify from 'isomorphic-dompurify';

// Configure DOMPurify for different contexts
const defaultConfig = {
  ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'br', 'ul', 'ol', 'li'],
  ALLOWED_ATTR: ['href', 'target', 'rel'],
  ALLOW_DATA_ATTR: false,
  ALLOWED_URI_REGEXP: /^(?:(?:https?|mailto):|[^a-z]|[a-z+.-]+(?:[^a-z+.\-:]|$))/i,
};

const strictConfig = {
  ALLOWED_TAGS: [],
  ALLOWED_ATTR: [],
  ALLOW_DATA_ATTR: false,
};

/**
 * Sanitize HTML content to prevent XSS attacks
 * @param dirty - The potentially dangerous HTML string
 * @param options - Optional configuration for DOMPurify
 * @returns Sanitized HTML string
 */
export function sanitizeHtml(dirty: string, options = defaultConfig): string {
  if (!dirty) return '';
  
  // Additional pre-processing to catch common XSS patterns
  let preprocessed = dirty
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '') // Remove script tags
    .replace(/<iframe[^>]*>[\s\S]*?<\/iframe>/gi, ''); // Remove iframes
  
  return DOMPurify.sanitize(preprocessed, options);
}

/**
 * Sanitize plain text (no HTML allowed)
 * @param dirty - The potentially dangerous string
 * @returns Sanitized plain text string
 */
export function sanitizeText(dirty: string): string {
  if (!dirty) return '';
  
  // Use strict config to remove all HTML
  const cleaned = DOMPurify.sanitize(dirty, strictConfig);
  
  // Additional cleaning for plain text
  return cleaned
    .replace(/[<>]/g, '') // Remove any remaining angle brackets
    .trim();
}

/**
 * Sanitize user input for database storage
 * @param input - User input object
 * @returns Sanitized input object
 */
export function sanitizeUserInput<T extends Record<string, any>>(input: T): T {
  const sanitized = {} as T;
  
  for (const [key, value] of Object.entries(input)) {
    if (typeof value === 'string') {
      // For most fields, use text sanitization
      if (key === 'content' || key === 'description' || key === 'bio') {
        // Allow limited HTML for content fields
        sanitized[key as keyof T] = sanitizeHtml(value) as T[keyof T];
      } else {
        // Plain text for other fields
        sanitized[key as keyof T] = sanitizeText(value) as T[keyof T];
      }
    } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      // Recursively sanitize nested objects
      sanitized[key as keyof T] = sanitizeUserInput(value) as T[keyof T];
    } else {
      // Keep other types as-is (numbers, booleans, arrays, etc.)
      sanitized[key as keyof T] = value;
    }
  }
  
  return sanitized;
}

/**
 * Escape HTML entities for safe display
 * @param str - String to escape
 * @returns Escaped string
 */
export function escapeHtml(str: string): string {
  const htmlEntities: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;',
    '/': '&#x2F;',
  };
  
  return String(str).replace(/[&<>"'\/]/g, (s) => htmlEntities[s]);
}

/**
 * Validate and sanitize URL
 * @param url - URL to validate
 * @returns Sanitized URL or empty string if invalid
 */
export function sanitizeUrl(url: string): string {
  if (!url) return '';
  
  try {
    const parsed = new URL(url);
    
    // Only allow http(s) and mailto protocols
    if (!['http:', 'https:', 'mailto:'].includes(parsed.protocol)) {
      return '';
    }
    
    // Prevent javascript: and data: URLs
    if (url.toLowerCase().includes('javascript:') || url.toLowerCase().includes('data:')) {
      return '';
    }
    
    return url;
  } catch {
    // If URL parsing fails, check if it's a relative URL
    if (url.startsWith('/') && !url.includes('//')) {
      return url;
    }
    return '';
  }
}

/**
 * Sanitize filename to prevent directory traversal
 * @param filename - Original filename
 * @returns Sanitized filename
 */
export function sanitizeFilename(filename: string): string {
  if (!filename) return '';
  
  return filename
    .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace special chars with underscore
    .replace(/\.{2,}/g, '.') // Replace multiple dots with single dot
    .replace(/^\.+|\.+$/g, '') // Remove leading/trailing dots
    .toLowerCase();
}