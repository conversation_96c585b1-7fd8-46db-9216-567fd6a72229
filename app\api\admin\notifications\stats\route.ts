import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../../lib/auth';
import { prisma } from '../../../../lib/db';
import { logger } from '../../../../lib/logger';

/**
 * GET /api/admin/notifications/stats
 * Get notification statistics for admin dashboard
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Only allow admin users
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 401 });
    }

    logger.info('Fetching notification statistics for admin dashboard');

    // Get date range for recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [
      totalSent,
      totalDelivered,
      totalRead,
      recentActivity,
    ] = await Promise.all([
      // Total notifications sent
      prisma.notification.count(),
      
      // Total notifications delivered (email sent successfully)
      prisma.notification.count({
        where: {
          emailSent: true,
        },
      }),
      
      // Total notifications read
      prisma.notification.count({
        where: {
          isRead: true,
        },
      }),
      
      // Recent activity (last 30 days)
      prisma.notification.count({
        where: {
          createdAt: {
            gte: thirtyDaysAgo,
          },
        },
      }),
    ]);

    // Calculate rates
    const deliveryRate = totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0;
    const readRate = totalDelivered > 0 ? (totalRead / totalDelivered) * 100 : 0;

    const stats = {
      totalSent,
      totalDelivered,
      totalRead,
      deliveryRate,
      readRate,
      recentActivity,
    };

    logger.info('Notification statistics retrieved successfully', {
      totalSent,
      totalDelivered,
      totalRead,
      deliveryRate: deliveryRate.toFixed(1),
      readRate: readRate.toFixed(1),
      recentActivity,
    });

    return NextResponse.json({
      success: true,
      stats,
    });

  } catch (error) {
    logger.error('Failed to fetch notification statistics', error as Error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
