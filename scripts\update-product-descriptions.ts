import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Product descriptions mapping
const productDescriptions = [
  {
    name: "Anti-Pimple Pack",
    description: "Bid farewell to acne with Herbalicious Anti-Pimple Pack! Enriched with neem, multani mitti, and natural herbs, this powerful formula fights acne-causing bacteria, reduces inflammation, and heals scars. Our organic blend helps you achieve fresh, clear skin naturally. Perfect for those seeking a natural solution to acne woes."
  },
  {
    name: "Anti Ageing Gel Creme",
    description: "Turn back the clock with Herbalicious Anti Ageing Gel Creme! Our potent hydrating formula deeply nourishes your skin while fighting signs of aging. This organic gel smooths fine lines, prevents wrinkles, fades dark spots, and promotes firm, youthful, glowing skin. Embrace ageless beauty with our natural anti-aging solution."
  },
  {
    name: "Baby Hair Oil",
    description: "Care for your little one's delicate hair with Herbalicious Baby Hair Oil! Our gentle blend of organic coconut oil, almond oil, and castor oil nourishes baby's scalp and promotes healthy hair growth. This natural formula keeps hair soft, shiny, and manageable. Perfect for your baby's sensitive scalp and delicate hair needs."
  },
  {
    name: "Beard Oil",
    description: "Elevate your beard game with Herbalicious Beard Oil! Specially formulated for men, our nutrient-rich oil promotes thick, full beard growth while making it manageable and easy to groom. Just a few drops of our organic blend stimulates new hair follicles for a healthier, more impressive beard."
  },
  {
    name: "Body Polishing & Glow Massage Oil",
    description: "Transform your skincare routine into a spa experience with Herbalicious Body Polishing & Glow Massage Oil! Our rich blend of herbs and oils detoxifies and polishes your skin while imparting a lovely glow and delightful softness. Indulge in this organic formula to pamper your body and achieve radiant skin at home."
  },
  {
    name: "Bridal Face Ubtan",
    description: "Embrace timeless beauty with Herbalicious Bridal Face Ubtan! Our traditional blend of freshly air-dried and powdered pure herbs pampers your skin for your special day. Regular use of this organic ubtan helps ward off acne, blackheads, and blemishes, revealing clearer, fairer skin. Perfect for brides-to-be and anyone seeking naturally beautiful skin."
  },
  {
    name: "Chiksa",
    description: "Experience the ancient beauty secret with Herbalicious Chiksa! This traditional remedy, beloved by brides for generations, powerfully combines sandalwood, turmeric, and rose petal powder to create glowing, fragrant, and supple skin. Our organic Chiksa nourishes and smooths your skin, revealing its natural radiance."
  },
  {
    name: "Deep Clean & Polishing Face Scrub",
    description: "Unveil radiant skin with Herbalicious Deep Clean & Polishing Face Scrub! Our blend of pure, hand-milled organic ingredients gently sloughs away dead skin cells and impurities. This natural scrub reveals healthy, soft skin and helps you achieve a natural glow. Perfect for your regular skincare routine."
  },
  {
    name: "Eyebrow Thickening & Lash Lengthening Oil",
    description: "Enhance your natural beauty with Herbalicious Eyebrow Thickening & Lash Lengthening Oil! Our organic formula strengthens and promotes the growth of your lashes and brows, making them thicker, fuller, and longer. This nourishing oil also imparts a beautiful luster and shine to frame your eyes perfectly."
  },
  {
    name: "Face Elixir Oil",
    description: "Transform your skincare routine with Herbalicious Face Elixir Oil! This powerhouse organic product prepares your skin for smooth makeup application and doubles as a nighttime skin repair system. Our fabulous blend of oils nourishes your skin, leaving it dewy with a flawless appearance. A must-have for radiant, healthy skin."
  },
  {
    name: "Face Ubtan",
    description: "Discover the ancient beauty secret with Herbalicious Face Ubtan! Used for centuries in Indian bridal traditions, our organic ubtan is a must-have for clear, smooth skin free of blemishes, acne, and blackheads. Embrace this time-tested formula to reveal your skin's natural radiance and achieve a flawless complexion."
  },
  {
    name: "Glitter Lip Balm",
    description: "Add sparkle to your smile with Herbalicious Glitter Lip Balm! Our hydrating organic formula soothes and deeply moisturizes your lips, making them appear supple and full. This glittery balm prevents dryness by helping skin cells retain hydration longer. Shine bright with our nourishing lip treatment!"
  },
  {
    name: "Gold Dust Skin Tonic",
    description: "Refresh and illuminate your skin with Herbalicious Gold Dust Skin Tonic! Infused with nourishing acai berry, aloe vera, rose, chamomile, Vitamins C and E, and 24k gold dust, our organic mist provides instant hydration while imparting a healthy shine. A quick spritz for radiant, glowing skin anytime!"
  },
  {
    name: "Hair Growth & Shine Mask",
    description: "Revitalize your hair with Herbalicious Hair Growth & Shine Mask! Our powerful organic blend of freshly ground Triphala, Shikakai, Reetha, Amla, Aloe Vera, and secret ingredients fights dandruff, promotes hair growth, and imparts a healthy shine. Transform your hair care routine for thick, lustrous locks!"
  },
  {
    name: "Kumkumadi Face Oil",
    description: "Experience the magic of Ayurveda with Herbalicious Kumkumadi Face Oil! This wonderful herbal mixture improves skin health and treats various skin conditions. Our organic oil brightens skin, reduces wrinkles and fine lines, fades dark circles, spots, and blemishes. Suitable for all skin types, especially sensitive or dry skin."
  },
  {
    name: "Luxury Moisturizing Milk Cream",
    description: "Nourish and hydrate your skin with Herbalicious Luxury Moisturizing Milk Cream! Our rich organic formulation treats dry skin conditions, including inflamed and irritated skin. Enriched with argan oil, it provides glow and hydration, leaving your skin softer and more moisturized. Embrace the luxury of naturally beautiful skin!"
  },
  {
    name: "Maharani Hair Oil",
    description: "Restore and revitalize your hair with Herbalicious Maharani Hair Oil! Our potent organic blend of coconut oil, onion, reetha, shikakai, vetiver roots, kalonji, and amla deeply moisturizes dry hair, tames frizz, and promotes healthy growth. Strengthen your hair shafts and reduce hair fall with this nourishing treatment."
  },
  {
    name: "Neem Face Wash",
    description: "Purify your skin with Herbalicious Neem Face Wash! Our organic formula, enriched with neem extract, fights bacterial diseases while rehydrating your skin. This antibacterial face wash eliminates excess oil and pollutants, unclogs pores, and removes dirt and pollution. Reveal clearer, healthier skin with each use!"
  },
  {
    name: "Papaya Face Wash",
    description: "Refresh your complexion with Herbalicious Papaya Face Wash! Packed with antioxidants, vitamins, and minerals, our organic face wash creates blemish-free, fresh-looking skin. It eliminates excess oil, clears pigmentation, unclogs pores, and removes dirt and pollution. Reveal your skin's natural radiance!"
  },
  {
    name: "Royal Facial Kit",
    description: "Give your skin royal treatment with Herbalicious Royal Facial Kit! Our complete organic facial system includes Whitening Scrub, Face Ubtan, and Saffron Gel. The scrub exfoliates, the ubtan nourishes and lightens, while the saffron gel hydrates and brightens. Experience the luxury of spa-quality facial treatment at home!"
  },
  {
    name: "Saffron Gel",
    description: "Illuminate your complexion with Herbalicious Saffron Gel! Our lightweight organic formula, enriched with the benefits of saffron, easily absorbs into the skin to brighten your complexion. This gentle gel tackles acne while keeping your skin hydrated. Reveal a more radiant, luminous you with each application!"
  },
  {
    name: "Saffron Night Repair Cream",
    description: "Transform your skin overnight with Herbalicious Saffron Night Repair Cream! Our potent organic blend of almond extract, saffron extract, and vitamins A, C & E revitalizes and renews your skin while you sleep. Reduce tan, pigmentation, acne, and premature aging. Wake up to brighter, more youthful, radiant skin!"
  },
  {
    name: "Night Care Combo",
    description: "Experience the ultimate nighttime skincare duo with Herbalicious Saffron Night Repair Cream & 24K Gold Elixir Oil! Our organic Saffron Cream combats skin concerns like tan, pigmentation, and acne, while the 24K Gold Elixir Oil restores skin's elasticity, reduces wrinkles, and adds enviable shine. Wake up to rejuvenated, glowing skin!"
  },
  {
    name: "Snow Face Cleanser",
    description: "Gently cleanse your skin with Herbalicious Snow Face Cleanser! Our non-foaming organic formula, enriched with black seed oil, neem oil, and tea tree oil, lifts away impurities without drying your skin. This light and fresh cleanser leaves your skin soft and supple to the touch. Experience pure, gentle cleansing for radiant skin!"
  },
  {
    name: "Strawberry Silk Booster",
    description: "Elevate your skincare routine with Herbalicious Strawberry Silk Booster! Our organic formula combines the goodness of strawberries with silk proteins to nourish and revitalize your skin. This lightweight booster enhances your skin's natural radiance, leaving it feeling silky smooth and looking beautifully glowing. Perfect for achieving that luminous complexion!"
  },
  {
    name: "Supermane Hair Oil",
    description: "Unleash your hair's potential with Herbalicious Supermane Hair Oil! Designed specifically for men, our rich organic blend nourishes the scalp, fights dandruff, and encourages the growth of new hair follicles. Transform your hair into thick, strong, and lustrous locks with this powerful formula. Because great hair isn't just for women!"
  },
  {
    name: "Under-Eye Gel",
    description: "Brighten your eyes with Herbalicious Under-Eye Gel! Our light organic formulation is specially designed for the delicate skin under your eyes. It gently nourishes to reduce dark circles, puffiness, and early signs of aging like wrinkles and crow's feet. Reveal fresher, more youthful-looking eyes with our natural eye care solution!"
  },
  {
    name: "Vitamin-C Serum",
    description: "Boost your skin's radiance with Herbalicious Vitamin C Serum! Our light, quick-absorbing organic formula harnesses the power of oranges and lemons, nature's best sources of vitamin C. This powerhouse serum gives your skin the glow boost it needs while keeping it hydrated in all seasons. A must-have in your natural skincare routine!"
  }
];

async function updateProductDescriptions() {
  console.log('Starting product description update...\n');
  
  let updatedCount = 0;
  let notFoundProducts: string[] = [];
  
  for (const item of productDescriptions) {
    try {
      // Try to find product by exact name match (case-insensitive)
      const product = await prisma.product.findFirst({
        where: {
          name: {
            equals: item.name,
            mode: 'insensitive'
          }
        }
      });
      
      if (product) {
        // Update the product description
        await prisma.product.update({
          where: { id: product.id },
          data: { 
            description: item.description,
            shortDescription: item.description.substring(0, 150) + '...' // First 150 chars for short description
          }
        });
        
        console.log(`✅ Updated: ${item.name}`);
        updatedCount++;
      } else {
        console.log(`❌ Not found: ${item.name}`);
        notFoundProducts.push(item.name);
      }
    } catch (error) {
      console.error(`❌ Error updating ${item.name}:`, error);
    }
  }
  
  console.log('\n=== Update Summary ===');
  console.log(`Total products in list: ${productDescriptions.length}`);
  console.log(`Successfully updated: ${updatedCount}`);
  console.log(`Not found: ${notFoundProducts.length}`);
  
  if (notFoundProducts.length > 0) {
    console.log('\nProducts not found in database:');
    notFoundProducts.forEach(name => console.log(`  - ${name}`));
  }
}

// Run the update
updateProductDescriptions()
  .catch((error) => {
    console.error('Error running update:', error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });