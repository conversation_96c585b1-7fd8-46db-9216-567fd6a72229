'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { signOut, useSession } from 'next-auth/react';
import { User, Package, Heart, Settings, CreditCard, MapPin, Bell, LogOut, ChevronRight, Shield } from 'lucide-react';

interface UserStats {
  user: {
    id: string;
    name: string;
    email: string;
    joinDate: string;
    role: string;
  };
  orders: {
    total: number;
    recent: any[];
  };
  counts: {
    reviews: number;
    wishlist: number;
    addresses: number;
  };
  accountStatus: string;
}

const Profile: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(true);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'loading') return; // Still loading
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  useEffect(() => {
    const fetchUserStats = async () => {
      if (!session?.user?.id) return;

      try {
        const response = await fetch(`/api/users/${session.user.id}/stats`);
        if (response.ok) {
          const data = await response.json();
          setUserStats(data.data);
        }
      } catch (error) {
        console.error('Error fetching user stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserStats();
  }, [session?.user?.id]);

  const user = {
    name: session?.user?.name || 'User',
    email: session?.user?.email || '',
    joinDate: userStats?.user?.joinDate ? new Date(userStats.user.joinDate).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    }) : 'Loading...',
    totalOrders: userStats?.orders?.total || 0,
    isAdmin: (session?.user as any)?.role === 'ADMIN',
    accountStatus: userStats?.accountStatus || 'Loading...'
  };

  const handleSignOut = async () => {
    try {
      await signOut({ 
        redirect: false,
        callbackUrl: '/' 
      });
      // Use replace to avoid back button issues
      window.location.replace('/');
    } catch (error) {
      console.error('Error signing out:', error);
      // Fallback redirect
      window.location.replace('/');
    }
  };

  const menuItems = [
    {
      icon: Package,
      title: 'Order History',
      description: 'View your past orders',
      href: '/order-history',
      color: 'bg-green-100 text-green-600'
    },
    {
      icon: Heart,
      title: 'Wishlist',
      description: 'Your saved items',
      href: '/wishlist',
      color: 'bg-green-100 text-green-600'
    },
    {
      icon: User,
      title: 'Edit Profile',
      description: 'Update your profile details',
      href: '/edit-profile',
      color: 'bg-green-100 text-green-600'
    }
  ];

  const adminItems = [
    {
      icon: Shield,
      title: 'Admin Panel',
      description: 'Manage store (Admin only)',
      href: '/admin',
      color: 'bg-green-100 text-green-600'
    }
  ];

  // Show loading state while checking authentication
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render content if not authenticated
  if (status === 'unauthenticated') {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 grid grid-cols-1 lg:grid-cols-12 gap-8">
      {/* Mobile Layout */}
      <div className="lg:hidden col-span-12">
        <div className="px-4 py-6">
          {/* Profile Header */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center">
                <User className="w-8 h-8 text-white" />
              </div>
              <div className="flex-1">
                <h1 className="text-xl font-bold text-gray-800">{user.name}</h1>
                <p className="text-gray-600">{user.email}</p>
                <p className="text-sm text-gray-500">Member since {user.joinDate}</p>
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 text-center mb-6">
            <p className="text-2xl font-bold text-gray-800">{user.totalOrders}</p>
            <p className="text-sm text-gray-600">Total Orders</p>
          </div>

          {/* Menu Items */}
          <div className="space-y-3">
            {menuItems.map((item, index) => (
              <Link
                key={index}
                href={item.href}
                className="flex items-center space-x-4 p-4 bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
              >
                <div className={`w-12 h-12 rounded-full flex items-center justify-center ${item.color}`}>
                  <item.icon className="w-6 h-6" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-800">{item.title}</h3>
                  <p className="text-sm text-gray-600">{item.description}</p>
                </div>
                <ChevronRight className="w-5 h-5 text-gray-400" />
              </Link>
            ))}

            {/* Admin Items */}
            {user.isAdmin && adminItems.map((item, index) => (
              <Link
                key={`admin-${index}`}
                href={item.href}
                className="flex items-center space-x-4 p-4 bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
              >
                <div className={`w-12 h-12 rounded-full flex items-center justify-center ${item.color}`}>
                  <item.icon className="w-6 h-6" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-800">{item.title}</h3>
                  <p className="text-sm text-gray-600">{item.description}</p>
                </div>
                <ChevronRight className="w-5 h-5 text-gray-400" />
              </Link>
            ))}
          </div>

          {/* Sign Out */}
          <div className="mt-6">
            <button 
              onClick={handleSignOut}
              className="w-full flex items-center justify-center space-x-2 p-4 bg-red-50 text-red-600 rounded-2xl font-medium hover:bg-red-100 transition-colors"
            >
              <LogOut className="w-5 h-5" />
              <span>Sign Out</span>
            </button>
          </div>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:block lg:col-span-12">
        <div className="py-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-12">Profile</h1>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Profile Info */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 mb-8">
                <div className="text-center">
                  <div className="w-24 h-24 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <User className="w-12 h-12 text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-1">{user.name}</h2>
                  <p className="text-gray-600 mb-1">{user.email}</p>
                  <p className="text-sm text-gray-500">Member since {user.joinDate}</p>
                </div>
              </div>

              {/* Stats */}
              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Account Stats</h3>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Orders</span>
                    <span className="font-semibold text-gray-800">{user.totalOrders}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Account Status</span>
                    <span className="font-semibold text-green-600">{user.accountStatus}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Quick Actions */}
              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
                <h2 className="text-2xl font-semibold text-gray-800 mb-6">Quick Actions</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {menuItems.map((item, index) => (
                    <Link
                      key={index}
                      href={item.href}
                      className="flex items-center space-x-3 p-4 rounded-xl hover:bg-gray-50 transition-colors border border-gray-100"
                    >
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${item.color}`}>
                        <item.icon className="w-5 h-5" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-800">{item.title}</h3>
                        <p className="text-sm text-gray-600">{item.description}</p>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>

              {/* Account Actions */}
              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
                <h2 className="text-2xl font-semibold text-gray-800 mb-6">Account Actions</h2>
                <div className="flex flex-wrap gap-4">
                  <Link
                    href="/edit-profile"
                    className="flex items-center space-x-2 px-6 py-3 bg-green-600 text-white rounded-xl font-medium hover:bg-green-700 transition-colors"
                  >
                    <User className="w-5 h-5" />
                    <span>Edit Profile</span>
                  </Link>
                  {user.isAdmin && (
                    <Link
                      href="/admin"
                      className="flex items-center space-x-2 px-6 py-3 bg-green-700 text-white rounded-xl font-medium hover:bg-green-800 transition-colors"
                    >
                      <Shield className="w-5 h-5" />
                      <span>Admin Panel</span>
                    </Link>
                  )}
                  <button 
                    onClick={handleSignOut}
                    className="flex items-center space-x-2 px-6 py-3 bg-red-50 text-red-600 rounded-xl font-medium hover:bg-red-100 transition-colors ml-auto"
                  >
                    <LogOut className="w-5 h-5" />
                    <span>Sign Out</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;