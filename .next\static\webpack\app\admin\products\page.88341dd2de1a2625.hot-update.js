"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/page",{

/***/ "(app-pages-browser)/./app/admin/products/page.tsx":
/*!*************************************!*\
  !*** ./app/admin/products/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _lib_currency__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/currency */ \"(app-pages-browser)/./app/lib/currency.ts\");\n/* harmony import */ var _components_admin_FAQManagementModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/admin/FAQManagementModal */ \"(app-pages-browser)/./app/components/admin/FAQManagementModal.tsx\");\n/* harmony import */ var _components_admin_VariationsManagementModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/admin/VariationsManagementModal */ \"(app-pages-browser)/./app/components/admin/VariationsManagementModal.tsx\");\n/* harmony import */ var _components_admin_ImageGalleryManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/admin/ImageGalleryManager */ \"(app-pages-browser)/./app/components/admin/ImageGalleryManager.tsx\");\n/* harmony import */ var _components_admin_CategoryMultiSelect__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/admin/CategoryMultiSelect */ \"(app-pages-browser)/./app/components/admin/CategoryMultiSelect.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\nconst ProductsPage = ()=>{\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProduct, setEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [importing, setImporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [bulkLoading, setBulkLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showFAQModal, setShowFAQModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProductForFAQ, setSelectedProductForFAQ] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showVariationsModal, setShowVariationsModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProductForVariations, setSelectedProductForVariations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch products and categories\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProducts();\n        fetchCategories();\n    }, []);\n    const fetchProducts = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/products?limit=1000\", {\n                headers: {\n                    \"x-admin-request\": \"true\"\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setProducts(data.data);\n            } else {\n                setError(\"Failed to fetch products\");\n            }\n        } catch (error) {\n            if (true) {\n                console.error(\"Error fetching products:\", error);\n            }\n            setError(\"Failed to fetch products\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchCategories = async ()=>{\n        try {\n            const response = await fetch(\"/api/categories\");\n            const data = await response.json();\n            if (data.success) {\n                setCategories(data.data);\n            }\n        } catch (error) {\n            if (true) {\n                console.error(\"Error fetching categories:\", error);\n            }\n        }\n    };\n    // CSV Template Download\n    const handleDownloadTemplate = ()=>{\n        const csvContent = [\n            \"Name,Slug,Description,Short Description,Price,Compare Price,Category,Categories,Featured,Active,Variations\",\n            'Sample Product,sample-product,\"A great product description\",\"Short description\",99.99,149.99,Skincare,\"Skincare;Face Care\",yes,yes,\"[{\"\"name\"\":\"\"Size\"\",\"\"value\"\":\"\"50ml\"\",\"\"price\"\":99.99}]\"',\n            \"# Instructions:\",\n            \"# - Name: Required product name\",\n            \"# - Slug: SEO-friendly URL (auto-generated if empty)\",\n            \"# - Price: Required base price in rupees\",\n            \"# - Category: Single category name\",\n            \"# - Categories: Multiple categories separated by semicolons\",\n            \"# - Featured: yes/no\",\n            \"# - Active: yes/no (defaults to yes)\",\n            \"# - Variations: JSON array of variations with name, value, and price\"\n        ].join(\"\\n\");\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: \"text/csv\"\n        });\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"products_template.csv\";\n        a.click();\n        window.URL.revokeObjectURL(url);\n    };\n    // JSON Template Download\n    const handleDownloadJSONTemplate = ()=>{\n        const jsonTemplate = {\n            products: [\n                {\n                    name: \"Sample Product\",\n                    slug: \"sample-product\",\n                    description: \"A detailed product description\",\n                    shortDescription: \"Short description\",\n                    price: 99.99,\n                    comparePrice: 149.99,\n                    categoryNames: [\n                        \"Skincare\",\n                        \"Face Care\"\n                    ],\n                    isFeatured: true,\n                    isActive: true,\n                    variations: [\n                        {\n                            name: \"Size\",\n                            value: \"50ml\",\n                            price: 99.99\n                        },\n                        {\n                            name: \"Size\",\n                            value: \"100ml\",\n                            price: 179.99\n                        }\n                    ]\n                }\n            ]\n        };\n        const blob = new Blob([\n            JSON.stringify(jsonTemplate, null, 2)\n        ], {\n            type: \"application/json\"\n        });\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"products_template.json\";\n        a.click();\n        window.URL.revokeObjectURL(url);\n    };\n    // Import functionality\n    const handleImport = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".csv,.json\";\n        input.onchange = async (e)=>{\n            var _e_target_files;\n            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n            if (!file) return;\n            try {\n                setImporting(true);\n                const text = await file.text();\n                let importData = [];\n                if (file.name.toLowerCase().endsWith(\".json\")) {\n                    // Handle JSON import\n                    try {\n                        const jsonData = JSON.parse(text);\n                        // Check if it's the expected format with products array\n                        if (jsonData.products && Array.isArray(jsonData.products)) {\n                            importData = jsonData.products;\n                        } else if (Array.isArray(jsonData)) {\n                            // Handle direct array of products\n                            importData = jsonData;\n                        } else {\n                            alert('Invalid JSON format. Expected format: {\"products\": [...]} or direct array of products.');\n                            return;\n                        }\n                        // Validate each product in JSON\n                        const validProducts = [];\n                        for (const product of importData){\n                            if (!product.name) {\n                                if (true) {\n                                    console.warn(\"Skipping product without name:\", product);\n                                }\n                                continue;\n                            }\n                            // Handle both single category and multiple categories\n                            const categoryNames = [];\n                            if (product.category) {\n                                categoryNames.push(product.category);\n                            }\n                            if (product.categoryNames && Array.isArray(product.categoryNames)) {\n                                categoryNames.push(...product.categoryNames);\n                            }\n                            if (categoryNames.length === 0) {\n                                if (true) {\n                                    console.warn(\"Skipping product without categories:\", product.name);\n                                }\n                                continue;\n                            }\n                            // Check if price is valid or if variations provide pricing\n                            const hasValidPrice = product.price && product.price > 0 || product.variations && product.variations.length > 0;\n                            if (!hasValidPrice) {\n                                if (true) {\n                                    console.warn(\"Skipping product without valid price or variations:\", product.name);\n                                }\n                                continue;\n                            }\n                            validProducts.push({\n                                ...product,\n                                categoryNames,\n                                slug: product.slug || (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.generateSlug)(product.name),\n                                isFeatured: Boolean(product.isFeatured),\n                                isActive: product.isActive !== false\n                            });\n                        }\n                        importData = validProducts;\n                    } catch (jsonError) {\n                        alert(\"Invalid JSON file. Please check the file format.\");\n                        return;\n                    }\n                } else {\n                    // Handle CSV import\n                    const lines = text.split(\"\\n\").filter((line)=>line.trim() && !line.trim().startsWith(\"#\"));\n                    const headers = lines[0].split(\",\").map((h)=>h.trim().replace(/\"/g, \"\"));\n                    if (true) {\n                        console.log(\"CSV Headers found:\", headers);\n                    }\n                    // Validate headers\n                    const requiredHeaders = [\n                        \"Name\"\n                    ];\n                    const missingHeaders = requiredHeaders.filter((header)=>!headers.some((h)=>h.toLowerCase().includes(header.toLowerCase())));\n                    if (missingHeaders.length > 0) {\n                        alert(\"Missing required columns: \".concat(missingHeaders.join(\", \")));\n                        return;\n                    }\n                    // Check if we have either Category or Categories column\n                    const hasCategoryColumn = headers.some((h)=>h.toLowerCase().includes(\"category\"));\n                    if (!hasCategoryColumn) {\n                        alert('Missing category information. Please include either \"Category\" or \"Categories\" column.');\n                        return;\n                    }\n                    for(let i = 1; i < lines.length; i++){\n                        var _values_featuredIndex, _values_activeIndex;\n                        const line = lines[i].trim();\n                        if (!line) continue;\n                        const values = line.split(\",\").map((v)=>v.trim().replace(/\"/g, \"\"));\n                        // Find header indices more robustly\n                        const nameIndex = headers.findIndex((h)=>h.toLowerCase().includes(\"name\"));\n                        const slugIndex = headers.findIndex((h)=>h.toLowerCase().includes(\"slug\"));\n                        const variationsIndex = headers.findIndex((h)=>h.toLowerCase().includes(\"variations\"));\n                        const productName = nameIndex >= 0 ? values[nameIndex] || \"\" : \"\";\n                        const productSlug = slugIndex >= 0 ? values[slugIndex] || \"\" : \"\";\n                        const variationsStr = variationsIndex >= 0 ? values[variationsIndex] || \"\" : \"\";\n                        // Parse variations from JSON string\n                        let variations = [];\n                        if (variationsStr) {\n                            try {\n                                variations = JSON.parse(variationsStr);\n                            } catch (e) {\n                                if (true) {\n                                    console.warn(\"Invalid variations JSON:\", variationsStr);\n                                }\n                            }\n                        }\n                        // Parse categories\n                        const categoryNames = [];\n                        // Check for multiple categories in \"Categories\" column\n                        const categoriesIndex = headers.findIndex((h)=>h.toLowerCase() === \"categories\");\n                        const categoriesValue = categoriesIndex >= 0 ? values[categoriesIndex] || \"\" : \"\";\n                        if (categoriesValue) {\n                            // Split by semicolon and clean up\n                            categoryNames.push(...categoriesValue.split(\";\").map((cat)=>cat.trim()).filter((cat)=>cat));\n                        }\n                        // Fallback to single category column if no multiple categories\n                        if (categoryNames.length === 0) {\n                            const categoryIndex = headers.findIndex((h)=>h.toLowerCase() === \"category\");\n                            const singleCategory = categoryIndex >= 0 ? values[categoryIndex] || \"\" : \"\";\n                            if (singleCategory) {\n                                categoryNames.push(singleCategory);\n                            }\n                        }\n                        const priceIndex = headers.findIndex((h)=>h.toLowerCase() === \"price\");\n                        const priceStr = priceIndex >= 0 ? values[priceIndex] || \"\" : \"\";\n                        const price = parseFloat(priceStr) || 0;\n                        // Check if price is valid or if variations provide pricing\n                        const hasValidPrice = price >= 0 || variations && variations.length > 0;\n                        // Find remaining field indices\n                        const descriptionIndex = headers.findIndex((h)=>h.toLowerCase() === \"description\");\n                        const shortDescriptionIndex = headers.findIndex((h)=>h.toLowerCase().includes(\"short description\"));\n                        const comparePriceIndex = headers.findIndex((h)=>h.toLowerCase().includes(\"compare price\"));\n                        const featuredIndex = headers.findIndex((h)=>h.toLowerCase() === \"featured\");\n                        const activeIndex = headers.findIndex((h)=>h.toLowerCase() === \"active\");\n                        const product = {\n                            name: productName,\n                            slug: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.validateSlug)(productSlug, productName),\n                            description: descriptionIndex >= 0 ? values[descriptionIndex] || \"\" : \"\",\n                            shortDescription: shortDescriptionIndex >= 0 ? values[shortDescriptionIndex] || \"\" : \"\",\n                            price: price,\n                            comparePrice: comparePriceIndex >= 0 ? parseFloat(values[comparePriceIndex] || \"0\") || null : null,\n                            categoryNames: categoryNames,\n                            isFeatured: featuredIndex >= 0 ? ((_values_featuredIndex = values[featuredIndex]) === null || _values_featuredIndex === void 0 ? void 0 : _values_featuredIndex.toLowerCase()) === \"yes\" : false,\n                            isActive: activeIndex >= 0 ? ((_values_activeIndex = values[activeIndex]) === null || _values_activeIndex === void 0 ? void 0 : _values_activeIndex.toLowerCase()) !== \"no\" : true,\n                            variations: variations\n                        };\n                        // Allow products with name, category, and valid price\n                        const hasValidCategory = categoryNames.length > 0;\n                        if (product.name && hasValidCategory && hasValidPrice) {\n                            importData.push(product);\n                        }\n                    }\n                }\n                if (importData.length === 0) {\n                    alert('No valid products found in the file. Please ensure:\\n\\n1. Each product has a Name\\n2. Each product has at least one Category\\n3. Price must be provided OR variations must have prices\\n\\nFor JSON: Use format {\"products\": [...]} or direct array\\nFor CSV: Check the CSV template for the correct format.');\n                    return;\n                }\n                // Send import data to API\n                const response = await fetch(\"/api/products/import\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        products: importData\n                    })\n                });\n                const result = await response.json();\n                if (result.success) {\n                    const { success, failed, errors } = result.data;\n                    let message = \"Import completed!\\n✅ \".concat(success, \" products imported successfully\");\n                    if (failed > 0) {\n                        message += \"\\n❌ \".concat(failed, \" products failed\");\n                        if (errors.length > 0) {\n                            message += \"\\n\\nErrors:\\n\".concat(errors.slice(0, 5).join(\"\\n\"));\n                            if (errors.length > 5) {\n                                message += \"\\n... and \".concat(errors.length - 5, \" more errors\");\n                            }\n                        }\n                    }\n                    alert(message);\n                    // Refresh the products list\n                    if (success > 0) {\n                        fetchProducts();\n                    }\n                } else {\n                    alert(\"Import failed: \".concat(result.error));\n                }\n            } catch (error) {\n                if (true) {\n                    console.error(\"Import error:\", error);\n                }\n                alert(\"Failed to import products. Please check the file format.\");\n            } finally{\n                setImporting(false);\n            }\n        };\n        input.click();\n    };\n    // Export functionality\n    const handleExport = async function() {\n        let format = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"json\";\n        try {\n            const response = await fetch(\"/api/admin/products/export?format=\".concat(format));\n            if (!response.ok) {\n                throw new Error(\"Failed to export products\");\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            // Generate filename with current date\n            const extension = format === \"csv\" ? \"csv\" : \"json\";\n            const filename = \"products-backup-\".concat(new Date().toISOString().split(\"T\")[0], \".\").concat(extension);\n            a.download = filename;\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            window.URL.revokeObjectURL(url);\n        } catch (error) {\n            console.error(\"Error exporting products:\", error);\n            alert(\"Failed to export products. Please try again.\");\n        }\n    };\n    // Cleanup functionality\n    const handleCleanup = async ()=>{\n        if (!confirm(\"Are you sure you want to delete all notifications, reviews, and orders? This action cannot be undone.\")) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/admin/cleanup\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const result = await response.json();\n            if (result.success) {\n                alert(\"Cleanup completed successfully!\\n\\nDeleted:\\n- \".concat(result.deletedCounts.notifications, \" notifications\\n- \").concat(result.deletedCounts.reviews, \" reviews\\n- \").concat(result.deletedCounts.orders, \" orders\\n- \").concat(result.deletedCounts.orderItems, \" order items\\n- \").concat(result.deletedCounts.orderAddresses, \" order addresses\\n- \").concat(result.deletedCounts.couponUsages, \" coupon usages\"));\n            } else {\n                alert(\"Cleanup failed: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error(\"Error during cleanup:\", error);\n            alert(\"Failed to cleanup database. Please try again.\");\n        }\n    };\n    // Fixed filteredProducts function\n    const filteredProducts = products.filter((product)=>{\n        var _product_category, _product_productCategories;\n        if (!searchTerm) return true;\n        const nameMatch = product.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const primaryCategoryMatch = (_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const multipleCategoriesMatch = (_product_productCategories = product.productCategories) === null || _product_productCategories === void 0 ? void 0 : _product_productCategories.some((pc)=>pc.category.name.toLowerCase().includes(searchTerm.toLowerCase()));\n        return nameMatch || primaryCategoryMatch || multipleCategoriesMatch;\n    });\n    const handleSelectProduct = (productId)=>{\n        setSelectedProducts((prev)=>prev.includes(productId) ? prev.filter((id)=>id !== productId) : [\n                ...prev,\n                productId\n            ]);\n    };\n    const handleSelectAll = ()=>{\n        setSelectedProducts(selectedProducts.length === filteredProducts.length ? [] : filteredProducts.map((p)=>p.id));\n    };\n    const handleEditProduct = (product)=>{\n        setEditingProduct(product);\n        setShowEditModal(true);\n    };\n    const handleDeleteProduct = async (productId)=>{\n        if (confirm(\"Are you sure you want to delete this product?\")) {\n            try {\n                const response = await fetch(\"/api/products/\".concat(productId), {\n                    method: \"DELETE\"\n                });\n                const result = await response.json();\n                if (response.ok) {\n                    if (result.type === \"soft_delete\") {\n                        alert(\"Product deactivated: \".concat(result.message));\n                    } else {\n                        alert(\"Product deleted successfully\");\n                    }\n                    setProducts((prev)=>prev.filter((p)=>p.id !== productId));\n                } else {\n                    alert(\"Failed to delete product: \".concat(result.error || \"Unknown error\"));\n                }\n            } catch (error) {\n                if (true) {\n                    console.error(\"Error deleting product:\", error);\n                }\n                alert(\"Failed to delete product\");\n            }\n        }\n    };\n    // Bulk operations\n    const handleBulkAction = async (action)=>{\n        if (selectedProducts.length === 0) {\n            alert(\"Please select products first\");\n            return;\n        }\n        const actionMessages = {\n            delete: \"Are you sure you want to delete the selected products? This action cannot be undone.\",\n            activate: \"Are you sure you want to activate the selected products?\",\n            deactivate: \"Are you sure you want to deactivate the selected products?\",\n            feature: \"Are you sure you want to feature the selected products?\",\n            unfeature: \"Are you sure you want to unfeature the selected products?\"\n        };\n        if (!confirm(actionMessages[action])) {\n            return;\n        }\n        try {\n            setBulkLoading(true);\n            const response = await fetch(\"/api/products/bulk\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action,\n                    productIds: selectedProducts\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                alert(result.message);\n                setSelectedProducts([]);\n                fetchProducts(); // Refresh the products list\n            } else {\n                alert(\"Failed to \".concat(action, \" products: \").concat(result.error));\n            }\n        } catch (error) {\n            console.error(\"Error in bulk \".concat(action, \":\"), error);\n            alert(\"Failed to \".concat(action, \" products\"));\n        } finally{\n            setBulkLoading(false);\n        }\n    };\n    const handleManageFAQs = (product)=>{\n        setSelectedProductForFAQ(product);\n        setShowFAQModal(true);\n    };\n    const handleManageVariations = (product)=>{\n        setSelectedProductForVariations(product);\n        setShowVariationsModal(true);\n    };\n    const toggleFeatured = async (productId)=>{\n        try {\n            const product = products.find((p)=>p.id === productId);\n            if (!product) return;\n            const response = await fetch(\"/api/products/\".concat(productId), {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    isFeatured: !product.isFeatured\n                })\n            });\n            if (response.ok) {\n                setProducts((prev)=>prev.map((p)=>p.id === productId ? {\n                            ...p,\n                            isFeatured: !p.isFeatured\n                        } : p));\n            }\n        } catch (error) {\n            if (true) {\n                console.error(\"Error updating product:\", error);\n            }\n        }\n    };\n    const getCategoryColor = (categoryName)=>{\n        const colors = {\n            \"Skincare\": \"bg-green-100 text-green-800\",\n            \"Hair Care\": \"bg-purple-100 text-purple-800\",\n            \"Body Care\": \"bg-blue-100 text-blue-800\",\n            \"cleanser\": \"bg-blue-100 text-blue-800\",\n            \"serum\": \"bg-purple-100 text-purple-800\",\n            \"moisturizer\": \"bg-green-100 text-green-800\",\n            \"mask\": \"bg-yellow-100 text-yellow-800\",\n            \"exfoliator\": \"bg-pink-100 text-pink-800\",\n            \"eye-care\": \"bg-indigo-100 text-indigo-800\"\n        };\n        return colors[categoryName] || \"bg-gray-100 text-gray-800\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Manage your skincare product catalog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 678,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 sm:mt-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddModal(true),\n                                className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 687,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Add Product\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 683,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 682,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 677,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 676,\n                columnNumber: 7\n            }, undefined),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-green-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 697,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 text-gray-600\",\n                        children: \"Loading products...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 698,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 696,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 705,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchProducts,\n                        className: \"mt-2 text-red-600 hover:text-red-700 underline\",\n                        children: \"Try again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 706,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 704,\n                columnNumber: 9\n            }, undefined),\n            !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 722,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search products...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 721,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleDownloadTemplate,\n                                                    className: \"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                    title: \"Download CSV template\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-5 h-5 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleDownloadJSONTemplate,\n                                                    className: \"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium text-gray-600\",\n                                                    title: \"Download JSON template\",\n                                                    children: \"JSON\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleImport,\n                                            disabled: importing,\n                                            className: \"flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\",\n                                            title: \"Import products from CSV/JSON\",\n                                            children: [\n                                                importing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 761,\n                                                    columnNumber: 17\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                importing ? \"Importing...\" : \"Import\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleExport(\"json\"),\n                                            className: \"flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                            title: \"Export products as JSON\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 772,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                \"JSON\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 767,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleExport(\"csv\"),\n                                            className: \"flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                            title: \"Export products as CSV\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 780,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                \"CSV\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 775,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCleanup,\n                                            className: \"flex items-center px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n                                            title: \"Clean up notifications, reviews, and orders\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                \"Cleanup\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 736,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 719,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 718,\n                        columnNumber: 7\n                    }, undefined),\n                    selectedProducts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-blue-900\",\n                                            children: [\n                                                selectedProducts.length,\n                                                \" product\",\n                                                selectedProducts.length > 1 ? \"s\" : \"\",\n                                                \" selected\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 800,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedProducts([]),\n                                            className: \"text-blue-600 hover:text-blue-700 text-sm\",\n                                            children: \"Clear selection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 803,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 799,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleBulkAction(\"activate\"),\n                                            disabled: bulkLoading,\n                                            className: \"px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:opacity-50\",\n                                            children: \"Activate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleBulkAction(\"deactivate\"),\n                                            disabled: bulkLoading,\n                                            className: \"px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 disabled:opacity-50\",\n                                            children: \"Deactivate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 819,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleBulkAction(\"feature\"),\n                                            disabled: bulkLoading,\n                                            className: \"px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700 disabled:opacity-50\",\n                                            children: \"Feature\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 826,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleBulkAction(\"unfeature\"),\n                                            disabled: bulkLoading,\n                                            className: \"px-3 py-1 bg-orange-600 text-white text-sm rounded hover:bg-orange-700 disabled:opacity-50\",\n                                            children: \"Unfeature\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 833,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleBulkAction(\"delete\"),\n                                            disabled: bulkLoading,\n                                            className: \"px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 disabled:opacity-50 flex items-center\",\n                                            children: [\n                                                bulkLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 846,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 848,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Delete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 840,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 811,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 798,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 797,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: selectedProducts.length === filteredProducts.length && filteredProducts.length > 0,\n                                                        onChange: handleSelectAll,\n                                                        className: \"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 864,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 863,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Product\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 871,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 874,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Price\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 880,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 883,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 862,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 861,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: filteredProducts.map((product)=>{\n                                            var _product_images_, _product_images_1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: selectedProducts.includes(product.id),\n                                                            onChange: ()=>handleSelectProduct(product.id),\n                                                            className: \"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 895,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 894,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: ((_product_images_ = product.images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.url) || \"/images/default-product.jpg\",\n                                                                    alt: ((_product_images_1 = product.images[0]) === null || _product_images_1 === void 0 ? void 0 : _product_images_1.alt) || product.name,\n                                                                    className: \"w-12 h-12 rounded-lg object-cover mr-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: product.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 910,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: product.shortDescription\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 911,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 909,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 903,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1\",\n                                                            children: product.productCategories && product.productCategories.length > 0 ? product.productCategories.map((pc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-medium rounded-full \".concat(getCategoryColor(pc.category.name)),\n                                                                    children: pc.category.name\n                                                                }, pc.category.id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 920,\n                                                                    columnNumber: 27\n                                                                }, undefined)) : /* Fallback to primary category if no many-to-many categories */ product.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-medium rounded-full \".concat(getCategoryColor(product.category.name)),\n                                                                children: product.category.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 927,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 916,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 915,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 text-sm text-gray-900\",\n                                                        children: [\n                                                            product.variants && product.variants.length > 0 ? (()=>{\n                                                                const prices = product.variants.map((v)=>v.price).filter((p)=>typeof p === \"number\" && p > 0);\n                                                                if (prices.length === 0) return \"No pricing\";\n                                                                const min = Math.min(...prices);\n                                                                const max = Math.max(...prices);\n                                                                return min === max ? (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(min) : \"\".concat((0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(min), \" - \").concat((0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(max));\n                                                            })() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500\",\n                                                                children: \"No variations\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 944,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            product.comparePrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 line-through ml-2\",\n                                                                children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(product.comparePrice)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 947,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: product.variants && product.variants.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-600\",\n                                                                children: [\n                                                                    product.variants.length,\n                                                                    \" variations\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 25\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500\",\n                                                                children: \"No variations\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 957,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 953,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 952,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                !product.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800\",\n                                                                    children: \"Inactive\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 964,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                product.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800\",\n                                                                    children: \"Featured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 969,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                product.isActive && !product.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800\",\n                                                                    children: \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 974,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 962,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 961,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleEditProduct(product),\n                                                                    className: \"p-1 text-gray-400 hover:text-green-600 transition-colors\",\n                                                                    title: \"Edit Product\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 987,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 982,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleManageFAQs(product),\n                                                                    className: \"p-1 text-gray-400 hover:text-blue-600 transition-colors\",\n                                                                    title: \"Manage FAQs\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 994,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 989,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleManageVariations(product),\n                                                                    className: \"p-1 text-gray-400 hover:text-purple-600 transition-colors\",\n                                                                    title: \"Manage Variations\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1001,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 996,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleDeleteProduct(product.id),\n                                                                    className: \"p-1 text-gray-400 hover:text-red-600 transition-colors\",\n                                                                    title: \"Delete Product\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1008,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1003,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 981,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 980,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, product.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 893,\n                                                columnNumber: 17\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 891,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 860,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 859,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 858,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 text-sm text-gray-500\",\n                        children: [\n                            \"Showing \",\n                            filteredProducts.length,\n                            \" of \",\n                            products.length,\n                            \" products\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 1020,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true),\n            showAddModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductModal, {\n                isOpen: showAddModal,\n                onClose: ()=>setShowAddModal(false),\n                onSave: fetchProducts,\n                categories: categories\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 1028,\n                columnNumber: 9\n            }, undefined),\n            showEditModal && editingProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductModal, {\n                isOpen: showEditModal,\n                onClose: ()=>{\n                    setShowEditModal(false);\n                    setEditingProduct(null);\n                },\n                onSave: fetchProducts,\n                categories: categories,\n                product: editingProduct\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 1038,\n                columnNumber: 9\n            }, undefined),\n            showFAQModal && selectedProductForFAQ && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_FAQManagementModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showFAQModal,\n                onClose: ()=>{\n                    setShowFAQModal(false);\n                    setSelectedProductForFAQ(null);\n                },\n                product: selectedProductForFAQ\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 1052,\n                columnNumber: 9\n            }, undefined),\n            showVariationsModal && selectedProductForVariations && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_VariationsManagementModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showVariationsModal,\n                onClose: ()=>{\n                    setShowVariationsModal(false);\n                    setSelectedProductForVariations(null);\n                },\n                product: selectedProductForVariations\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 1064,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n        lineNumber: 674,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductsPage, \"EvH8EMgvHJS7UcfEai3ubiXmPTs=\");\n_c = ProductsPage;\nconst ProductModal = (param)=>{\n    let { isOpen, onClose, onSave, categories, product } = param;\n    var _product_comparePrice, _product_category, _product_images, _product_variants;\n    _s1();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (product === null || product === void 0 ? void 0 : product.name) || \"\",\n        slug: (product === null || product === void 0 ? void 0 : product.slug) || \"\",\n        description: (product === null || product === void 0 ? void 0 : product.description) || \"\",\n        shortDescription: (product === null || product === void 0 ? void 0 : product.shortDescription) || \"\",\n        comparePrice: (product === null || product === void 0 ? void 0 : (_product_comparePrice = product.comparePrice) === null || _product_comparePrice === void 0 ? void 0 : _product_comparePrice.toString()) || \"\",\n        categoryId: (product === null || product === void 0 ? void 0 : (_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.id) || \"\",\n        isFeatured: (product === null || product === void 0 ? void 0 : product.isFeatured) || false\n    });\n    // Initialize selected categories from product data\n    const [selectedCategoryIds, setSelectedCategoryIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        var _product_category;\n        if ((product === null || product === void 0 ? void 0 : product.productCategories) && product.productCategories.length > 0) {\n            return product.productCategories.map((pc)=>pc.category.id);\n        } else if (product === null || product === void 0 ? void 0 : (_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.id) {\n            return [\n                product.category.id\n            ];\n        }\n        return [];\n    });\n    const [productImages, setProductImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((product === null || product === void 0 ? void 0 : (_product_images = product.images) === null || _product_images === void 0 ? void 0 : _product_images.map((img)=>({\n            id: img.id || \"\",\n            url: img.url,\n            alt: img.alt || \"\",\n            position: img.position || 0\n        }))) || []);\n    const [variations, setVariations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((product === null || product === void 0 ? void 0 : (_product_variants = product.variants) === null || _product_variants === void 0 ? void 0 : _product_variants.map((v)=>{\n        var _v_price;\n        return {\n            id: v.id,\n            name: v.name,\n            value: v.value,\n            price: ((_v_price = v.price) === null || _v_price === void 0 ? void 0 : _v_price.toString()) || \"0\"\n        };\n    })) || []);\n    const [showVariations, setShowVariations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize variations when product changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (product === null || product === void 0 ? void 0 : product.variants) {\n            setVariations(product.variants.map((v)=>{\n                var _v_price;\n                return {\n                    id: v.id,\n                    name: v.name,\n                    value: v.value,\n                    price: ((_v_price = v.price) === null || _v_price === void 0 ? void 0 : _v_price.toString()) || \"0\"\n                };\n            }));\n        } else {\n            setVariations([]);\n        }\n    }, [\n        product\n    ]);\n    const addVariation = ()=>{\n        setVariations([\n            ...variations,\n            {\n                name: \"\",\n                value: \"\",\n                price: \"0\"\n            }\n        ]);\n    };\n    const updateVariation = (index, field, value)=>{\n        const updatedVariations = [\n            ...variations\n        ];\n        updatedVariations[index] = {\n            ...updatedVariations[index],\n            [field]: value\n        };\n        setVariations(updatedVariations);\n    };\n    const removeVariation = (index)=>{\n        setVariations(variations.filter((_, i)=>i !== index));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        try {\n            const url = product ? \"/api/products/\".concat(product.id) : \"/api/products\";\n            const method = product ? \"PUT\" : \"POST\";\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    ...formData,\n                    slug: formData.slug || (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.generateSlug)(formData.name),\n                    comparePrice: formData.comparePrice ? parseFloat(formData.comparePrice) : null,\n                    categoryIds: selectedCategoryIds,\n                    images: productImages.map((img, index)=>({\n                            url: img.url,\n                            alt: img.alt || formData.name,\n                            position: index\n                        })),\n                    variations: variations.filter((v)=>v.name && v.value).map((v)=>({\n                            name: v.name,\n                            value: v.value,\n                            price: v.price ? parseFloat(v.price) : 0\n                        }))\n                })\n            });\n            const result = await response.json();\n            if (response.ok) {\n                if (result.warnings && result.warnings.length > 0) {\n                    alert(\"Product saved successfully!\\n\\nWarnings:\\n\".concat(result.warnings.join(\"\\n\")));\n                }\n                onSave();\n                onClose();\n            } else {\n                alert(\"Failed to save product: \".concat(result.error || \"Unknown error\"));\n            }\n        } catch (error) {\n            if (true) {\n                console.error(\"Error saving product:\", error);\n            }\n            alert(\"Failed to save product\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: product ? \"Edit Product\" : \"Add Product\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1225,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1228,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 1224,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Product Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1239,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.name,\n                                            onChange: (e)=>{\n                                                const newName = e.target.value;\n                                                setFormData({\n                                                    ...formData,\n                                                    name: newName,\n                                                    // Auto-generate slug if slug field is empty\n                                                    slug: formData.slug || (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.generateSlug)(newName)\n                                                });\n                                            },\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1242,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1238,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: [\n                                                \"URL Slug\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 ml-1\",\n                                                    children: \"(SEO-friendly URL)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1262,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1260,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.slug,\n                                            onChange: (e)=>setFormData({\n                                                    ...formData,\n                                                    slug: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.generateSlug)(e.target.value)\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                                            placeholder: \"auto-generated-from-name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1264,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                \"Preview: /products/\",\n                                                formData.slug || (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.generateSlug)(formData.name)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1271,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1259,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_CategoryMultiSelect__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        categories: categories,\n                                        selectedCategoryIds: selectedCategoryIds,\n                                        onChange: setSelectedCategoryIds,\n                                        label: \"Categories\",\n                                        placeholder: \"Select categories...\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 1277,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1276,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Compare Price (₹)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1288,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            step: \"0.01\",\n                                            value: formData.comparePrice,\n                                            onChange: (e)=>setFormData({\n                                                    ...formData,\n                                                    comparePrice: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                                            placeholder: \"Enter compare price in Rupees\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1291,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1287,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1237,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Short Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1303,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.shortDescription,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            shortDescription: e.target.value\n                                        }),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1306,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1302,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1315,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: formData.description,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            description: e.target.value\n                                        }),\n                                    rows: 4,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1318,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1314,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ImageGalleryManager__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            images: productImages,\n                            onChange: (images)=>setProductImages(images.map((img)=>({\n                                        id: img.id || \"\",\n                                        url: img.url,\n                                        alt: img.alt || \"\",\n                                        position: img.position || 0\n                                    }))),\n                            productName: formData.name || \"Product\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1326,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border border-gray-200 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"Product Variations\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1340,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowVariations(!showVariations),\n                                            className: \"text-sm text-blue-600 hover:text-blue-700\",\n                                            children: showVariations ? \"Hide Variations\" : \"Add Variations\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1341,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1339,\n                                    columnNumber: 13\n                                }, undefined),\n                                showVariations && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600 mb-3\",\n                                            children: \"Add variations like Size, Color, Material, etc. Each variation can have its own price adjustment and stock.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1352,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        variations.map((variation, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-3 p-3 border border-gray-200 rounded-lg bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                                                children: \"Name (e.g., Size)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1359,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: variation.name,\n                                                                onChange: (e)=>updateVariation(index, \"name\", e.target.value),\n                                                                className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500\",\n                                                                placeholder: \"Size\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1362,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1358,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                                                children: \"Value (e.g., Large)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1371,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: variation.value,\n                                                                onChange: (e)=>updateVariation(index, \"value\", e.target.value),\n                                                                className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500\",\n                                                                placeholder: \"Large\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1374,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1370,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-end space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                                                        children: \"Price Adjustment (₹)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1384,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: variation.price,\n                                                                        onChange: (e)=>updateVariation(index, \"price\", e.target.value),\n                                                                        className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1387,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1383,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>removeVariation(index),\n                                                                className: \"px-2 py-1 text-red-600 hover:text-red-700 text-sm\",\n                                                                title: \"Remove variation\",\n                                                                children: \"\\xd7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1396,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1382,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 1357,\n                                                columnNumber: 19\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: addVariation,\n                                            className: \"w-full px-3 py-2 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-700 transition-colors\",\n                                            children: \"+ Add Variation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1408,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1351,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1338,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"featured\",\n                                    checked: formData.isFeatured,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            isFeatured: e.target.checked\n                                        }),\n                                    className: \"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1420,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"featured\",\n                                    className: \"ml-2 text-sm text-gray-700\",\n                                    children: \"Featured Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1427,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1419,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-end space-x-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1433,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\",\n                                    children: loading ? \"Saving...\" : product ? \"Update\" : \"Create\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1440,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1432,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 1236,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n            lineNumber: 1223,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n        lineNumber: 1222,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(ProductModal, \"1TmEwypsCy4rN1ZAJXEjVGFAyng=\");\n_c1 = ProductModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProductsPage);\nvar _c, _c1;\n$RefreshReg$(_c, \"ProductsPage\");\n$RefreshReg$(_c1, \"ProductModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/products/page.tsx\n"));

/***/ })

});