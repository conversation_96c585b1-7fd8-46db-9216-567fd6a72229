'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { logger } from '../lib/logger';

interface PaymentButtonProps {
  cartItems: Array<{
    productId: string;
    quantity: number;
    price: number;
    product: {
      name: string;
      price: number;
    };
  }>;
  shippingAddress: {
    firstName: string;
    lastName: string;
    address1: string;
    address2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
    phone: string;
  };
  totalAmount: number;
  appliedCoupons?: Array<{
    coupon: {
      id: string;
      code: string;
      name: string;
    };
    discountAmount: number;
  }>;
  onSuccess?: (orderId: string) => void;
  onError?: (error: string) => void;
  className?: string;
  disabled?: boolean;
}

declare global {
  interface Window {
    Razorpay: any;
  }
}

export default function PaymentButton({
  cartItems,
  shippingAddress,
  totalAmount,
  appliedCoupons = [],
  onSuccess,
  onError,
  className = '',
  disabled = false
}: PaymentButtonProps) {
  const { data: session } = useSession();
  const [isProcessing, setIsProcessing] = useState(false);

  const loadRazorpayScript = () => {
    return new Promise((resolve) => {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => resolve(true);
      script.onerror = () => resolve(false);
      document.body.appendChild(script);
    });
  };

  const handlePayment = async () => {
    if (!session?.user) {
      onError?.('Please login to continue');
      return;
    }

    if (cartItems.length === 0) {
      onError?.('Cart is empty');
      return;
    }

    setIsProcessing(true);

    try {
      // Load Razorpay script
      const scriptLoaded = await loadRazorpayScript();
      if (!scriptLoaded) {
        throw new Error('Failed to load payment gateway');
      }

      // Create order
      const orderResponse = await fetch('/api/payments/create-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cartItems: cartItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            price: item.price
          })),
          shippingAddress,
          totalAmount,
          appliedCoupons
        }),
      });

      if (!orderResponse.ok) {
        const errorData = await orderResponse.json();
        throw new Error(errorData.message || 'Failed to create order');
      }

      const orderData = await orderResponse.json();

      // Configure Razorpay options
      const options = {
        key: orderData.razorpayKeyId,
        amount: orderData.order.amount,
        currency: orderData.order.currency,
        name: 'Herbalicious',
        description: `Order #${orderData.order.orderNumber || 'New Order'}`,
        order_id: orderData.order.razorpayOrderId,
        handler: async (response: any) => {
          try {
            // Verify payment
            const verifyResponse = await fetch('/api/payments/verify', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                razorpay_order_id: response.razorpay_order_id,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_signature: response.razorpay_signature,
                order_id: orderData.order.id
              }),
            });

            if (!verifyResponse.ok) {
              const errorData = await verifyResponse.json();
              throw new Error(errorData.message || 'Payment verification failed');
            }

            const verifyData = await verifyResponse.json();
            
            logger.info('Payment completed successfully', {
              orderId: verifyData.order.id,
              orderNumber: verifyData.order.orderNumber,
              paymentId: response.razorpay_payment_id
            });

            onSuccess?.(verifyData.order.id);
          } catch (error) {
            console.error('Payment verification error:', error);
            onError?.((error as Error).message || 'Payment verification failed');
          }
        },
        prefill: {
          name: `${shippingAddress.firstName} ${shippingAddress.lastName}`,
          email: session.user.email || '',
          contact: shippingAddress.phone
        },
        notes: {
          address: `${shippingAddress.address1}, ${shippingAddress.city}, ${shippingAddress.state}`
        },
        theme: {
          color: '#2d5a27'
        },
        modal: {
          ondismiss: () => {
            setIsProcessing(false);
            onError?.('Payment cancelled');
          }
        }
      };

      // Open Razorpay checkout
      const razorpay = new window.Razorpay(options);
      razorpay.open();

    } catch (error) {
      console.error('Payment error:', error);
      setIsProcessing(false);
      onError?.((error as Error).message || 'Payment failed');
    }
  };

  return (
    <button
      onClick={handlePayment}
      disabled={disabled || isProcessing || !session?.user}
      className={`
        w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 
        text-white font-semibold py-3 px-6 rounded-lg transition-colors
        flex items-center justify-center space-x-2
        ${className}
      `}
    >
      {isProcessing ? (
        <>
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
          <span>Processing...</span>
        </>
      ) : (
        <>
          <span>Pay ₹{totalAmount.toFixed(2)}</span>
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        </>
      )}
    </button>
  );
}