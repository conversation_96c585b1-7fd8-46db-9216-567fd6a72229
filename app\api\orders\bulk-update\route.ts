import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../lib/auth';
import { prisma } from '../../../lib/db';
import { handleApiError, AuthenticationError, ValidationError, asyncHandler } from '../../../lib/errors';
import { logger } from '../../../lib/logger';
import { orderNotifications } from '../../../lib/notification-helpers';

const bulkUpdateSchema = z.object({
  orderIds: z.array(z.string()).min(1, 'At least one order ID is required'),
  updates: z.object({
    status: z.enum(['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELLED', 'REFUNDED']).optional(),
    paymentStatus: z.enum(['PENDING', 'PAID', 'FAILED', 'REFUNDED']).optional(),
  }).refine(data => data.status || data.paymentStatus, {
    message: 'At least one update field is required'
  })
});

// POST /api/orders/bulk-update - Bulk update orders (admin only)
export const POST = asyncHandler(async (request: NextRequest) => {
  logger.apiRequest('POST', '/api/orders/bulk-update');

  // Check authentication and admin role
  const session = await getServerSession(authOptions);
  if (!session?.user?.id || session.user.role !== 'ADMIN') {
    throw new AuthenticationError('Admin access required');
  }

  const body = await request.json();
  const validatedData = bulkUpdateSchema.parse(body);
  const { orderIds, updates } = validatedData;

  try {
    // Get current orders
    const currentOrders = await prisma.order.findMany({
      where: {
        id: { in: orderIds }
      },
      include: {
        user: true
      }
    });

    if (currentOrders.length === 0) {
      throw new ValidationError('No valid orders found');
    }

    // Update orders
    const updateResult = await prisma.order.updateMany({
      where: {
        id: { in: orderIds }
      },
      data: {
        ...(updates.status && { status: updates.status }),
        ...(updates.paymentStatus && { paymentStatus: updates.paymentStatus }),
        updatedAt: new Date()
      }
    });

    // Send notifications for status changes
    if (updates.status) {
      const notificationPromises = currentOrders
        .filter(order => order.status !== updates.status)
        .map(async (order) => {
          try {
            switch (updates.status) {
              case 'CONFIRMED':
                await orderNotifications.orderConfirmed(order.userId, {
                  orderId: order.id,
                  orderNumber: order.orderNumber
                });
                break;
              case 'SHIPPED':
                await orderNotifications.orderShipped(order.userId, {
                  orderId: order.id,
                  orderNumber: order.orderNumber
                });
                break;
              case 'DELIVERED':
                await orderNotifications.orderDelivered(order.userId, {
                  orderId: order.id,
                  orderNumber: order.orderNumber
                });
                // Update payment status to PAID for COD orders
                if (order.paymentMethod === 'COD' && order.paymentStatus !== 'PAID') {
                  await prisma.order.update({
                    where: { id: order.id },
                    data: { paymentStatus: 'PAID' }
                  });
                }
                break;
              case 'CANCELLED':
                await orderNotifications.orderCancelled(order.userId, {
                  orderId: order.id,
                  orderNumber: order.orderNumber,
                  reason: 'Bulk update by admin'
                });
                break;
            }
          } catch (error) {
            logger.error(`Failed to send notification for order ${order.id}`, error as Error);
          }
        });

      await Promise.allSettled(notificationPromises);
    }

    logger.info('Bulk order update completed', {
      adminId: session.user.id,
      orderCount: updateResult.count,
      updates
    });

    return NextResponse.json({
      success: true,
      updatedCount: updateResult.count,
      message: `Successfully updated ${updateResult.count} orders`
    });

  } catch (error) {
    logger.error('Failed to bulk update orders', error as Error);
    throw error;
  }
});