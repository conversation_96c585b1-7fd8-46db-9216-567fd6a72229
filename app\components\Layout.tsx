'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Home, ShoppingBag, ShoppingCart, User, Bell, Menu } from 'lucide-react';
import { useCart } from '../context/CartContext';
import { useNotifications } from '../context/NotificationContext';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const pathname = usePathname();
  const { data: session } = useSession();
  const { state } = useCart();
  const { unreadCount } = useNotifications();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const isActive = (path: string) => pathname === path;


  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Top Navigation */}
      <header className="bg-white shadow-sm sticky top-0 z-40">
        <div className="max-w-7xl mx-auto lg:px-8">
          {/* Mobile Header */}
          <div className="max-w-md mx-auto lg:hidden px-4 py-3 flex items-center justify-between">
            <button className="p-2 rounded-full hover:bg-gray-100 transition-colors">
              <Link href="/settings">
                <Menu className="w-5 h-5 text-gray-600" />
              </Link>
            </button>
            
            <Link href="/" className="flex items-center">
              <Image
                src="/logo.svg"
                alt="Herbalicious Logo"
                width={60}
                height={60}
                className="h-[40px] w-auto"
              />
            </Link>
            
            <Link href="/notifications" className="p-2 rounded-full hover:bg-gray-100 transition-colors relative">
              <Bell className="w-5 h-5 text-gray-600" />
              {isClient && session?.user && unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {unreadCount > 99 ? '99+' : unreadCount}
                </span>
              )}
            </Link>
          </div>

          {/* Desktop Header */}
          <div className="hidden lg:flex px-4 py-3 items-center justify-between">
            {/* Logo - Left */}
            <Link href="/" className="flex items-center">
              <Image
                src="/logo.svg"
                alt="Herbalicious Logo"
                width={60}
                height={60}
                className="h-[60px] w-auto"
              />
            </Link>
            
            {/* Navigation - Center */}
            <nav className="flex items-center space-x-8 absolute left-1/2 transform -translate-x-1/2">
              <Link href="/" className="text-gray-600 hover:text-green-600 font-medium transition-colors">
                Home
              </Link>
              <Link href="/shop" className="text-gray-600 hover:text-green-600 font-medium transition-colors">
                Shop
              </Link>
              <Link href="/about" className="text-gray-600 hover:text-green-600 font-medium transition-colors">
                About
              </Link>
              <Link href="/contact" className="text-gray-600 hover:text-green-600 font-medium transition-colors">
                Contact
              </Link>
            </nav>
            
            {/* Actions - Right */}
            <div className="flex items-center space-x-2">
            <Link href="/notifications" className="p-2 rounded-full hover:bg-gray-100 transition-colors relative">
              <Bell className="w-5 h-5 text-gray-600" />
              {isClient && session?.user && unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {unreadCount > 99 ? '99+' : unreadCount}
                </span>
              )}
            </Link>
              <Link href="/cart" className="relative p-2 rounded-full hover:bg-gray-100 transition-colors">
                <ShoppingCart className="w-5 h-5 text-gray-600" />
                {isClient && state.itemCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {state.itemCount}
                  </span>
                )}
              </Link>
              <Link href="/profile" className="p-2 rounded-full hover:bg-gray-100 transition-colors">
                <User className="w-5 h-5 text-gray-600" />
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 pb-20 lg:pb-8">
        <div className="max-w-7xl mx-auto lg:px-8">
          <div className="max-w-md mx-auto lg:max-w-none">
            {children}
          </div>
        </div>
      </main>

      {/* Bottom Navigation - Mobile Only */}
      <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 lg:hidden">
        <div className="max-w-md mx-auto px-4 py-2">
          <div className="flex justify-around">
            <Link
              href="/"
              className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
                isActive('/') 
                  ? 'text-green-600 bg-green-50' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Home className="w-6 h-6 mb-1" />
              <span className="text-xs font-medium">Home</span>
            </Link>
            
            <Link
              href="/shop"
              className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
                isActive('/shop') 
                  ? 'text-green-600 bg-green-50' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <ShoppingBag className="w-6 h-6 mb-1" />
              <span className="text-xs font-medium">Shop</span>
            </Link>
            
            <Link
              href="/cart"
              className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors relative ${
                isActive('/cart')
                  ? 'text-green-600 bg-green-50'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <ShoppingCart className="w-6 h-6 mb-1" />
              {isClient && state.itemCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {state.itemCount}
                </span>
              )}
              <span className="text-xs font-medium">Cart</span>
            </Link>
            
            <Link
              href="/profile"
              className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
                isActive('/profile') 
                  ? 'text-green-600 bg-green-50' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <User className="w-6 h-6 mb-1" />
              <span className="text-xs font-medium">Profile</span>
            </Link>
          </div>
        </div>
      </nav>
    </div>
  );
};

export default Layout;