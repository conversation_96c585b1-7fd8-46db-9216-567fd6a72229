import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../lib/auth';
import { prisma } from '../../lib/db';
import { handleApiError, ValidationError, AuthenticationError, AppError, asyncHandler } from '../../lib/errors';
import { logger } from '../../lib/logger';
import { withRateLimit, generalLimiter } from '../../lib/rate-limit';
import { orderNotifications } from '../../lib/notification-helpers';

const getOrdersSchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('10'),
  status: z.enum(['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELLED', 'REFUNDED']).optional(),
  paymentStatus: z.enum(['PENDING', 'PAID', 'FAILED', 'REFUNDED']).optional()
});

const createOrderSchema = z.object({
  cartItems: z.array(z.object({
    productId: z.string(),
    quantity: z.number().min(1),
    price: z.number().min(0)
  })),
  shippingAddress: z.object({
    firstName: z.string().min(1),
    lastName: z.string().min(1),
    address1: z.string().min(1),
    address2: z.string().optional(),
    city: z.string().min(1),
    state: z.string().min(1),
    postalCode: z.string().min(1),
    country: z.string().min(1),
    phone: z.string().min(1)
  }),
  totalAmount: z.number().min(1),
  paymentMethod: z.enum(['ONLINE', 'COD']),
  appliedCoupons: z.array(z.object({
    coupon: z.object({
      id: z.string(),
      code: z.string(),
      name: z.string()
    }),
    discountAmount: z.number().min(0)
  })).optional().default([])
});

// GET /api/orders - Get user's orders or all orders (admin)
export const GET = asyncHandler(async (request: NextRequest) => {
  logger.apiRequest('GET', '/api/orders');

  // Apply rate limiting
  await withRateLimit(request, generalLimiter, 30);

  // Check authentication
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new AuthenticationError('Authentication required');
  }

  const { searchParams } = new URL(request.url);
  const queryParams = Object.fromEntries(searchParams.entries());
  const validatedParams = getOrdersSchema.parse(queryParams);

  const page = parseInt(validatedParams.page);
  const limit = parseInt(validatedParams.limit);
  const offset = (page - 1) * limit;

  const isAdmin = session.user.role === 'ADMIN';

  try {
    // Build where clause
    const whereClause: any = {};
    
    // Non-admin users can only see their own orders
    if (!isAdmin) {
      whereClause.userId = session.user.id;
    }

    // Add status filters if provided
    if (validatedParams.status) {
      whereClause.status = validatedParams.status;
    }
    if (validatedParams.paymentStatus) {
      whereClause.paymentStatus = validatedParams.paymentStatus;
    }

    // Get orders with pagination
    const [orders, totalCount] = await Promise.all([
      prisma.order.findMany({
        where: whereClause,
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  price: true
                }
              }
            }
          },
          address: true,
          user: isAdmin ? {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true
            }
          } : false
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip: offset,
        take: limit
      }),
      prisma.order.count({ where: whereClause })
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    logger.info('Orders retrieved successfully', {
      userId: session.user.id,
      isAdmin,
      count: orders.length,
      totalCount,
      page,
      filters: { status: validatedParams.status, paymentStatus: validatedParams.paymentStatus }
    });

    return NextResponse.json({
      success: true,
      orders,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    logger.error('Failed to retrieve orders', error as Error);
    throw error;
  }
});

// POST /api/orders - Create new order (for COD)
export const POST = asyncHandler(async (request: NextRequest) => {
  logger.apiRequest('POST', '/api/orders');

  // Apply rate limiting
  await withRateLimit(request, generalLimiter, 10);

  // Check authentication
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new AuthenticationError('Authentication required');
  }

  const body = await request.json();
  const validatedData = createOrderSchema.parse(body);
  const { cartItems, shippingAddress, totalAmount, paymentMethod, appliedCoupons } = validatedData;

  logger.info('Creating COD order', {
    userId: session.user.id,
    totalAmount,
    itemCount: cartItems.length,
    paymentMethod
  });

  // Validate cart items and calculate total
  let calculatedTotal = 0;
  const orderItems = [];

  for (const item of cartItems) {
    // Verify product exists and price is correct
    const product = await prisma.product.findUnique({
      where: { id: item.productId }
    });

    if (!product) {
      throw new ValidationError(`Product ${item.productId} not found`);
    }

    const itemPrice = product.price || 0;

    // Verify price matches
    if (Math.abs(itemPrice - item.price) > 0.01) {
      throw new ValidationError(`Price mismatch for product ${item.productId}`);
    }

    const itemTotal = itemPrice * item.quantity;
    calculatedTotal += itemTotal;
    
    orderItems.push({
      productId: item.productId,
      quantity: item.quantity,
      price: itemPrice,
      total: itemTotal
    });
  }

  // Calculate coupon discount
  const totalDiscount = appliedCoupons.reduce((sum, coupon) => sum + coupon.discountAmount, 0);
  const subtotal = calculatedTotal;
  const expectedTotal = subtotal - totalDiscount;

  // Add COD charges for COD orders
  let finalTotal = expectedTotal;
  if (paymentMethod === 'COD') {
    finalTotal += 50; // COD charges
  }

  // Verify total amount
  if (Math.abs(finalTotal - totalAmount) > 0.01) {
    throw new ValidationError(`Total amount mismatch. Expected: ${finalTotal}, Received: ${totalAmount}`);
  }

  // Generate order number
  const orderNumber = Math.random().toString(36).substring(2, 8).toUpperCase();

  try {
    // Check if this address already exists for the user
    const existingAddress = await prisma.address.findFirst({
      where: {
        userId: session.user.id,
        firstName: shippingAddress.firstName,
        lastName: shippingAddress.lastName,
        address1: shippingAddress.address1,
        city: shippingAddress.city,
        state: shippingAddress.state,
        postalCode: shippingAddress.postalCode,
        country: shippingAddress.country
      }
    });

    // Create or use existing address in user's address book
    let userAddress;
    if (!existingAddress) {
      userAddress = await prisma.address.create({
        data: {
          userId: session.user.id,
          firstName: shippingAddress.firstName,
          lastName: shippingAddress.lastName,
          address1: shippingAddress.address1,
          address2: shippingAddress.address2,
          city: shippingAddress.city,
          state: shippingAddress.state,
          postalCode: shippingAddress.postalCode,
          country: shippingAddress.country,
          phone: shippingAddress.phone
        }
      });
    }

    // Create order in database
    const order = await prisma.order.create({
      data: {
        orderNumber: orderNumber,
        userId: session.user.id,
        status: 'CONFIRMED', // COD orders are confirmed immediately
        paymentStatus: 'PENDING', // Will be marked as PAID when delivered
        paymentMethod: paymentMethod,
        subtotal: subtotal,
        couponDiscount: totalDiscount,
        total: finalTotal,
        currency: 'INR',
        notes: `Order created via ${paymentMethod}${paymentMethod === 'COD' ? ' with ₹50 COD charges' : ''}${appliedCoupons.length > 0 ? ` | Coupons: ${appliedCoupons.map(c => c.coupon.code).join(', ')}` : ''}`,
        address: {
          create: {
            firstName: shippingAddress.firstName,
            lastName: shippingAddress.lastName,
            address1: shippingAddress.address1,
            address2: shippingAddress.address2,
            city: shippingAddress.city,
            state: shippingAddress.state,
            postalCode: shippingAddress.postalCode,
            country: shippingAddress.country,
            phone: shippingAddress.phone
          }
        },
        items: {
          create: orderItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            price: item.price,
            total: item.total
          }))
        },
        // Create coupon usage records
        ...(appliedCoupons.length > 0 && {
          couponUsages: {
            create: appliedCoupons.map(appliedCoupon => ({
              couponId: appliedCoupon.coupon.id,
              userId: session.user.id,
              usedAt: new Date()
            }))
          }
        })
      },
      include: {
        items: {
          include: {
            product: true
          }
        },
        address: true
      }
    });

    logger.info('COD order created successfully', {
      orderId: order.id,
      orderNumber: order.orderNumber,
      amount: finalTotal,
      userId: session.user.id,
      paymentMethod: paymentMethod
    });

    // Send order placed notification
    try {
      await orderNotifications.orderPlaced(session.user.id, {
        orderId: order.id,
        orderNumber: order.orderNumber,
        total: finalTotal,
        currency: 'INR',
        itemCount: cartItems.length,
      });

      logger.info('Order placed notification sent', {
        orderId: order.id,
        userId: session.user.id
      });
    } catch (notificationError) {
      logger.error('Failed to send order placed notification', notificationError as Error);
      // Don't fail the order creation if notification fails
    }

    return NextResponse.json({
      success: true,
      order: {
        id: order.id,
        orderNumber: order.orderNumber,
        total: finalTotal,
        currency: 'INR'
      }
    });

  } catch (error) {
    logger.error('Failed to create COD order', error as Error);
    throw new AppError('Failed to create order', 500);
  }
});