'use client';

import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Search, X, Check } from 'lucide-react';

interface Product {
  id: string;
  name: string;
  slug?: string;
  price?: number;
  images?: Array<{ url: string; alt?: string }>;
}

interface SearchableProductDropdownProps {
  products: Product[];
  selectedProductId: string | null;
  onProductSelect: (productId: string | null) => void;
  placeholder?: string;
  className?: string;
}

const SearchableProductDropdown: React.FC<SearchableProductDropdownProps> = ({
  products,
  selectedProductId,
  onProductSelect,
  placeholder = "Search and select a product...",
  className = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Filter products based on search term
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (product.slug && product.slug.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Get selected product
  const selectedProduct = products.find(p => p.id === selectedProductId);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus input when dropdown opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  const handleProductSelect = (productId: string | null) => {
    onProductSelect(productId);
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleToggleDropdown = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setSearchTerm('');
    }
  };

  const clearSelection = (e: React.MouseEvent) => {
    e.stopPropagation();
    onProductSelect(null);
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Dropdown Trigger */}
      <div
        onClick={handleToggleDropdown}
        className="w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus-within:border-indigo-500 focus-within:ring-1 focus-within:ring-indigo-500 cursor-pointer"
      >
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            {selectedProduct ? (
              <div className="flex items-center space-x-2">
                {selectedProduct.images?.[0] && (
                  <img
                    src={selectedProduct.images[0].url}
                    alt={selectedProduct.images[0].alt || selectedProduct.name}
                    className="w-6 h-6 rounded object-cover flex-shrink-0"
                  />
                )}
                <span className="text-sm text-gray-900 truncate">
                  {selectedProduct.name}
                </span>
              </div>
            ) : (
              <span className="text-sm text-gray-500">{placeholder}</span>
            )}
          </div>
          <div className="flex items-center space-x-1">
            {selectedProduct && (
              <button
                onClick={clearSelection}
                className="p-1 hover:bg-gray-100 rounded transition-colors"
                title="Clear selection"
              >
                <X className="w-4 h-4 text-gray-400" />
              </button>
            )}
            <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          </div>
        </div>
      </div>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-hidden">
          {/* Search Input */}
          <div className="p-3 border-b border-gray-200">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                ref={inputRef}
                type="text"
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-9 pr-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
          </div>

          {/* Options List */}
          <div className="max-h-60 overflow-y-auto">
            {/* None option */}
            <div
              onClick={() => handleProductSelect(null)}
              className={`px-3 py-2 cursor-pointer hover:bg-gray-50 flex items-center justify-between ${
                !selectedProductId ? 'bg-indigo-50 text-indigo-700' : 'text-gray-900'
              }`}
            >
              <span className="text-sm">None - Use automatic selection</span>
              {!selectedProductId && <Check className="w-4 h-4" />}
            </div>

            {/* Product options */}
            {filteredProducts.length > 0 ? (
              filteredProducts.map((product) => (
                <div
                  key={product.id}
                  onClick={() => handleProductSelect(product.id)}
                  className={`px-3 py-2 cursor-pointer hover:bg-gray-50 flex items-center justify-between ${
                    selectedProductId === product.id ? 'bg-indigo-50 text-indigo-700' : 'text-gray-900'
                  }`}
                >
                  <div className="flex items-center space-x-2 min-w-0 flex-1">
                    {product.images?.[0] && (
                      <img
                        src={product.images[0].url}
                        alt={product.images[0].alt || product.name}
                        className="w-8 h-8 rounded object-cover flex-shrink-0"
                      />
                    )}
                    <div className="min-w-0 flex-1">
                      <div className="text-sm font-medium truncate">{product.name}</div>
                      {product.price && (
                        <div className="text-xs text-gray-500">₹{product.price}</div>
                      )}
                    </div>
                  </div>
                  {selectedProductId === product.id && <Check className="w-4 h-4 flex-shrink-0" />}
                </div>
              ))
            ) : (
              <div className="px-3 py-4 text-sm text-gray-500 text-center">
                No products found matching "{searchTerm}"
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchableProductDropdown;
