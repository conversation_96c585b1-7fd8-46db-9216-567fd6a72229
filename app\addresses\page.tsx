'use client'

import React from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, MapPin, Plus, Edit, Trash2 } from 'lucide-react';

const Addresses: React.FC = () => {
  const router = useRouter();

  // Mock addresses - in real app, fetch from API
  const addresses = [
    {
      id: '1',
      firstName: '<PERSON>',
      lastName: 'Doe',
      company: 'Tech Corp',
      address1: '123 Main Street',
      address2: 'Apt 4B',
      city: 'New York',
      state: 'NY',
      postalCode: '10001',
      country: 'India',
      phone: '+91 99878 10707',
      isDefault: true,
    },
    {
      id: '2',
      firstName: '<PERSON>',
      lastName: 'Doe',
      company: '',
      address1: '456 Oak Avenue',
      address2: '',
      city: 'Brooklyn',
      state: 'NY',
      postalCode: '11201',
      country: 'India',
      phone: '+91 99878 10707',
      isDefault: false,
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button
            onClick={() => router.back()}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Back</span>
          </button>
        </div>

        <h1 className="text-4xl font-bold text-gray-800 mb-8">Shipping Addresses</h1>

        <div className="space-y-6">
          {/* Add New Address */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <button className="w-full flex items-center justify-center space-x-3 p-4 border-2 border-dashed border-gray-300 rounded-xl hover:border-green-400 hover:bg-green-50 transition-colors">
              <Plus className="w-6 h-6 text-gray-400" />
              <span className="text-gray-600 font-medium">Add New Address</span>
            </button>
          </div>

          {/* Existing Addresses */}
          {addresses.map((address) => (
            <div key={address.id} className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mt-1">
                    <MapPin className="w-6 h-6 text-gray-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-semibold text-gray-800">
                        {address.firstName} {address.lastName}
                      </h3>
                      {address.isDefault && (
                        <span className="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full">
                          Default
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      {address.company && <p className="font-medium">{address.company}</p>}
                      <p>{address.address1}</p>
                      {address.address2 && <p>{address.address2}</p>}
                      <p>{address.city}, {address.state} {address.postalCode}</p>
                      <p>{address.country}</p>
                      {address.phone && <p className="font-medium">{address.phone}</p>}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {!address.isDefault && (
                    <button className="px-4 py-2 text-sm font-medium text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                      Set as Default
                    </button>
                  )}
                  <button className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                    <Edit className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}

          {/* Info Notice */}
          <div className="bg-blue-50 rounded-2xl p-6 border border-blue-100">
            <h3 className="font-semibold text-blue-900 mb-2">Address Information</h3>
            <p className="text-blue-800 text-sm">
              Your default address will be automatically selected during checkout. You can always change it before placing an order.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Addresses;