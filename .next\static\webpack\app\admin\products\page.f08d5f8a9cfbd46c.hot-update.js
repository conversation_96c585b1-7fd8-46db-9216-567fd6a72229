"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/page",{

/***/ "(app-pages-browser)/./app/admin/products/page.tsx":
/*!*************************************!*\
  !*** ./app/admin/products/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,FileText,Filter,HelpCircle,Loader2,Plus,Search,Settings,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _lib_currency__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/currency */ \"(app-pages-browser)/./app/lib/currency.ts\");\n/* harmony import */ var _components_admin_FAQManagementModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/admin/FAQManagementModal */ \"(app-pages-browser)/./app/components/admin/FAQManagementModal.tsx\");\n/* harmony import */ var _components_admin_VariationsManagementModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/admin/VariationsManagementModal */ \"(app-pages-browser)/./app/components/admin/VariationsManagementModal.tsx\");\n/* harmony import */ var _components_admin_ImageGalleryManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/admin/ImageGalleryManager */ \"(app-pages-browser)/./app/components/admin/ImageGalleryManager.tsx\");\n/* harmony import */ var _components_admin_CategoryMultiSelect__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/admin/CategoryMultiSelect */ \"(app-pages-browser)/./app/components/admin/CategoryMultiSelect.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\nconst ProductsPage = ()=>{\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProduct, setEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [importing, setImporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [bulkLoading, setBulkLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showFAQModal, setShowFAQModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProductForFAQ, setSelectedProductForFAQ] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showVariationsModal, setShowVariationsModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProductForVariations, setSelectedProductForVariations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch products and categories\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProducts();\n        fetchCategories();\n    }, []);\n    const fetchProducts = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/products?limit=1000\", {\n                headers: {\n                    \"x-admin-request\": \"true\"\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setProducts(data.data);\n            } else {\n                setError(\"Failed to fetch products\");\n            }\n        } catch (error) {\n            if (true) {\n                console.error(\"Error fetching products:\", error);\n            }\n            setError(\"Failed to fetch products\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchCategories = async ()=>{\n        try {\n            const response = await fetch(\"/api/categories\");\n            const data = await response.json();\n            if (data.success) {\n                setCategories(data.data);\n            }\n        } catch (error) {\n            if (true) {\n                console.error(\"Error fetching categories:\", error);\n            }\n        }\n    };\n    // CSV Template Download\n    const handleDownloadTemplate = ()=>{\n        const csvContent = [\n            \"Name,Slug,Description,Short Description,Price,Compare Price,Category,Categories,Featured,Active,Variations\",\n            'Sample Product,sample-product,\"A great product description\",\"Short description\",99.99,149.99,Skincare,\"Skincare;Face Care\",yes,yes,\"[{\"\"name\"\":\"\"Size\"\",\"\"value\"\":\"\"50ml\"\",\"\"price\"\":99.99}]\"',\n            \"# Instructions:\",\n            \"# - Name: Required product name\",\n            \"# - Slug: SEO-friendly URL (auto-generated if empty)\",\n            \"# - Price: Required base price in rupees\",\n            \"# - Category: Single category name\",\n            \"# - Categories: Multiple categories separated by semicolons\",\n            \"# - Featured: yes/no\",\n            \"# - Active: yes/no (defaults to yes)\",\n            \"# - Variations: JSON array of variations with name, value, and price\"\n        ].join(\"\\n\");\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: \"text/csv\"\n        });\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"products_template.csv\";\n        a.click();\n        window.URL.revokeObjectURL(url);\n    };\n    // JSON Template Download\n    const handleDownloadJSONTemplate = ()=>{\n        const jsonTemplate = {\n            products: [\n                {\n                    name: \"Sample Product\",\n                    slug: \"sample-product\",\n                    description: \"A detailed product description\",\n                    shortDescription: \"Short description\",\n                    price: 99.99,\n                    comparePrice: 149.99,\n                    categoryNames: [\n                        \"Skincare\",\n                        \"Face Care\"\n                    ],\n                    isFeatured: true,\n                    isActive: true,\n                    variations: [\n                        {\n                            name: \"Size\",\n                            value: \"50ml\",\n                            price: 99.99\n                        },\n                        {\n                            name: \"Size\",\n                            value: \"100ml\",\n                            price: 179.99\n                        }\n                    ]\n                }\n            ]\n        };\n        const blob = new Blob([\n            JSON.stringify(jsonTemplate, null, 2)\n        ], {\n            type: \"application/json\"\n        });\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"products_template.json\";\n        a.click();\n        window.URL.revokeObjectURL(url);\n    };\n    // Import functionality\n    const handleImport = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".csv,.json\";\n        input.onchange = async (e)=>{\n            var _e_target_files;\n            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n            if (!file) return;\n            try {\n                setImporting(true);\n                const text = await file.text();\n                let importData = [];\n                if (file.name.toLowerCase().endsWith(\".json\")) {\n                    // Handle JSON import\n                    try {\n                        const jsonData = JSON.parse(text);\n                        // Check if it's the expected format with products array\n                        if (jsonData.products && Array.isArray(jsonData.products)) {\n                            importData = jsonData.products;\n                        } else if (Array.isArray(jsonData)) {\n                            // Handle direct array of products\n                            importData = jsonData;\n                        } else {\n                            alert('Invalid JSON format. Expected format: {\"products\": [...]} or direct array of products.');\n                            return;\n                        }\n                        // Validate each product in JSON\n                        const validProducts = [];\n                        for (const product of importData){\n                            if (!product.name) {\n                                if (true) {\n                                    console.warn(\"Skipping product without name:\", product);\n                                }\n                                continue;\n                            }\n                            // Handle both single category and multiple categories\n                            const categoryNames = [];\n                            if (product.category) {\n                                categoryNames.push(product.category);\n                            }\n                            if (product.categoryNames && Array.isArray(product.categoryNames)) {\n                                categoryNames.push(...product.categoryNames);\n                            }\n                            if (categoryNames.length === 0) {\n                                if (true) {\n                                    console.warn(\"Skipping product without categories:\", product.name);\n                                }\n                                continue;\n                            }\n                            // Check if price is valid or if variations provide pricing\n                            const hasValidPrice = product.price && product.price > 0 || product.variations && product.variations.length > 0;\n                            if (!hasValidPrice) {\n                                if (true) {\n                                    console.warn(\"Skipping product without valid price or variations:\", product.name);\n                                }\n                                continue;\n                            }\n                            validProducts.push({\n                                ...product,\n                                categoryNames,\n                                slug: product.slug || (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.generateSlug)(product.name),\n                                isFeatured: Boolean(product.isFeatured),\n                                isActive: product.isActive !== false\n                            });\n                        }\n                        importData = validProducts;\n                    } catch (jsonError) {\n                        alert(\"Invalid JSON file. Please check the file format.\");\n                        return;\n                    }\n                } else {\n                    // Handle CSV import\n                    const lines = text.split(\"\\n\").filter((line)=>line.trim() && !line.trim().startsWith(\"#\"));\n                    const headers = lines[0].split(\",\").map((h)=>h.trim().replace(/\"/g, \"\"));\n                    if (true) {\n                        console.log(\"CSV Headers found:\", headers);\n                    }\n                    // Validate headers\n                    const requiredHeaders = [\n                        \"Name\"\n                    ];\n                    const missingHeaders = requiredHeaders.filter((header)=>!headers.some((h)=>h.toLowerCase().includes(header.toLowerCase())));\n                    if (missingHeaders.length > 0) {\n                        alert(\"Missing required columns: \".concat(missingHeaders.join(\", \")));\n                        return;\n                    }\n                    // Check if we have either Category or Categories column\n                    const hasCategoryColumn = headers.some((h)=>h.toLowerCase().includes(\"category\"));\n                    if (!hasCategoryColumn) {\n                        alert('Missing category information. Please include either \"Category\" or \"Categories\" column.');\n                        return;\n                    }\n                    for(let i = 1; i < lines.length; i++){\n                        var _values_featuredIndex, _values_activeIndex;\n                        const line = lines[i].trim();\n                        if (!line) continue;\n                        const values = line.split(\",\").map((v)=>v.trim().replace(/\"/g, \"\"));\n                        // Find header indices more robustly\n                        const nameIndex = headers.findIndex((h)=>h.toLowerCase().includes(\"name\"));\n                        const slugIndex = headers.findIndex((h)=>h.toLowerCase().includes(\"slug\"));\n                        const variationsIndex = headers.findIndex((h)=>h.toLowerCase().includes(\"variations\"));\n                        const productName = nameIndex >= 0 ? values[nameIndex] || \"\" : \"\";\n                        const productSlug = slugIndex >= 0 ? values[slugIndex] || \"\" : \"\";\n                        const variationsStr = variationsIndex >= 0 ? values[variationsIndex] || \"\" : \"\";\n                        // Parse variations from JSON string\n                        let variations = [];\n                        if (variationsStr) {\n                            try {\n                                variations = JSON.parse(variationsStr);\n                            } catch (e) {\n                                if (true) {\n                                    console.warn(\"Invalid variations JSON:\", variationsStr);\n                                }\n                            }\n                        }\n                        // Parse categories\n                        const categoryNames = [];\n                        // Check for multiple categories in \"Categories\" column\n                        const categoriesIndex = headers.findIndex((h)=>h.toLowerCase() === \"categories\");\n                        const categoriesValue = categoriesIndex >= 0 ? values[categoriesIndex] || \"\" : \"\";\n                        if (categoriesValue) {\n                            // Split by semicolon and clean up\n                            categoryNames.push(...categoriesValue.split(\";\").map((cat)=>cat.trim()).filter((cat)=>cat));\n                        }\n                        // Fallback to single category column if no multiple categories\n                        if (categoryNames.length === 0) {\n                            const categoryIndex = headers.findIndex((h)=>h.toLowerCase() === \"category\");\n                            const singleCategory = categoryIndex >= 0 ? values[categoryIndex] || \"\" : \"\";\n                            if (singleCategory) {\n                                categoryNames.push(singleCategory);\n                            }\n                        }\n                        const priceIndex = headers.findIndex((h)=>h.toLowerCase() === \"price\");\n                        const priceStr = priceIndex >= 0 ? values[priceIndex] || \"\" : \"\";\n                        const price = parseFloat(priceStr) || 0;\n                        // Check if price is valid or if variations provide pricing\n                        const hasValidPrice = price >= 0 || variations && variations.length > 0;\n                        // Find remaining field indices\n                        const descriptionIndex = headers.findIndex((h)=>h.toLowerCase() === \"description\");\n                        const shortDescriptionIndex = headers.findIndex((h)=>h.toLowerCase().includes(\"short description\"));\n                        const comparePriceIndex = headers.findIndex((h)=>h.toLowerCase().includes(\"compare price\"));\n                        const featuredIndex = headers.findIndex((h)=>h.toLowerCase() === \"featured\");\n                        const activeIndex = headers.findIndex((h)=>h.toLowerCase() === \"active\");\n                        const product = {\n                            name: productName,\n                            slug: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.validateSlug)(productSlug, productName),\n                            description: descriptionIndex >= 0 ? values[descriptionIndex] || \"\" : \"\",\n                            shortDescription: shortDescriptionIndex >= 0 ? values[shortDescriptionIndex] || \"\" : \"\",\n                            price: price,\n                            comparePrice: comparePriceIndex >= 0 ? parseFloat(values[comparePriceIndex] || \"0\") || null : null,\n                            categoryNames: categoryNames,\n                            isFeatured: featuredIndex >= 0 ? ((_values_featuredIndex = values[featuredIndex]) === null || _values_featuredIndex === void 0 ? void 0 : _values_featuredIndex.toLowerCase()) === \"yes\" : false,\n                            isActive: activeIndex >= 0 ? ((_values_activeIndex = values[activeIndex]) === null || _values_activeIndex === void 0 ? void 0 : _values_activeIndex.toLowerCase()) !== \"no\" : true,\n                            variations: variations\n                        };\n                        // Allow products with name, category, and valid price\n                        const hasValidCategory = categoryNames.length > 0;\n                        if (product.name && hasValidCategory && hasValidPrice) {\n                            importData.push(product);\n                        }\n                    }\n                }\n                if (importData.length === 0) {\n                    alert('No valid products found in the file. Please ensure:\\n\\n1. Each product has a Name\\n2. Each product has at least one Category\\n3. Price must be provided OR variations must have prices\\n\\nFor JSON: Use format {\"products\": [...]} or direct array\\nFor CSV: Check the CSV template for the correct format.');\n                    return;\n                }\n                // Send import data to API\n                const response = await fetch(\"/api/products/import\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        products: importData\n                    })\n                });\n                const result = await response.json();\n                if (result.success) {\n                    const { success, failed, errors } = result.data;\n                    let message = \"Import completed!\\n✅ \".concat(success, \" products imported successfully\");\n                    if (failed > 0) {\n                        message += \"\\n❌ \".concat(failed, \" products failed\");\n                        if (errors.length > 0) {\n                            message += \"\\n\\nErrors:\\n\".concat(errors.slice(0, 5).join(\"\\n\"));\n                            if (errors.length > 5) {\n                                message += \"\\n... and \".concat(errors.length - 5, \" more errors\");\n                            }\n                        }\n                    }\n                    alert(message);\n                    // Refresh the products list\n                    if (success > 0) {\n                        fetchProducts();\n                    }\n                } else {\n                    alert(\"Import failed: \".concat(result.error));\n                }\n            } catch (error) {\n                if (true) {\n                    console.error(\"Import error:\", error);\n                }\n                alert(\"Failed to import products. Please check the file format.\");\n            } finally{\n                setImporting(false);\n            }\n        };\n        input.click();\n    };\n    // Export functionality\n    const handleExport = async function() {\n        let format = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"json\";\n        try {\n            const response = await fetch(\"/api/admin/products/export?format=\".concat(format));\n            if (!response.ok) {\n                throw new Error(\"Failed to export products\");\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            // Generate filename with current date\n            const extension = format === \"csv\" ? \"csv\" : \"json\";\n            const filename = \"products-backup-\".concat(new Date().toISOString().split(\"T\")[0], \".\").concat(extension);\n            a.download = filename;\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            window.URL.revokeObjectURL(url);\n        } catch (error) {\n            console.error(\"Error exporting products:\", error);\n            alert(\"Failed to export products. Please try again.\");\n        }\n    };\n    // Cleanup functionality\n    const handleCleanup = async ()=>{\n        if (!confirm(\"Are you sure you want to delete all notifications, reviews, and orders? This action cannot be undone.\")) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/admin/cleanup\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const result = await response.json();\n            if (result.success) {\n                alert(\"Cleanup completed successfully!\\n\\nDeleted:\\n- \".concat(result.deletedCounts.notifications, \" notifications\\n- \").concat(result.deletedCounts.reviews, \" reviews\\n- \").concat(result.deletedCounts.orders, \" orders\\n- \").concat(result.deletedCounts.orderItems, \" order items\\n- \").concat(result.deletedCounts.orderAddresses, \" order addresses\\n- \").concat(result.deletedCounts.couponUsages, \" coupon usages\"));\n            } else {\n                alert(\"Cleanup failed: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error(\"Error during cleanup:\", error);\n            alert(\"Failed to cleanup database. Please try again.\");\n        }\n    };\n    // Fixed filteredProducts function\n    const filteredProducts = products.filter((product)=>{\n        var _product_category, _product_productCategories;\n        if (!searchTerm) return true;\n        const nameMatch = product.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const primaryCategoryMatch = (_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const multipleCategoriesMatch = (_product_productCategories = product.productCategories) === null || _product_productCategories === void 0 ? void 0 : _product_productCategories.some((pc)=>pc.category.name.toLowerCase().includes(searchTerm.toLowerCase()));\n        return nameMatch || primaryCategoryMatch || multipleCategoriesMatch;\n    });\n    const handleSelectProduct = (productId)=>{\n        setSelectedProducts((prev)=>prev.includes(productId) ? prev.filter((id)=>id !== productId) : [\n                ...prev,\n                productId\n            ]);\n    };\n    const handleSelectAll = ()=>{\n        setSelectedProducts(selectedProducts.length === filteredProducts.length ? [] : filteredProducts.map((p)=>p.id));\n    };\n    const handleEditProduct = (product)=>{\n        setEditingProduct(product);\n        setShowEditModal(true);\n    };\n    const handleDeleteProduct = async (productId)=>{\n        if (confirm(\"Are you sure you want to delete this product?\")) {\n            try {\n                const response = await fetch(\"/api/products/\".concat(productId), {\n                    method: \"DELETE\"\n                });\n                const result = await response.json();\n                if (response.ok) {\n                    if (result.type === \"soft_delete\") {\n                        alert(\"Product deactivated: \".concat(result.message));\n                    } else {\n                        alert(\"Product deleted successfully\");\n                    }\n                    setProducts((prev)=>prev.filter((p)=>p.id !== productId));\n                } else {\n                    alert(\"Failed to delete product: \".concat(result.error || \"Unknown error\"));\n                }\n            } catch (error) {\n                if (true) {\n                    console.error(\"Error deleting product:\", error);\n                }\n                alert(\"Failed to delete product\");\n            }\n        }\n    };\n    // Bulk operations\n    const handleBulkAction = async (action)=>{\n        if (selectedProducts.length === 0) {\n            alert(\"Please select products first\");\n            return;\n        }\n        const actionMessages = {\n            delete: \"Are you sure you want to delete the selected products? This action cannot be undone.\",\n            activate: \"Are you sure you want to activate the selected products?\",\n            deactivate: \"Are you sure you want to deactivate the selected products?\",\n            feature: \"Are you sure you want to feature the selected products?\",\n            unfeature: \"Are you sure you want to unfeature the selected products?\"\n        };\n        if (!confirm(actionMessages[action])) {\n            return;\n        }\n        try {\n            setBulkLoading(true);\n            const response = await fetch(\"/api/products/bulk\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action,\n                    productIds: selectedProducts\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                alert(result.message);\n                setSelectedProducts([]);\n                fetchProducts(); // Refresh the products list\n            } else {\n                alert(\"Failed to \".concat(action, \" products: \").concat(result.error));\n            }\n        } catch (error) {\n            console.error(\"Error in bulk \".concat(action, \":\"), error);\n            alert(\"Failed to \".concat(action, \" products\"));\n        } finally{\n            setBulkLoading(false);\n        }\n    };\n    const handleManageFAQs = (product)=>{\n        setSelectedProductForFAQ(product);\n        setShowFAQModal(true);\n    };\n    const handleManageVariations = (product)=>{\n        setSelectedProductForVariations(product);\n        setShowVariationsModal(true);\n    };\n    const toggleFeatured = async (productId)=>{\n        try {\n            const product = products.find((p)=>p.id === productId);\n            if (!product) return;\n            const response = await fetch(\"/api/products/\".concat(productId), {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    isFeatured: !product.isFeatured\n                })\n            });\n            if (response.ok) {\n                setProducts((prev)=>prev.map((p)=>p.id === productId ? {\n                            ...p,\n                            isFeatured: !p.isFeatured\n                        } : p));\n            }\n        } catch (error) {\n            if (true) {\n                console.error(\"Error updating product:\", error);\n            }\n        }\n    };\n    const getCategoryColor = (categoryName)=>{\n        const colors = {\n            \"Skincare\": \"bg-green-100 text-green-800\",\n            \"Hair Care\": \"bg-purple-100 text-purple-800\",\n            \"Body Care\": \"bg-blue-100 text-blue-800\",\n            \"cleanser\": \"bg-blue-100 text-blue-800\",\n            \"serum\": \"bg-purple-100 text-purple-800\",\n            \"moisturizer\": \"bg-green-100 text-green-800\",\n            \"mask\": \"bg-yellow-100 text-yellow-800\",\n            \"exfoliator\": \"bg-pink-100 text-pink-800\",\n            \"eye-care\": \"bg-indigo-100 text-indigo-800\"\n        };\n        return colors[categoryName] || \"bg-gray-100 text-gray-800\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Manage your skincare product catalog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 678,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 sm:mt-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddModal(true),\n                                className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 687,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Add Product\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 683,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 682,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 677,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 676,\n                columnNumber: 7\n            }, undefined),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-green-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 697,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 text-gray-600\",\n                        children: \"Loading products...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 698,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 696,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 705,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchProducts,\n                        className: \"mt-2 text-red-600 hover:text-red-700 underline\",\n                        children: \"Try again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 706,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 704,\n                columnNumber: 9\n            }, undefined),\n            !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 722,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search products...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 721,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleDownloadTemplate,\n                                                    className: \"p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                    title: \"Download CSV template\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-5 h-5 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleDownloadJSONTemplate,\n                                                    className: \"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium text-gray-600\",\n                                                    title: \"Download JSON template\",\n                                                    children: \"JSON\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleImport,\n                                            disabled: importing,\n                                            className: \"flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\",\n                                            title: \"Import products from CSV/JSON\",\n                                            children: [\n                                                importing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 761,\n                                                    columnNumber: 17\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                importing ? \"Importing...\" : \"Import\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleExport(\"json\"),\n                                            className: \"flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                            title: \"Export products as JSON\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 772,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                \"JSON\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 767,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleExport(\"csv\"),\n                                            className: \"flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                            title: \"Export products as CSV\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 780,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                \"CSV\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 775,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCleanup,\n                                            className: \"flex items-center px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n                                            title: \"Clean up notifications, reviews, and orders\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                \"Cleanup\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 736,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 719,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 718,\n                        columnNumber: 7\n                    }, undefined),\n                    selectedProducts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-blue-900\",\n                                            children: [\n                                                selectedProducts.length,\n                                                \" product\",\n                                                selectedProducts.length > 1 ? \"s\" : \"\",\n                                                \" selected\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 800,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedProducts([]),\n                                            className: \"text-blue-600 hover:text-blue-700 text-sm\",\n                                            children: \"Clear selection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 803,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 799,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleBulkAction(\"activate\"),\n                                            disabled: bulkLoading,\n                                            className: \"px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:opacity-50\",\n                                            children: \"Activate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleBulkAction(\"deactivate\"),\n                                            disabled: bulkLoading,\n                                            className: \"px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 disabled:opacity-50\",\n                                            children: \"Deactivate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 819,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleBulkAction(\"feature\"),\n                                            disabled: bulkLoading,\n                                            className: \"px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700 disabled:opacity-50\",\n                                            children: \"Feature\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 826,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleBulkAction(\"unfeature\"),\n                                            disabled: bulkLoading,\n                                            className: \"px-3 py-1 bg-orange-600 text-white text-sm rounded hover:bg-orange-700 disabled:opacity-50\",\n                                            children: \"Unfeature\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 833,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleBulkAction(\"delete\"),\n                                            disabled: bulkLoading,\n                                            className: \"px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 disabled:opacity-50 flex items-center\",\n                                            children: [\n                                                bulkLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 846,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 848,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Delete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 840,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 811,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 798,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 797,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: selectedProducts.length === filteredProducts.length && filteredProducts.length > 0,\n                                                        onChange: handleSelectAll,\n                                                        className: \"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 864,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 863,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Product\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 871,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 874,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Price\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 880,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 883,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 862,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 861,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: filteredProducts.map((product)=>{\n                                            var _product_images_, _product_images_1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: selectedProducts.includes(product.id),\n                                                            onChange: ()=>handleSelectProduct(product.id),\n                                                            className: \"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 895,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 894,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: ((_product_images_ = product.images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.url) || \"/images/default-product.jpg\",\n                                                                    alt: ((_product_images_1 = product.images[0]) === null || _product_images_1 === void 0 ? void 0 : _product_images_1.alt) || product.name,\n                                                                    className: \"w-12 h-12 rounded-lg object-cover mr-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: product.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 910,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: product.shortDescription\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 911,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 909,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 903,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1\",\n                                                            children: product.productCategories && product.productCategories.length > 0 ? product.productCategories.map((pc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-medium rounded-full \".concat(getCategoryColor(pc.category.name)),\n                                                                    children: pc.category.name\n                                                                }, pc.category.id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 920,\n                                                                    columnNumber: 27\n                                                                }, undefined)) : /* Fallback to primary category if no many-to-many categories */ product.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-medium rounded-full \".concat(getCategoryColor(product.category.name)),\n                                                                children: product.category.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 927,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 916,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 915,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 text-sm text-gray-900\",\n                                                        children: [\n                                                            product.variants && product.variants.length > 0 ? (()=>{\n                                                                const prices = product.variants.map((v)=>v.price).filter((p)=>typeof p === \"number\" && p > 0);\n                                                                if (prices.length === 0) return \"No pricing\";\n                                                                const min = Math.min(...prices);\n                                                                const max = Math.max(...prices);\n                                                                return min === max ? (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(min) : \"\".concat((0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(min), \" - \").concat((0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(max));\n                                                            })() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500\",\n                                                                children: \"No variations\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 944,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            product.comparePrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 line-through ml-2\",\n                                                                children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(product.comparePrice)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 947,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: product.variants && product.variants.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-600\",\n                                                                children: [\n                                                                    product.variants.length,\n                                                                    \" variations\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 25\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500\",\n                                                                children: \"No variations\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 957,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 953,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 952,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex px-2 py-1 text-xs font-medium rounded-full \".concat(product.isFeatured ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"),\n                                                            children: product.isFeatured ? \"Featured\" : \"Active\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 962,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 961,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleEditProduct(product),\n                                                                    className: \"p-1 text-gray-400 hover:text-green-600 transition-colors\",\n                                                                    title: \"Edit Product\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 975,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 970,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleManageFAQs(product),\n                                                                    className: \"p-1 text-gray-400 hover:text-blue-600 transition-colors\",\n                                                                    title: \"Manage FAQs\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 982,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 977,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleManageVariations(product),\n                                                                    className: \"p-1 text-gray-400 hover:text-purple-600 transition-colors\",\n                                                                    title: \"Manage Variations\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 989,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 984,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleDeleteProduct(product.id),\n                                                                    className: \"p-1 text-gray-400 hover:text-red-600 transition-colors\",\n                                                                    title: \"Delete Product\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_FileText_Filter_HelpCircle_Loader2_Plus_Search_Settings_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 996,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 991,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 969,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 968,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, product.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 893,\n                                                columnNumber: 17\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 891,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 860,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 859,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 858,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 text-sm text-gray-500\",\n                        children: [\n                            \"Showing \",\n                            filteredProducts.length,\n                            \" of \",\n                            products.length,\n                            \" products\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 1008,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true),\n            showAddModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductModal, {\n                isOpen: showAddModal,\n                onClose: ()=>setShowAddModal(false),\n                onSave: fetchProducts,\n                categories: categories\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 1016,\n                columnNumber: 9\n            }, undefined),\n            showEditModal && editingProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductModal, {\n                isOpen: showEditModal,\n                onClose: ()=>{\n                    setShowEditModal(false);\n                    setEditingProduct(null);\n                },\n                onSave: fetchProducts,\n                categories: categories,\n                product: editingProduct\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 1026,\n                columnNumber: 9\n            }, undefined),\n            showFAQModal && selectedProductForFAQ && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_FAQManagementModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showFAQModal,\n                onClose: ()=>{\n                    setShowFAQModal(false);\n                    setSelectedProductForFAQ(null);\n                },\n                product: selectedProductForFAQ\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 1040,\n                columnNumber: 9\n            }, undefined),\n            showVariationsModal && selectedProductForVariations && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_VariationsManagementModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showVariationsModal,\n                onClose: ()=>{\n                    setShowVariationsModal(false);\n                    setSelectedProductForVariations(null);\n                },\n                product: selectedProductForVariations\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 1052,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n        lineNumber: 674,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductsPage, \"EvH8EMgvHJS7UcfEai3ubiXmPTs=\");\n_c = ProductsPage;\nconst ProductModal = (param)=>{\n    let { isOpen, onClose, onSave, categories, product } = param;\n    var _product_comparePrice, _product_category, _product_images, _product_variants;\n    _s1();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (product === null || product === void 0 ? void 0 : product.name) || \"\",\n        slug: (product === null || product === void 0 ? void 0 : product.slug) || \"\",\n        description: (product === null || product === void 0 ? void 0 : product.description) || \"\",\n        shortDescription: (product === null || product === void 0 ? void 0 : product.shortDescription) || \"\",\n        comparePrice: (product === null || product === void 0 ? void 0 : (_product_comparePrice = product.comparePrice) === null || _product_comparePrice === void 0 ? void 0 : _product_comparePrice.toString()) || \"\",\n        categoryId: (product === null || product === void 0 ? void 0 : (_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.id) || \"\",\n        isFeatured: (product === null || product === void 0 ? void 0 : product.isFeatured) || false\n    });\n    // Initialize selected categories from product data\n    const [selectedCategoryIds, setSelectedCategoryIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        var _product_category;\n        if ((product === null || product === void 0 ? void 0 : product.productCategories) && product.productCategories.length > 0) {\n            return product.productCategories.map((pc)=>pc.category.id);\n        } else if (product === null || product === void 0 ? void 0 : (_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.id) {\n            return [\n                product.category.id\n            ];\n        }\n        return [];\n    });\n    const [productImages, setProductImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((product === null || product === void 0 ? void 0 : (_product_images = product.images) === null || _product_images === void 0 ? void 0 : _product_images.map((img)=>({\n            id: img.id || \"\",\n            url: img.url,\n            alt: img.alt || \"\",\n            position: img.position || 0\n        }))) || []);\n    const [variations, setVariations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((product === null || product === void 0 ? void 0 : (_product_variants = product.variants) === null || _product_variants === void 0 ? void 0 : _product_variants.map((v)=>{\n        var _v_price;\n        return {\n            id: v.id,\n            name: v.name,\n            value: v.value,\n            price: ((_v_price = v.price) === null || _v_price === void 0 ? void 0 : _v_price.toString()) || \"0\"\n        };\n    })) || []);\n    const [showVariations, setShowVariations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize variations when product changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (product === null || product === void 0 ? void 0 : product.variants) {\n            setVariations(product.variants.map((v)=>{\n                var _v_price;\n                return {\n                    id: v.id,\n                    name: v.name,\n                    value: v.value,\n                    price: ((_v_price = v.price) === null || _v_price === void 0 ? void 0 : _v_price.toString()) || \"0\"\n                };\n            }));\n        } else {\n            setVariations([]);\n        }\n    }, [\n        product\n    ]);\n    const addVariation = ()=>{\n        setVariations([\n            ...variations,\n            {\n                name: \"\",\n                value: \"\",\n                price: \"0\"\n            }\n        ]);\n    };\n    const updateVariation = (index, field, value)=>{\n        const updatedVariations = [\n            ...variations\n        ];\n        updatedVariations[index] = {\n            ...updatedVariations[index],\n            [field]: value\n        };\n        setVariations(updatedVariations);\n    };\n    const removeVariation = (index)=>{\n        setVariations(variations.filter((_, i)=>i !== index));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        try {\n            const url = product ? \"/api/products/\".concat(product.id) : \"/api/products\";\n            const method = product ? \"PUT\" : \"POST\";\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    ...formData,\n                    slug: formData.slug || (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.generateSlug)(formData.name),\n                    comparePrice: formData.comparePrice ? parseFloat(formData.comparePrice) : null,\n                    categoryIds: selectedCategoryIds,\n                    images: productImages.map((img, index)=>({\n                            url: img.url,\n                            alt: img.alt || formData.name,\n                            position: index\n                        })),\n                    variations: variations.filter((v)=>v.name && v.value).map((v)=>({\n                            name: v.name,\n                            value: v.value,\n                            price: v.price ? parseFloat(v.price) : 0\n                        }))\n                })\n            });\n            const result = await response.json();\n            if (response.ok) {\n                if (result.warnings && result.warnings.length > 0) {\n                    alert(\"Product saved successfully!\\n\\nWarnings:\\n\".concat(result.warnings.join(\"\\n\")));\n                }\n                onSave();\n                onClose();\n            } else {\n                alert(\"Failed to save product: \".concat(result.error || \"Unknown error\"));\n            }\n        } catch (error) {\n            if (true) {\n                console.error(\"Error saving product:\", error);\n            }\n            alert(\"Failed to save product\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: product ? \"Edit Product\" : \"Add Product\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1213,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1216,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 1212,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Product Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1227,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.name,\n                                            onChange: (e)=>{\n                                                const newName = e.target.value;\n                                                setFormData({\n                                                    ...formData,\n                                                    name: newName,\n                                                    // Auto-generate slug if slug field is empty\n                                                    slug: formData.slug || (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.generateSlug)(newName)\n                                                });\n                                            },\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1230,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1226,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: [\n                                                \"URL Slug\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 ml-1\",\n                                                    children: \"(SEO-friendly URL)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1250,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1248,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.slug,\n                                            onChange: (e)=>setFormData({\n                                                    ...formData,\n                                                    slug: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.generateSlug)(e.target.value)\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                                            placeholder: \"auto-generated-from-name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1252,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                \"Preview: /products/\",\n                                                formData.slug || (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.generateSlug)(formData.name)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1259,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1247,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_CategoryMultiSelect__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        categories: categories,\n                                        selectedCategoryIds: selectedCategoryIds,\n                                        onChange: setSelectedCategoryIds,\n                                        label: \"Categories\",\n                                        placeholder: \"Select categories...\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 1265,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1264,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Compare Price (₹)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1276,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            step: \"0.01\",\n                                            value: formData.comparePrice,\n                                            onChange: (e)=>setFormData({\n                                                    ...formData,\n                                                    comparePrice: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                                            placeholder: \"Enter compare price in Rupees\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1279,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1275,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1225,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Short Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1291,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.shortDescription,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            shortDescription: e.target.value\n                                        }),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1294,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1290,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1303,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: formData.description,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            description: e.target.value\n                                        }),\n                                    rows: 4,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1306,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1302,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ImageGalleryManager__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            images: productImages,\n                            onChange: (images)=>setProductImages(images.map((img)=>({\n                                        id: img.id || \"\",\n                                        url: img.url,\n                                        alt: img.alt || \"\",\n                                        position: img.position || 0\n                                    }))),\n                            productName: formData.name || \"Product\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1314,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border border-gray-200 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"Product Variations\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1328,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowVariations(!showVariations),\n                                            className: \"text-sm text-blue-600 hover:text-blue-700\",\n                                            children: showVariations ? \"Hide Variations\" : \"Add Variations\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1329,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1327,\n                                    columnNumber: 13\n                                }, undefined),\n                                showVariations && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600 mb-3\",\n                                            children: \"Add variations like Size, Color, Material, etc. Each variation can have its own price adjustment and stock.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1340,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        variations.map((variation, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-3 p-3 border border-gray-200 rounded-lg bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                                                children: \"Name (e.g., Size)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1347,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: variation.name,\n                                                                onChange: (e)=>updateVariation(index, \"name\", e.target.value),\n                                                                className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500\",\n                                                                placeholder: \"Size\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1350,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1346,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                                                children: \"Value (e.g., Large)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1359,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: variation.value,\n                                                                onChange: (e)=>updateVariation(index, \"value\", e.target.value),\n                                                                className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500\",\n                                                                placeholder: \"Large\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1362,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1358,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-end space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-xs font-medium text-gray-700 mb-1\",\n                                                                        children: \"Price Adjustment (₹)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1372,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        step: \"0.01\",\n                                                                        value: variation.price,\n                                                                        onChange: (e)=>updateVariation(index, \"price\", e.target.value),\n                                                                        className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500\",\n                                                                        placeholder: \"0.00\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1375,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1371,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>removeVariation(index),\n                                                                className: \"px-2 py-1 text-red-600 hover:text-red-700 text-sm\",\n                                                                title: \"Remove variation\",\n                                                                children: \"\\xd7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1384,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1370,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 1345,\n                                                columnNumber: 19\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: addVariation,\n                                            className: \"w-full px-3 py-2 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-700 transition-colors\",\n                                            children: \"+ Add Variation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 1396,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1339,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1326,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"featured\",\n                                    checked: formData.isFeatured,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            isFeatured: e.target.checked\n                                        }),\n                                    className: \"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1408,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"featured\",\n                                    className: \"ml-2 text-sm text-gray-700\",\n                                    children: \"Featured Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1415,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1407,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-end space-x-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1421,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\",\n                                    children: loading ? \"Saving...\" : product ? \"Update\" : \"Create\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 1428,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 1420,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 1224,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n            lineNumber: 1211,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\products\\\\page.tsx\",\n        lineNumber: 1210,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(ProductModal, \"1TmEwypsCy4rN1ZAJXEjVGFAyng=\");\n_c1 = ProductModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProductsPage);\nvar _c, _c1;\n$RefreshReg$(_c, \"ProductsPage\");\n$RefreshReg$(_c1, \"ProductModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/products/page.tsx\n"));

/***/ })

});