import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../../lib/auth';
import { adminNotifications } from '../../../../lib/notification-helpers';
import { logger } from '../../../../lib/logger';
import { z } from 'zod';

const sendNotificationSchema = z.object({
  userIds: z.array(z.string()).min(1, 'At least one user must be selected'),
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
  type: z.enum(['ADMIN_MESSAGE', 'BROADCAST', 'PROMOTIONAL', 'SYSTEM']).default('ADMIN_MESSAGE'),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']).default('NORMAL'),
  sendEmail: z.boolean().default(true),
  sendInApp: z.boolean().default(true),
});

/**
 * POST /api/admin/notifications/send
 * Send notifications to selected users
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Only allow admin users
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = sendNotificationSchema.parse(body);

    logger.info('Admin sending notifications', {
      adminId: session.user.id,
      userCount: validatedData.userIds.length,
      type: validatedData.type,
      priority: validatedData.priority,
    });

    const results = [];
    let successCount = 0;
    let errorCount = 0;

    // Send notification to each selected user
    for (const userId of validatedData.userIds) {
      try {
        await adminNotifications.adminMessage(userId, {
          title: validatedData.title,
          content: validatedData.content,
          type: validatedData.type,
          priority: validatedData.priority,
          sendEmail: validatedData.sendEmail,
          sendInApp: validatedData.sendInApp,
        });

        results.push({
          userId,
          success: true,
        });
        successCount++;

      } catch (userError) {
        logger.error('Failed to send notification to user', userError as Error, {
          userId,
          adminId: session.user.id,
        });

        results.push({
          userId,
          success: false,
          error: userError instanceof Error ? userError.message : 'Unknown error',
        });
        errorCount++;
      }
    }

    logger.info('Notification sending completed', {
      adminId: session.user.id,
      totalUsers: validatedData.userIds.length,
      successCount,
      errorCount,
    });

    return NextResponse.json({
      success: true,
      message: `Notifications sent successfully to ${successCount} user(s)`,
      stats: {
        total: validatedData.userIds.length,
        success: successCount,
        errors: errorCount,
      },
      results,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation error',
        details: error.issues,
      }, { status: 400 });
    }

    logger.error('Failed to send admin notifications', error as Error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
