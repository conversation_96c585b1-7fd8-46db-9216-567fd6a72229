// Basic service worker to prevent 404 errors
// This is a minimal service worker that doesn't cache anything
// but prevents the browser from showing 404 errors

self.addEventListener('install', function(event) {
  // Skip waiting to activate immediately
  self.skipWaiting();
});

self.addEventListener('activate', function(event) {
  // Claim all clients immediately
  event.waitUntil(self.clients.claim());
});

self.addEventListener('fetch', function(event) {
  // Let all requests pass through without caching
  // This prevents any interference with the application
  return;
});