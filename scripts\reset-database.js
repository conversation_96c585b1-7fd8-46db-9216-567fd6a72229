const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function resetDatabase() {
  console.log('🔄 Starting database reset...');
  
  try {
    // First, delete all related data in the correct order to avoid foreign key constraints
    
    console.log('🗑️  Deleting all order items...');
    await prisma.orderItem.deleteMany({});
    
    console.log('🗑️  Deleting all order addresses...');
    await prisma.orderAddress.deleteMany({});
    
    console.log('🗑️  Deleting all orders...');
    await prisma.order.deleteMany({});
    
    console.log('🗑️  Deleting all coupon usages...');
    await prisma.couponUsage.deleteMany({});
    
    console.log('🗑️  Deleting all coupons...');
    await prisma.coupon.deleteMany({});
    
    console.log('🗑️  Deleting all wishlist items...');
    await prisma.wishlistItem.deleteMany({});
    
    console.log('🗑️  Deleting all reviews...');
    await prisma.review.deleteMany({});
    
    console.log('🗑️  Deleting all product FAQs...');
    await prisma.productFAQ.deleteMany({});
    
    console.log('🗑️  Deleting all product images...');
    await prisma.productImage.deleteMany({});
    
    console.log('🗑️  Deleting all product variants...');
    await prisma.productVariant.deleteMany({});
    
    console.log('🗑️  Deleting all product categories relationships...');
    await prisma.productCategory.deleteMany({});
    
    console.log('🗑️  Deleting all products...');
    await prisma.product.deleteMany({});
    
    console.log('🗑️  Deleting all categories...');
    await prisma.category.deleteMany({});
    
    console.log('🗑️  Deleting all addresses...');
    await prisma.address.deleteMany({});
    
    console.log('🗑️  Deleting all user preferences...');
    await prisma.userPreference.deleteMany({});
    
    // Users are preserved as requested
    
    console.log('✅ Database reset completed - all products and categories removed, users preserved');
    
    // Now let's recreate the basic categories from the seed file
    console.log('🌱 Recreating default categories...');
    
    const categories = [
      {
        name: 'Skincare',
        slug: 'skincare',
        description: 'Natural skincare products for healthy, glowing skin',
        isActive: true,
      },
      {
        name: 'Hair Care',
        slug: 'hair-care',
        description: 'Nourishing hair care products for all hair types',
        isActive: true,
      },
      {
        name: 'Body Care',
        slug: 'body-care',
        description: 'Luxurious body care essentials',
        isActive: true,
      },
    ];
    
    for (const category of categories) {
      await prisma.category.create({
        data: category,
      });
    }
    
    console.log('✅ Default categories recreated successfully');
    console.log('📊 Reset Summary:');
    console.log('- All products removed');
    console.log('- All categories reset to clean state');
    console.log('- All orders, reviews, wishlist items removed');
    console.log('- Users preserved');
    console.log('- 3 default categories recreated: Skincare, Hair Care, Body Care');
    
  } catch (error) {
    console.error('❌ Error resetting database:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the reset if this file is executed directly
if (require.main === module) {
  resetDatabase()
    .then(() => {
      console.log('🎉 Database reset completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Database reset failed:', error);
      process.exit(1);
    });
}

module.exports = { resetDatabase };