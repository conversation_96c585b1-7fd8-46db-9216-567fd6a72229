{"products": [{"name": "Anti-Pimple Pack", "slug": "anti-pimple-pack", "description": "", "shortDescription": "", "price": 650, "comparePrice": null, "categoryNames": ["Facial Kits"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "75g", "price": 650}]}, {"name": "Anti Ageing Gel Creme", "slug": "anti-ageing-gel-creme", "description": "", "shortDescription": "", "price": 700, "comparePrice": null, "categoryNames": ["Creams & Moisturizers"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "30g", "price": 700}]}, {"name": "Baby Hair Oil", "slug": "baby-hair-oil", "description": "", "shortDescription": "", "price": 300, "comparePrice": null, "categoryNames": ["Hair Oils"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "100ml", "price": 300}, {"name": "Size", "value": "200ml", "price": 600}]}, {"name": "Beard Oil", "slug": "beard-oil", "description": "", "shortDescription": "", "price": 600, "comparePrice": null, "categoryNames": ["Hair Oils"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "30ml", "price": 600}]}, {"name": "Body Polishing & Glow Massage Oil", "slug": "body-polishing-glow-massage-oil", "description": "", "shortDescription": "", "price": 300, "comparePrice": null, "categoryNames": ["Massage & Body Oils"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "100ml", "price": 300}, {"name": "Size", "value": "200ml", "price": 600}]}, {"name": "Bridal Face <PERSON>", "slug": "bridal-face-ubtan", "description": "", "shortDescription": "", "price": 550, "comparePrice": null, "categoryNames": ["Ubtan & Masks"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "75g", "price": 550}]}, {"name": "<PERSON><PERSON><PERSON>", "slug": "chiksa", "description": "", "shortDescription": "", "price": 900, "comparePrice": null, "categoryNames": ["Facial Kits"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "150g", "price": 900}]}, {"name": "Deep Clean & Polishing Face Scrub", "slug": "deep-clean-polishing-face-scrub", "description": "", "shortDescription": "", "price": 500, "comparePrice": null, "categoryNames": ["Scrubs & Exfoliants"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "75g", "price": 500}]}, {"name": "Eyebrow Thickening & Lash Lengthening Oil", "slug": "eyebrow-thickening-lash-lengthening-oil", "description": "", "shortDescription": "", "price": 550, "comparePrice": null, "categoryNames": ["Eye & Lip Care"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "10ml", "price": 550}]}, {"name": "Face Elixir Oil", "slug": "face-elixir-oil", "description": "", "shortDescription": "", "price": 850, "comparePrice": null, "categoryNames": ["Facial Oils & Elixirs"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "30ml", "price": 850}]}, {"name": "<PERSON>", "slug": "face-ubtan", "description": "", "shortDescription": "", "price": 500, "comparePrice": null, "categoryNames": ["Ubtan & Masks"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "75g", "price": 500}]}, {"name": "Glitter Lip Balm", "slug": "glitter-lip-balm", "description": "", "shortDescription": "", "price": 350, "comparePrice": null, "categoryNames": ["Eye & Lip Care"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "15g", "price": 350}]}, {"name": "Gold Dust Skin Tonic", "slug": "gold-dust-skin-tonic", "description": "", "shortDescription": "", "price": 540, "comparePrice": null, "categoryNames": ["Toners"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "100ml", "price": 540}]}, {"name": "Hair Growth & Shine Mask", "slug": "hair-growth-shine-mask", "description": "", "shortDescription": "", "price": 600, "comparePrice": null, "categoryNames": ["Hair Masks", "Ubtan & Masks"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "150g", "price": 600}]}, {"name": "Kumkumadi Face Oil", "slug": "kumkumadi-face-oil", "description": "", "shortDescription": "", "price": 1200, "comparePrice": null, "categoryNames": ["Facial Oils & Elixirs"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "30ml", "price": 1200}]}, {"name": "Luxury Moisturizing Milk Cream", "slug": "luxury-moisturizing-milk-cream", "description": "", "shortDescription": "", "price": 540, "comparePrice": null, "categoryNames": ["Creams & Moisturizers"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "15g", "price": 540}]}, {"name": "Ma<PERSON>ni Hair Oil", "slug": "maharani-hair-oil", "description": "", "shortDescription": "", "price": 300, "comparePrice": null, "categoryNames": ["Hair Oils"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "100ml", "price": 300}, {"name": "Size", "value": "200ml", "price": 600}]}, {"name": "<PERSON><PERSON><PERSON>", "slug": "neem-face-wash", "description": "", "shortDescription": "", "price": 350, "comparePrice": null, "categoryNames": ["Cleansers & Face Wash"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "100g", "price": 350}]}, {"name": "<PERSON><PERSON>", "slug": "papaya-face-wash", "description": "", "shortDescription": "", "price": 350, "comparePrice": null, "categoryNames": ["Cleansers & Face Wash"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "100g", "price": 350}]}, {"name": "Royal Facial Kit", "slug": "royal-facial-kit", "description": "", "shortDescription": "", "price": 500, "comparePrice": null, "categoryNames": ["Facial Kits"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "Mini", "price": 500}, {"name": "Size", "value": "Regular", "price": 1500}]}, {"name": "Sa<PERSON>ron Gel", "slug": "saffron-gel", "description": "", "shortDescription": "", "price": 700, "comparePrice": null, "categoryNames": ["Gels & Serums"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "30g", "price": 700}]}, {"name": "Saffron Night Repair Cream", "slug": "saffron-night-repair-cream", "description": "", "shortDescription": "", "price": 800, "comparePrice": null, "categoryNames": ["Creams & Moisturizers"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "15g", "price": 800}, {"name": "Size", "value": "30g", "price": 1500}]}, {"name": "24K Night Care", "slug": "24k-night-care", "description": "", "shortDescription": "", "price": 3000, "comparePrice": null, "categoryNames": ["Creams & Moisturizers", "Combo & Complete Care"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "30ml + 30g", "price": 3000}]}, {"name": "Snow Face Cleanser", "slug": "snow-face-cleanser", "description": "", "shortDescription": "", "price": 550, "comparePrice": null, "categoryNames": ["Cleansers & Face Wash"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "50g", "price": 550}]}, {"name": "Strawberry Silk Booster", "slug": "strawberry-silk-booster", "description": "", "shortDescription": "", "price": 700, "comparePrice": null, "categoryNames": ["Gels & Serums"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "30ml", "price": 700}]}, {"name": "<PERSON><PERSON>", "slug": "supermane-hair-oil", "description": "", "shortDescription": "", "price": 300, "comparePrice": null, "categoryNames": ["Hair Oils"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "100ml", "price": 300}, {"name": "Size", "value": "200ml", "price": 600}]}, {"name": "Under-<PERSON> Gel", "slug": "under-eye-gel", "description": "", "shortDescription": "", "price": 450, "comparePrice": null, "categoryNames": ["Gels & Serums", "Eye & Lip Care"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "15g", "price": 450}]}, {"name": "Vitamin-C <PERSON>", "slug": "vitamin-c-serum", "description": "", "shortDescription": "", "price": 800, "comparePrice": null, "categoryNames": ["Gels & Serums"], "isFeatured": false, "isActive": true, "variations": [{"name": "Size", "value": "30ml", "price": 800}]}]}