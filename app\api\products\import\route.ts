import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/lib/db';
import { generateSlug } from '@/app/lib/currency';

// POST /api/products/import - Bulk import products from CSV data
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { products } = body;

    if (!products || !Array.isArray(products)) {
      return NextResponse.json(
        { success: false, error: 'Invalid products data' },
        { status: 400 }
      );
    }

    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[],
    };

    // Get all categories to map names to IDs
    const categories = await prisma.category.findMany({
      where: { isActive: true },
    });

    const categoryMap = new Map(
      categories.map(cat => [cat.name.toLowerCase(), cat.id])
    );

    // Add common category name variations to the map
    categories.forEach(cat => {
      const lowerName = cat.name.toLowerCase();
      // Add variations for better matching
      if (lowerName === 'skincare') {
        categoryMap.set('skin care', cat.id);
        categoryMap.set('skin', cat.id);
      } else if (lowerName === 'hair care') {
        categoryMap.set('haircare', cat.id);
        categoryMap.set('hair', cat.id);
      } else if (lowerName === 'body care') {
        categoryMap.set('bodycare', cat.id);
        categoryMap.set('body', cat.id);
      }
    });

    for (const productData of products) {
      try {
        const {
          name,
          slug,
          description,
          shortDescription,
          price,
          comparePrice,
          category: categoryName,
          categoryNames = [],
          isFeatured,
          isActive,
          variations: rawVariations = [],
        } = productData;

        // Validate required fields
        if (!name) {
          results.failed++;
          results.errors.push(`Product missing required name field`);
          continue;
        }

        // Parse variations if they come as JSON strings
        let variations = rawVariations;
        if (typeof rawVariations === 'string') {
          try {
            variations = JSON.parse(rawVariations);
          } catch (e) {
            variations = [];
          }
        }

        // Ensure variations is an array
        if (!Array.isArray(variations)) {
          variations = [];
        }

        // Calculate default price from variations if not provided
        let defaultPrice = price ? parseFloat(price.toString()) : null;
        
        // Handle blank/empty price - check if variations provide pricing
        if ((!defaultPrice || defaultPrice === 0) && variations && variations.length > 0) {
          // Keep base price at 0 if variations exist
          defaultPrice = 0;
        } else if (!defaultPrice && defaultPrice !== 0) {
          // Only fail if no price AND no variations
          if (!variations || variations.length === 0) {
            results.failed++;
            results.errors.push(`Product "${name}" missing required price field or variations with pricing`);
            continue;
          } else {
            // Has variations but no base price - set to 0
            defaultPrice = 0;
          }
        }

        // Handle both single category and multiple categories
        const allCategoryNames = [];
        if (categoryName) {
          allCategoryNames.push(categoryName);
        }
        if (categoryNames && categoryNames.length > 0) {
          allCategoryNames.push(...categoryNames);
        }

        // Map category names to IDs with better matching
        const categoryIds = [];
        for (const catName of allCategoryNames) {
          // Try exact match first
          let categoryId = categoryMap.get(catName.toLowerCase());
          
          // If not found, try partial matching for common variations
          if (!categoryId) {
            if (catName.toLowerCase().includes('skin')) {
              categoryId = categoryMap.get('skincare');
            } else if (catName.toLowerCase().includes('hair')) {
              categoryId = categoryMap.get('hair care');
            } else if (catName.toLowerCase().includes('body')) {
              categoryId = categoryMap.get('body care');
            }
          }
          
          if (categoryId) {
            categoryIds.push(categoryId);
          } else {
            results.errors.push(`Category "${catName}" not found for product "${name}"`);
          }
        }

        if (categoryIds.length === 0) {
          results.failed++;
          results.errors.push(`Product "${name}" has no valid categories`);
          continue;
        }

        // Generate slug if not provided
        const productSlug = slug || generateSlug(name);

        // Check if product with this slug already exists
        const existingProduct = await prisma.product.findUnique({
          where: { slug: productSlug },
        });

        if (existingProduct) {
          results.failed++;
          results.errors.push(`Product with slug "${productSlug}" already exists`);
          continue;
        }

        // Create the product
        const product = await prisma.product.create({
          data: {
            name,
            slug: productSlug,
            description: description || '',
            shortDescription: shortDescription || '',
            price: defaultPrice,
            comparePrice: comparePrice ? parseFloat(comparePrice.toString()) : null,
            categoryId: categoryIds[0], // Set first category as primary for backward compatibility
            isFeatured: Boolean(isFeatured),
            isActive: isActive !== false, // Default to true if not specified
            variants: variations.length > 0
              ? {
                  create: variations.map((variation: any) => ({
                    name: variation.name,
                    value: variation.value,
                    price: variation.price ? parseFloat(variation.price.toString()) : null,
                  })),
                }
              : undefined,
            productCategories: {
              create: categoryIds.map((categoryId: string) => ({
                categoryId,
              })),
            },
          },
          include: {
            category: true,
            productCategories: {
              include: {
                category: true,
              },
            },
            variants: true,
          },
        });

        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`Failed to create product "${productData.name}": ${error}`);
        console.error('Error creating product:', error);
      }
    }

    return NextResponse.json({
      success: true,
      data: results,
      message: `Import completed: ${results.success} successful, ${results.failed} failed`,
    });
  } catch (error) {
    console.error('Error importing products:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to import products' },
      { status: 500 }
    );
  }
}
