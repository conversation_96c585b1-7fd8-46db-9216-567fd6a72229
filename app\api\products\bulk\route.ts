import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/lib/db';

// POST /api/products/bulk - Bulk operations on products
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, productIds } = body;

    if (!action || !productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Invalid request. Action and productIds array are required.' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'delete':
        return await handleBulkDelete(productIds);
      case 'activate':
        return await handleBulkActivate(productIds, true);
      case 'deactivate':
        return await handleBulkActivate(productIds, false);
      case 'feature':
        return await handleBulkFeature(productIds, true);
      case 'unfeature':
        return await handleBulkFeature(productIds, false);
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action. Supported actions: delete, activate, deactivate, feature, unfeature' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in bulk operation:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to perform bulk operation' },
      { status: 500 }
    );
  }
}

async function handleBulkDelete(productIds: string[]) {
  const results = {
    success: 0,
    softDeleted: 0,
    failed: 0,
    errors: [] as string[],
  };

  for (const productId of productIds) {
    try {
      // Check if product exists in any orders
      const orderItems = await prisma.orderItem.findMany({
        where: {
          productId: productId,
        },
        include: {
          order: {
            select: {
              id: true,
              status: true,
            },
          },
        },
      });

      if (orderItems.length > 0) {
        // Soft delete by setting isActive to false
        const product = await prisma.product.findUnique({
          where: { id: productId },
          select: { name: true }
        });

        if (product) {
          await prisma.product.update({
            where: {
              id: productId,
            },
            data: {
              isActive: false,
              name: `[DELETED] ${new Date().toISOString().split('T')[0]} - ${product.name}`,
            },
          });
          results.softDeleted++;
        }
      } else {
        // Hard delete - delete related records first
        await prisma.$transaction(async (tx) => {
          // Delete product images
          await tx.productImage.deleteMany({
            where: { productId: productId },
          });

          // Delete product variants
          await tx.productVariant.deleteMany({
            where: { productId: productId },
          });

          // Delete product categories (many-to-many relationships)
          await tx.productCategory.deleteMany({
            where: { productId: productId },
          });

          // Delete product reviews
          await tx.review.deleteMany({
            where: { productId: productId },
          });

          // Delete product FAQs
          await tx.productFAQ.deleteMany({
            where: { productId: productId },
          });

          // Delete wishlist items
          await tx.wishlistItem.deleteMany({
            where: { productId: productId },
          });

          // Finally delete the product
          await tx.product.delete({
            where: { id: productId },
          });
        });
        results.success++;
      }
    } catch (error) {
      console.error(`Error deleting product ${productId}:`, error);
      results.failed++;
      results.errors.push(`Failed to delete product ${productId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  let message = `Bulk delete completed. `;
  if (results.success > 0) {
    message += `${results.success} products deleted permanently. `;
  }
  if (results.softDeleted > 0) {
    message += `${results.softDeleted} products deactivated (had order history). `;
  }
  if (results.failed > 0) {
    message += `${results.failed} products failed to delete.`;
  }

  return NextResponse.json({
    success: true,
    message: message.trim(),
    data: results,
  });
}

async function handleBulkActivate(productIds: string[], isActive: boolean) {
  try {
    const result = await prisma.product.updateMany({
      where: {
        id: {
          in: productIds,
        },
      },
      data: {
        isActive: isActive,
      },
    });

    return NextResponse.json({
      success: true,
      message: `${result.count} products ${isActive ? 'activated' : 'deactivated'} successfully`,
      data: { updated: result.count },
    });
  } catch (error) {
    console.error('Error in bulk activate/deactivate:', error);
    return NextResponse.json(
      { success: false, error: `Failed to ${isActive ? 'activate' : 'deactivate'} products` },
      { status: 500 }
    );
  }
}

async function handleBulkFeature(productIds: string[], isFeatured: boolean) {
  try {
    const result = await prisma.product.updateMany({
      where: {
        id: {
          in: productIds,
        },
      },
      data: {
        isFeatured: isFeatured,
      },
    });

    return NextResponse.json({
      success: true,
      message: `${result.count} products ${isFeatured ? 'featured' : 'unfeatured'} successfully`,
      data: { updated: result.count },
    });
  } catch (error) {
    console.error('Error in bulk feature/unfeature:', error);
    return NextResponse.json(
      { success: false, error: `Failed to ${isFeatured ? 'feature' : 'unfeature'} products` },
      { status: 500 }
    );
  }
}