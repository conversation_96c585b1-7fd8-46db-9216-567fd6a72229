import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/lib/db';

// GET /api/categories/[id] - Get a specific category
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const category = await prisma.category.findUnique({
      where: {
        id: params.id,
      },
      include: {
        products: {
          include: {
            images: {
              take: 1,
              orderBy: {
                position: 'asc',
              },
            },
          },
        },
        parent: true,
        children: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: category,
    });
  } catch (error) {
    console.error('Error fetching category:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch category' },
      { status: 500 }
    );
  }
}

// PATCH /api/categories/[id] - Update a category
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { name, slug, description, image, isActive, parentId } = body;

    const category = await prisma.category.update({
      where: {
        id: params.id,
      },
      data: {
        ...(name && { name }),
        ...(slug && { slug }),
        ...(description && { description }),
        ...(image && { image }),
        ...(isActive !== undefined && { isActive }),
        ...(parentId && { parentId }),
      },
      include: {
        parent: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: category,
      message: 'Category updated successfully',
    });
  } catch (error) {
    console.error('Error updating category:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update category' },
      { status: 500 }
    );
  }
}

// DELETE /api/categories/[id] - Delete a category
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if category has products (direct relationship)
    const productCount = await prisma.product.count({
      where: {
        categoryId: params.id,
      },
    });

    // Check for products in many-to-many relationship (if table exists)
    let productCategoryCount = 0;
    try {
      productCategoryCount = await prisma.productCategory.count({
        where: {
          categoryId: params.id,
        },
      });
    } catch (error) {
      // ProductCategory table might not exist yet
      console.log('ProductCategory table not found, skipping many-to-many check');
    }

    const totalProductCount = productCount + productCategoryCount;

    if (totalProductCount > 0) {
      return NextResponse.json(
        { success: false, error: `Cannot delete category. ${totalProductCount} product(s) are still assigned to this category.` },
        { status: 400 }
      );
    }

    // Check if category has child categories
    const childCount = await prisma.category.count({
      where: {
        parentId: params.id,
      },
    });

    if (childCount > 0) {
      return NextResponse.json(
        { success: false, error: `Cannot delete category. ${childCount} subcategory(ies) exist under this category.` },
        { status: 400 }
      );
    }

    // If all checks pass, delete the category
    await prisma.category.delete({
      where: {
        id: params.id,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Category deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting category:', error);
    
    // Handle Prisma foreign key constraint errors
    if (error instanceof Error && error.message.includes('foreign key constraint')) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete category due to existing relationships' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to delete category' },
      { status: 500 }
    );
  }
}
