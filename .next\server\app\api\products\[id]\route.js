"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/[id]/route";
exports.ids = ["app/api/products/[id]/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_project_app_api_products_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/products/[id]/route.ts */ \"(rsc)/./app/api/products/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/[id]/route\",\n        pathname: \"/api/products/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\api\\\\products\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_project_app_api_products_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/products/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/products/[id]/route.ts":
/*!****************************************!*\
  !*** ./app/api/products/[id]/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _app_lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/db */ \"(rsc)/./app/lib/db.ts\");\n\n\n// GET /api/products/[id] - Get a specific product\nasync function GET(request, { params }) {\n    try {\n        // Try to find by ID first, then by slug\n        let product = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findUnique({\n            where: {\n                id: params.id\n            },\n            include: {\n                category: true,\n                productCategories: {\n                    include: {\n                        category: true\n                    }\n                },\n                images: {\n                    orderBy: {\n                        position: \"asc\"\n                    }\n                },\n                variants: true,\n                reviews: {\n                    include: {\n                        user: true\n                    },\n                    orderBy: {\n                        createdAt: \"desc\"\n                    }\n                }\n            }\n        });\n        // If not found by ID, try to find by slug\n        if (!product) {\n            product = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findUnique({\n                where: {\n                    slug: params.id\n                },\n                include: {\n                    category: true,\n                    productCategories: {\n                        include: {\n                            category: true\n                        }\n                    },\n                    images: {\n                        orderBy: {\n                            position: \"asc\"\n                        }\n                    },\n                    variants: true,\n                    reviews: {\n                        include: {\n                            user: true\n                        },\n                        orderBy: {\n                            createdAt: \"desc\"\n                        }\n                    }\n                }\n            });\n        }\n        if (!product) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Product not found\"\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: product\n        });\n    } catch (error) {\n        console.error(\"Error fetching product:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to fetch product\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PATCH /api/products/[id] - Update a product\nasync function PATCH(request, { params }) {\n    try {\n        const body = await request.json();\n        const { name, slug, description, shortDescription, price, comparePrice, sku, quantity, isFeatured, isActive, categoryId, categoryIds, images, variations = [] } = body;\n        // Handle category updates\n        const updateData = {\n            ...name && {\n                name\n            },\n            ...slug && {\n                slug\n            },\n            ...description && {\n                description\n            },\n            ...shortDescription && {\n                shortDescription\n            },\n            ...price && {\n                price\n            },\n            ...comparePrice && {\n                comparePrice\n            },\n            ...sku && {\n                sku\n            },\n            ...quantity !== undefined && {\n                quantity\n            },\n            ...isFeatured !== undefined && {\n                isFeatured\n            },\n            ...isActive !== undefined && {\n                isActive\n            },\n            ...categoryId && {\n                categoryId\n            }\n        };\n        // If categoryIds is provided, update the many-to-many relationships\n        if (categoryIds && Array.isArray(categoryIds)) {\n            // First delete existing category relationships\n            await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.productCategory.deleteMany({\n                where: {\n                    productId: params.id\n                }\n            });\n            // Then create new relationships\n            if (categoryIds.length > 0) {\n                updateData.productCategories = {\n                    create: categoryIds.map((catId)=>({\n                            categoryId: catId\n                        }))\n                };\n            }\n        }\n        // Handle images update\n        if (images !== undefined) {\n            // Delete existing images\n            await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.productImage.deleteMany({\n                where: {\n                    productId: params.id\n                }\n            });\n            // Create new images\n            if (images.length > 0) {\n                updateData.images = {\n                    create: images.map((img, index)=>({\n                            url: img.url,\n                            alt: img.alt || name || \"Product image\",\n                            position: index\n                        }))\n                };\n            }\n        }\n        // Handle variations update\n        if (variations !== undefined) {\n            // Delete existing variations\n            await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.productVariant.deleteMany({\n                where: {\n                    productId: params.id\n                }\n            });\n            // Create new variations\n            if (variations.length > 0) {\n                updateData.variants = {\n                    create: variations.map((variation)=>({\n                            name: variation.name,\n                            value: variation.value,\n                            price: variation.price || null\n                        }))\n                };\n            }\n        }\n        const product = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.update({\n            where: {\n                id: params.id\n            },\n            data: updateData,\n            include: {\n                category: true,\n                productCategories: {\n                    include: {\n                        category: true\n                    }\n                },\n                images: {\n                    orderBy: {\n                        position: \"asc\"\n                    }\n                },\n                variants: true\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: product,\n            message: \"Product updated successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error updating product:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to update product\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/products/[id] - Update a product (same as PATCH for compatibility)\nasync function PUT(request, { params }) {\n    return PATCH(request, {\n        params\n    });\n}\n// DELETE /api/products/[id] - Delete a product\nasync function DELETE(request, { params }) {\n    try {\n        // Check if product exists in any orders\n        const orderItems = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.orderItem.findMany({\n            where: {\n                productId: params.id\n            },\n            include: {\n                order: {\n                    select: {\n                        id: true,\n                        status: true\n                    }\n                }\n            }\n        });\n        if (orderItems.length > 0) {\n            // If product is in orders, soft delete by setting isActive to false\n            const updatedProduct = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.update({\n                where: {\n                    id: params.id\n                },\n                data: {\n                    isActive: false,\n                    name: `[DELETED] ${new Date().toISOString().split(\"T\")[0]} - ` + (await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findUnique({\n                        where: {\n                            id: params.id\n                        },\n                        select: {\n                            name: true\n                        }\n                    }))?.name\n                }\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: \"Product has been deactivated (soft deleted) because it exists in order history. It will no longer appear in the store.\",\n                type: \"soft_delete\",\n                orderCount: orderItems.length\n            });\n        }\n        // If no order references, proceed with hard delete\n        // Delete related records first\n        await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n            // Delete product images\n            await tx.productImage.deleteMany({\n                where: {\n                    productId: params.id\n                }\n            });\n            // Delete product variants\n            await tx.productVariant.deleteMany({\n                where: {\n                    productId: params.id\n                }\n            });\n            // Delete product categories (many-to-many relationships)\n            await tx.productCategory.deleteMany({\n                where: {\n                    productId: params.id\n                }\n            });\n            // Delete product reviews\n            await tx.review.deleteMany({\n                where: {\n                    productId: params.id\n                }\n            });\n            // Delete product FAQs\n            await tx.productFAQ.deleteMany({\n                where: {\n                    productId: params.id\n                }\n            });\n            // Delete wishlist items\n            await tx.wishlistItem.deleteMany({\n                where: {\n                    productId: params.id\n                }\n            });\n            // Finally delete the product\n            await tx.product.delete({\n                where: {\n                    id: params.id\n                }\n            });\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Product deleted successfully\",\n            type: \"hard_delete\"\n        });\n    } catch (error) {\n        console.error(\"Error deleting product:\", error);\n        // Check if it's a foreign key constraint error\n        if (error instanceof Error && error.message.includes(\"Foreign key constraint\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Cannot delete product because it is referenced in orders or other records. The product has been deactivated instead.\",\n                type: \"constraint_error\"\n            }, {\n                status: 409\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to delete product\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/products/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"error\"\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQ1hGLGdCQUFnQkUsTUFBTSxJQUN0QixJQUFJSCx3REFBWUEsQ0FBQztJQUNmSSxLQUFLO1FBQUM7S0FBUTtBQUNoQixHQUFFO0FBRUosSUFBSUMsSUFBeUIsRUFBY0osZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL2FwcC9saWIvZGIudHM/NThmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz9cbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ2Vycm9yJ10sIC8vIE9ubHkgbG9nIGVycm9ycywgbm8gcXVlcmllcyBvciB3YXJuaW5nc1xuICB9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();