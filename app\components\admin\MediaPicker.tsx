'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  X,
  Search,
  Upload,
  Image as ImageIcon,
  Video,
  File,
  Check,
  Grid3X3,
  List,
  Plus
} from 'lucide-react';
import { formatFileSize } from '../../lib/r2';

interface MediaFile {
  key: string;
  name: string;
  size: number;
  type: string;
  url: string;
  lastModified: Date;
  folder?: string;
}

interface MediaPickerProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (file: MediaFile) => void;
  allowedTypes?: string[];
  title?: string;
  multiple?: boolean;
  currentSelection?: string | string[];
}

const MediaPicker: React.FC<MediaPickerProps> = ({
  isOpen,
  onClose,
  onSelect,
  allowedTypes = ['image', 'video', 'document'],
  title = 'Select Media',
  multiple = false,
  currentSelection
}) => {
  const [files, setFiles] = useState<MediaFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFile, setSelectedFile] = useState<MediaFile | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<MediaFile[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load files
  useEffect(() => {
    if (isOpen) {
      loadFiles();
    }
  }, [isOpen]);

  // Handle file selection
  const handleFileSelect = (file: MediaFile) => {
    if (multiple) {
      const isSelected = selectedFiles.some(f => f.key === file.key);
      if (isSelected) {
        setSelectedFiles(selectedFiles.filter(f => f.key !== file.key));
      } else {
        setSelectedFiles([...selectedFiles, file]);
      }
    } else {
      setSelectedFile(file);
    }
  };

  // Handle confirm selection
  const handleConfirmSelection = () => {
    if (multiple) {
      selectedFiles.forEach(file => onSelect(file));
    } else if (selectedFile) {
      onSelect(selectedFile);
    }
    onClose();
  };

  const loadFiles = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/media/list');
      const data = await response.json();
      
      if (data.success) {
        setFiles(data.files.filter((file: MediaFile) => 
          allowedTypes.includes(file.type)
        ));
      }
    } catch (error) {
      console.error('Error loading files:', error);
    } finally {
      setLoading(false);
    }
  };

  // Filter files
  const filteredFiles = files.filter(file =>
    file.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get file icon
  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return <ImageIcon className="w-5 h-5" />;
      case 'video': return <Video className="w-5 h-5" />;
      default: return <File className="w-5 h-5" />;
    }
  };

  const handleSelect = () => {
    handleConfirmSelection();
    setSelectedFile(null);
    setSelectedFiles([]);
  };

  // Handle file upload
  const handleFileUpload = async (uploadFiles: FileList) => {
    setUploading(true);

    const uploadPromises = Array.from(uploadFiles).map(async (file) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'uploads');

      try {
        const response = await fetch('/api/media/upload', {
          method: 'POST',
          body: formData,
        });

        const result = await response.json();
        return result;
      } catch (error) {
        console.error(`Upload failed for ${file.name}:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Upload failed'
        };
      }
    });

    const results = await Promise.all(uploadPromises);
    const successful = results.filter(r => r.success);

    if (successful.length > 0) {
      // Refresh the files list
      loadFiles();
      // Auto-select the first uploaded file if single selection
      if (!multiple && successful[0]?.file) {
        setSelectedFile(successful[0].file);
      }
    }

    setUploading(false);
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const triggerFileUpload = () => {
    fileInputRef.current?.click();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl w-full max-w-4xl mx-4 max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Controls */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search files..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {/* Upload Button */}
              <button
                onClick={triggerFileUpload}
                disabled={uploading}
                className="flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {uploading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Uploading...
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    Upload
                  </>
                )}
              </button>

              {/* View Mode Toggle */}
              <div className="flex border border-gray-300 rounded-lg">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-green-600 text-white' : 'text-gray-600 hover:bg-gray-50'}`}
                >
                  <Grid3X3 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${viewMode === 'list' ? 'bg-green-600 text-white' : 'text-gray-600 hover:bg-gray-50'}`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Hidden File Input */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*,video/*,.pdf"
            multiple
            onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
            className="hidden"
          />
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
            </div>
          ) : filteredFiles.length === 0 ? (
            <div className="text-center py-12">
              <ImageIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 text-lg">No files found</p>
              <p className="text-gray-400">Try adjusting your search or upload new files</p>
            </div>
          ) : viewMode === 'grid' ? (
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {filteredFiles.map((file) => {
                const isSelected = multiple
                  ? selectedFiles.some(f => f.key === file.key)
                  : selectedFile?.key === file.key;

                return (
                <div
                  key={file.key}
                  onClick={() => handleFileSelect(file)}
                  className={`bg-white rounded-lg border-2 p-3 cursor-pointer transition-all hover:shadow-md ${
                    isSelected
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="relative">
                    {isSelected && (
                      <div className="absolute top-2 right-2 bg-green-600 text-white rounded-full p-1">
                        <Check className="w-3 h-3" />
                      </div>
                    )}
                    
                    <div className="aspect-square bg-gray-100 rounded-lg mb-3 flex items-center justify-center overflow-hidden">
                      {file.type === 'image' ? (
                        <img
                          src={file.url}
                          alt={file.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="text-gray-400">
                          {getFileIcon(file.type)}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-900 truncate" title={file.name}>
                      {file.name}
                    </p>
                    <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                  </div>
                </div>
                );
              })}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredFiles.map((file) => {
                const isSelected = multiple
                  ? selectedFiles.some(f => f.key === file.key)
                  : selectedFile?.key === file.key;

                return (
                <div
                  key={file.key}
                  onClick={() => handleFileSelect(file)}
                  className={`flex items-center p-3 rounded-lg border cursor-pointer transition-all ${
                    isSelected
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-3 overflow-hidden">
                    {file.type === 'image' ? (
                      <img
                        src={file.url}
                        alt={file.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="text-gray-400">
                        {getFileIcon(file.type)}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{file.name}</p>
                    <p className="text-xs text-gray-500">
                      {file.type} • {formatFileSize(file.size)} • {file.folder || 'Root'}
                    </p>
                  </div>
                  
                  {isSelected && (
                    <div className="bg-green-600 text-white rounded-full p-1 ml-3">
                      <Check className="w-4 h-4" />
                    </div>
                  )}
                </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="text-sm text-gray-500">
            {multiple
              ? `${selectedFiles.length} file(s) selected`
              : selectedFile
                ? `Selected: ${selectedFile.name}`
                : 'No file selected'
            }
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSelect}
              disabled={multiple ? selectedFiles.length === 0 : !selectedFile}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {multiple ? 'Select Files' : 'Select File'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MediaPicker;
