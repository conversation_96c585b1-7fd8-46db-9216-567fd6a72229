"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/route";
exports.ids = ["app/api/products/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_project_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/products/route.ts */ \"(rsc)/./app/api/products/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/route\",\n        pathname: \"/api/products\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\api\\\\products\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_project_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/products/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/products/route.ts":
/*!***********************************!*\
  !*** ./app/api/products/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _app_lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/db */ \"(rsc)/./app/lib/db.ts\");\n\n\n// GET /api/products - List all products\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const category = searchParams.get(\"category\");\n        const search = searchParams.get(\"search\");\n        const sort = searchParams.get(\"sort\") || \"random\"; // Default to random ordering\n        // Check if this is an admin request\n        const isAdminRequest = request.headers.get(\"x-admin-request\") === \"true\";\n        const skip = (page - 1) * limit;\n        const where = {};\n        // Only filter by isActive for non-admin requests\n        if (!isAdminRequest) {\n            where.isActive = true;\n        }\n        // Build AND conditions array\n        const andConditions = [];\n        if (category) {\n            andConditions.push({\n                OR: [\n                    // Match products with the category as primary category\n                    {\n                        category: {\n                            slug: category\n                        }\n                    },\n                    // Match products with the category in their many-to-many relationships\n                    {\n                        productCategories: {\n                            some: {\n                                category: {\n                                    slug: category\n                                }\n                            }\n                        }\n                    }\n                ]\n            });\n        }\n        if (search) {\n            andConditions.push({\n                OR: [\n                    {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        description: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                ]\n            });\n        }\n        if (andConditions.length > 0) {\n            where.AND = andConditions;\n        }\n        // Define ordering based on sort parameter\n        let orderBy;\n        switch(sort){\n            case \"name_asc\":\n                orderBy = {\n                    name: \"asc\"\n                };\n                break;\n            case \"name_desc\":\n                orderBy = {\n                    name: \"desc\"\n                };\n                break;\n            case \"price_asc\":\n                orderBy = {\n                    price: \"asc\"\n                };\n                break;\n            case \"price_desc\":\n                orderBy = {\n                    price: \"desc\"\n                };\n                break;\n            case \"newest\":\n                orderBy = {\n                    createdAt: \"desc\"\n                };\n                break;\n            case \"oldest\":\n                orderBy = {\n                    createdAt: \"asc\"\n                };\n                break;\n            case \"random\":\n            default:\n                // For random ordering, we'll use a different approach\n                orderBy = undefined;\n                break;\n        }\n        let products;\n        let total;\n        if (sort === \"random\") {\n            // For random ordering, we need to handle it differently\n            // First get the total count\n            total = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.count({\n                where\n            });\n            // For random ordering, we'll fetch all products and shuffle them\n            // This is acceptable for small to medium datasets\n            const allProducts = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findMany({\n                where,\n                include: {\n                    category: true,\n                    productCategories: {\n                        include: {\n                            category: true\n                        }\n                    },\n                    images: true,\n                    variants: true,\n                    _count: {\n                        select: {\n                            reviews: true\n                        }\n                    }\n                }\n            });\n            // Shuffle the products using Fisher-Yates algorithm\n            const shuffled = [\n                ...allProducts\n            ];\n            for(let i = shuffled.length - 1; i > 0; i--){\n                const j = Math.floor(Math.random() * (i + 1));\n                [shuffled[i], shuffled[j]] = [\n                    shuffled[j],\n                    shuffled[i]\n                ];\n            }\n            // Apply pagination to shuffled results\n            products = shuffled.slice(skip, skip + limit);\n        } else {\n            // For non-random sorting, use standard Prisma query\n            [products, total] = await Promise.all([\n                _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findMany({\n                    where,\n                    include: {\n                        category: true,\n                        productCategories: {\n                            include: {\n                                category: true\n                            }\n                        },\n                        images: true,\n                        variants: true,\n                        _count: {\n                            select: {\n                                reviews: true\n                            }\n                        }\n                    },\n                    orderBy,\n                    skip,\n                    take: limit\n                }),\n                _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.count({\n                    where\n                })\n            ]);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: products,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching products:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to fetch products\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/products - Create a new product\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { name, slug, description, shortDescription, price, comparePrice, categoryId, categoryIds = [], images, isFeatured, variations = [] } = body;\n        // Validate required fields\n        if (!name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Product name is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Allow zero or null base price - variations can provide pricing\n        let basePrice = price !== undefined ? parseFloat(price.toString()) : null;\n        // Validate that we have at least one pricing source\n        const hasValidBasePrice = basePrice !== null && basePrice >= 0;\n        const hasValidVariations = variations && variations.length > 0;\n        const warnings = [];\n        if (!hasValidBasePrice && !hasValidVariations) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Product must have either a base price (can be 0) or variations with pricing\"\n            }, {\n                status: 400\n            });\n        }\n        // Add warnings for edge cases\n        if (basePrice === 0 && (!variations || variations.length === 0)) {\n            warnings.push(\"Product has zero base price and no variations. Consider adding variations for pricing.\");\n        }\n        if (basePrice === 0 && variations && variations.length > 0) {\n            const hasZeroPricedVariations = variations.some((v)=>!v.price || v.price === 0);\n            if (hasZeroPricedVariations) {\n                warnings.push(\"Some variations have zero price. Ensure all variations have valid pricing.\");\n            }\n        }\n        // Default to 0 if no base price provided\n        if (basePrice === null) {\n            basePrice = 0;\n        }\n        // Use categoryIds if provided, otherwise fall back to single categoryId for backward compatibility\n        const categoriesToConnect = categoryIds.length > 0 ? categoryIds : categoryId ? [\n            categoryId\n        ] : [];\n        const product = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.create({\n            data: {\n                name,\n                slug,\n                description,\n                shortDescription,\n                price: basePrice,\n                comparePrice: comparePrice ? parseFloat(comparePrice.toString()) : null,\n                categoryId,\n                isFeatured: Boolean(isFeatured),\n                images: images ? {\n                    create: images.map((img, index)=>({\n                            url: img.url,\n                            alt: img.alt || name,\n                            position: index\n                        }))\n                } : undefined,\n                variants: variations.length > 0 ? {\n                    create: variations.map((variation)=>({\n                            name: variation.name,\n                            value: variation.value,\n                            price: variation.price || null,\n                            pricingMode: variation.pricingMode || \"INCREMENT\"\n                        }))\n                } : undefined,\n                productCategories: categoriesToConnect.length > 0 ? {\n                    create: categoriesToConnect.map((catId)=>({\n                            categoryId: catId\n                        }))\n                } : undefined\n            },\n            include: {\n                category: true,\n                productCategories: {\n                    include: {\n                        category: true\n                    }\n                },\n                images: true,\n                variants: true\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: product,\n            message: \"Product created successfully\",\n            warnings: warnings.length > 0 ? warnings : undefined\n        });\n    } catch (error) {\n        console.error(\"Error creating product:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to create product\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/products/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"error\"\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQ1hGLGdCQUFnQkUsTUFBTSxJQUN0QixJQUFJSCx3REFBWUEsQ0FBQztJQUNmSSxLQUFLO1FBQUM7S0FBUTtBQUNoQixHQUFFO0FBRUosSUFBSUMsSUFBeUIsRUFBY0osZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL2FwcC9saWIvZGIudHM/NThmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz9cbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ2Vycm9yJ10sIC8vIE9ubHkgbG9nIGVycm9ycywgbm8gcXVlcmllcyBvciB3YXJuaW5nc1xuICB9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();