import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/lib/auth';

// GET /api/users/[id]/preferences - Get user preferences
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const isAdmin = (session.user as any)?.role === 'ADMIN';
    const isOwnProfile = session.user.id === params.id;

    if (!isAdmin && !isOwnProfile) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    let preferences = await prisma.userPreference.findUnique({
      where: { userId: params.id },
    });

    // Create default preferences if they don't exist
    if (!preferences) {
      preferences = await prisma.userPreference.create({
        data: {
          userId: params.id,
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: preferences,
    });
  } catch (error) {
    console.error('Error fetching user preferences:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user preferences' },
      { status: 500 }
    );
  }
}

// PATCH /api/users/[id]/preferences - Update user preferences
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const isAdmin = (session.user as any)?.role === 'ADMIN';
    const isOwnProfile = session.user.id === params.id;

    if (!isAdmin && !isOwnProfile) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      language,
      theme,
      orderUpdates,
      promotions,
      newsletter,
      smsNotifications,
      // Notification preferences
      emailNotifications,
      inAppNotifications,
      orderNotifications,
      wishlistNotifications,
      reviewNotifications,
      priceDropAlerts,
      adminMessages,
      broadcastMessages,
    } = body;

    // Update or create preferences
    const preferences = await prisma.userPreference.upsert({
      where: { userId: params.id },
      update: {
        ...(language !== undefined && { language }),
        ...(theme !== undefined && { theme }),
        ...(orderUpdates !== undefined && { orderUpdates }),
        ...(promotions !== undefined && { promotions }),
        ...(newsletter !== undefined && { newsletter }),
        ...(smsNotifications !== undefined && { smsNotifications }),
        // Notification preferences
        ...(emailNotifications !== undefined && { emailNotifications }),
        ...(inAppNotifications !== undefined && { inAppNotifications }),
        ...(orderNotifications !== undefined && { orderNotifications }),
        ...(wishlistNotifications !== undefined && { wishlistNotifications }),
        ...(reviewNotifications !== undefined && { reviewNotifications }),
        ...(priceDropAlerts !== undefined && { priceDropAlerts }),
        ...(adminMessages !== undefined && { adminMessages }),
        ...(broadcastMessages !== undefined && { broadcastMessages }),
      },
      create: {
        userId: params.id,
        language: language || 'en-US',
        theme: theme || 'light',
        orderUpdates: orderUpdates !== undefined ? orderUpdates : true,
        promotions: promotions !== undefined ? promotions : false,
        newsletter: newsletter !== undefined ? newsletter : true,
        smsNotifications: smsNotifications !== undefined ? smsNotifications : false,
        // Notification preferences defaults
        emailNotifications: emailNotifications !== undefined ? emailNotifications : true,
        inAppNotifications: inAppNotifications !== undefined ? inAppNotifications : true,
        orderNotifications: orderNotifications !== undefined ? orderNotifications : true,
        wishlistNotifications: wishlistNotifications !== undefined ? wishlistNotifications : true,
        reviewNotifications: reviewNotifications !== undefined ? reviewNotifications : true,
        priceDropAlerts: priceDropAlerts !== undefined ? priceDropAlerts : false,
        adminMessages: adminMessages !== undefined ? adminMessages : true,
        broadcastMessages: broadcastMessages !== undefined ? broadcastMessages : true,
      },
    });

    return NextResponse.json({
      success: true,
      data: preferences,
      message: 'Preferences updated successfully',
    });
  } catch (error) {
    console.error('Error updating user preferences:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update user preferences' },
      { status: 500 }
    );
  }
}