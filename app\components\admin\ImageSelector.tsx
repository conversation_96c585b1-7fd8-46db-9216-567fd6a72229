'use client';

import React, { useState } from 'react';
import { Image as ImageIcon, X, Upload } from 'lucide-react';
import MediaPicker from './MediaPicker';

interface MediaFile {
  key: string;
  name: string;
  size: number;
  type: string;
  url: string;
  lastModified: Date;
  folder?: string;
}

interface ImageSelectorProps {
  value?: string;
  onChange: (url: string) => void;
  label?: string;
  placeholder?: string;
  className?: string;
}

const ImageSelector: React.FC<ImageSelectorProps> = ({
  value,
  onChange,
  label = 'Image',
  placeholder = 'Select an image',
  className = ''
}) => {
  const [showMediaPicker, setShowMediaPicker] = useState(false);

  const handleMediaSelect = (file: MediaFile) => {
    onChange(file.url);
  };

  const handleRemoveImage = () => {
    onChange('');
  };

  return (
    <div className={className}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
        </label>
      )}
      
      <div className="space-y-3">
        {/* Current Image Preview */}
        {value && (
          <div className="relative inline-block">
            <img
              src={value}
              alt="Selected image"
              className="w-32 h-32 object-cover rounded-lg border border-gray-300"
            />
            <button
              type="button"
              onClick={handleRemoveImage}
              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
            >
              <X className="w-3 h-3" />
            </button>
          </div>
        )}

        {/* Selection Buttons */}
        <div className="flex items-center space-x-3">
          <button
            type="button"
            onClick={() => setShowMediaPicker(true)}
            className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <ImageIcon className="w-4 h-4 mr-2" />
            {value ? 'Change Image' : 'Select Image'}
          </button>
          
          {!value && (
            <span className="text-sm text-gray-500">{placeholder}</span>
          )}
        </div>

        {/* Manual URL Input */}
        <div>
          <input
            type="url"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder="Or enter image URL directly"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm"
          />
        </div>
      </div>

      {/* Media Picker Modal */}
      <MediaPicker
        isOpen={showMediaPicker}
        onClose={() => setShowMediaPicker(false)}
        onSelect={handleMediaSelect}
        allowedTypes={['image']}
        title="Select Image"
      />
    </div>
  );
};

export default ImageSelector;
