# Applying Migration to Xata Database

Since Xata doesn't support Prisma's shadow database feature, you need to apply the migration manually. Here are the steps:

## Option 1: Using Xata's Web Interface

1. Go to your Xata dashboard at https://app.xata.io/
2. Navigate to your database "herbalicious:main"
3. Go to the "orders" table
4. Add the following columns:
   - `trackingNumber` (type: text, nullable)
   - `estimatedDelivery` (type: text, nullable)

## Option 2: Using Xata CLI

If you have the Xata CLI installed, you can run:

```bash
xata schema edit
```

Then add these fields to the orders table in the schema file:

```json
{
  "name": "trackingNumber",
  "type": "text",
  "nullable": true
},
{
  "name": "estimatedDelivery",
  "type": "text",
  "nullable": true
}
```

## Option 3: Using Prisma with Xata (Production-safe)

Since the schema is already updated in your Prisma schema file, you can use:

```bash
npx prisma db push
```

This command will sync your Prisma schema with the database without using migrations. It's safe for Xata as it doesn't require a shadow database.

## After applying the changes:

1. Run `npx prisma generate` to regenerate the Prisma client
2. The TypeScript errors should be resolved