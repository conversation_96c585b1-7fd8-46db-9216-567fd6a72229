'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowLeft, Heart, ShoppingCart, X, Star, Loader2 } from 'lucide-react';
import { useCart } from '../../context/CartContext';
import { Product } from '../../types';
import { WishlistSkeleton } from '../loaders/SkeletonLoaders';

interface WishlistItem {
  id: string;
  name: string;
  slug: string;
  price: number;
  shortDescription: string;
  image: string;
  rating: number;
  reviews: number;
  wishlistItemId: string;
}

const Wishlist: React.FC = () => {
  const router = useRouter();
  const { data: session, status } = useSession();
  const { dispatch } = useCart();
  
  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [recommendations, setRecommendations] = useState<WishlistItem[]>([]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'loading') return;
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // Fetch wishlist items
  useEffect(() => {
    const fetchWishlist = async () => {
      if (!session?.user?.id) {
        setLoading(false);
        return;
      }

      try {
        const response = await fetch('/api/wishlist');
        if (!response.ok) {
          throw new Error('Failed to fetch wishlist');
        }
        
        const data = await response.json();
        setWishlistItems(data.items || []);
      } catch (err) {
        console.error('Error fetching wishlist:', err);
        setError('Failed to load wishlist');
      } finally {
        setLoading(false);
      }
    };

    fetchWishlist();
  }, [session]);

  // Fetch recommendations
  useEffect(() => {
    const fetchRecommendations = async () => {
      try {
        const response = await fetch('/api/products?limit=4');
        if (response.ok) {
          const data = await response.json();
          setRecommendations(data.data || []);
        }
      } catch (err) {
        console.error('Error fetching recommendations:', err);
      }
    };

    fetchRecommendations();
  }, []);

  const removeFromWishlist = async (productId: string) => {
    try {
      const response = await fetch(`/api/wishlist?productId=${productId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to remove from wishlist');
      }

      // Update local state
      setWishlistItems(prev => prev.filter(item => item.id !== productId));
    } catch (err) {
      console.error('Error removing from wishlist:', err);
      // You might want to show a toast notification here
    }
  };

  const addToCart = (wishlistItem: WishlistItem) => {
    // Convert WishlistItem to Product format for cart
    const product: Product = {
      id: wishlistItem.id,
      name: wishlistItem.name,
      description: wishlistItem.shortDescription, // Use shortDescription as description
      shortDescription: wishlistItem.shortDescription,
      price: wishlistItem.price,
      image: wishlistItem.image,
      category: 'general', // Default category
      featured: false, // Default featured status
     
      benefits: [], // Default empty benefits
      rating: wishlistItem.rating,
      reviews: wishlistItem.reviews,
    };
    dispatch({ type: 'ADD_ITEM', payload: product });
  };

  const addAllToCart = () => {
    wishlistItems.forEach(wishlistItem => {
      // Convert WishlistItem to Product format for cart
      const product: Product = {
        id: wishlistItem.id,
        name: wishlistItem.name,
        description: wishlistItem.shortDescription, // Use shortDescription as description
        shortDescription: wishlistItem.shortDescription,
        price: wishlistItem.price,
        image: wishlistItem.image,
        category: 'general', // Default category
        featured: false, // Default featured status
      
        benefits: [], // Default empty benefits
        rating: wishlistItem.rating,
        reviews: wishlistItem.reviews,
      };
      dispatch({ type: 'ADD_ITEM', payload: product });
    });
  };

  // Show loading state while checking authentication
  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-green-600" />
      </div>
    );
  }

  // Don't render content if not authenticated
  if (status === 'unauthenticated') {
    return null;
  }

  return (
    <div className="lg:grid lg:grid-cols-12 lg:gap-8">
      {/* Mobile Layout */}
      <div className="lg:hidden">
        {/* Header */}
        <div className="sticky top-16 bg-white z-30 px-4 py-4 border-b">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.back()}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-xl font-bold text-gray-800">Wishlist</h1>
            <div className="ml-auto">
              <span className="text-sm text-gray-600">{wishlistItems.length} items</span>
            </div>
          </div>
        </div>

        <div className="px-4 py-6">
          {loading ? (
            <WishlistSkeleton />
          ) : error ? (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <X className="w-12 h-12 text-red-500" />
              </div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">Error loading wishlist</h2>
              <p className="text-gray-600 mb-6">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          ) : wishlistItems.length > 0 ? (
            <>
              {/* Add All to Cart */}
              <div className="mb-6">
                <button
                  onClick={addAllToCart}
                  className="w-full bg-green-600 text-white py-3 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center justify-center space-x-2"
                >
                  <ShoppingCart className="w-5 h-5" />
                  <span>Add All to Cart</span>
                </button>
              </div>

              {/* Wishlist Items */}
              <div className="space-y-4">
                {wishlistItems.map((product) => (
                  <div key={product.id} className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                    <div className="flex space-x-4">
                      <Link href={`/product/${product.id}`} className="block">
                        <div className="w-20 h-20 relative rounded-xl overflow-hidden flex-shrink-0">
                          <Image
                            src={product.image || '/placeholder-product.jpg'}
                            alt={product.name}
                            fill
                            className="object-cover"
                            sizes="80px"
                          />
                        </div>
                      </Link>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <Link href={`/product/${product.id}`}>
                            <h3 className="font-semibold text-gray-800 line-clamp-1">{product.name}</h3>
                          </Link>
                          <button
                            onClick={() => removeFromWishlist(product.id)}
                            className="p-1 text-red-500 hover:bg-red-50 rounded-full transition-colors ml-2"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-2 line-clamp-1">{product.shortDescription}</p>
                        
                        <div className="flex items-center space-x-2 mb-3">
                          <div className="flex items-center space-x-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span className="text-sm font-medium text-gray-700">{product.rating}</span>
                          </div>
                          <span className="text-sm text-gray-500">({product.reviews} reviews)</span>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-lg font-bold text-gray-900">₹{product.price}</span>
                          <button
                            onClick={() => addToCart(product)}
                            className="bg-green-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-green-700 transition-colors flex items-center space-x-1"
                          >
                            <ShoppingCart className="w-4 h-4" />
                            <span>Add to Cart</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Recommendations */}
              <div className="mt-8">
                <h2 className="text-lg font-semibold text-gray-800 mb-4">You might also like</h2>
                <div className="grid grid-cols-2 gap-4">
                  {recommendations.slice(0, 4).map((product) => (
                    <Link key={product.id} href={`/product/${product.id}`} className="block">
                      <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                        <div className="w-full h-32 relative rounded-xl overflow-hidden mb-3">
                          <Image
                            src={product.image || '/placeholder-product.jpg'}
                            alt={product.name}
                            fill
                            className="object-cover"
                            sizes="(max-width: 768px) 50vw, 25vw"
                          />
                        </div>
                        <h3 className="font-medium text-gray-800 text-sm line-clamp-1 mb-1">{product.name}</h3>
                        <p className="text-xs text-gray-600 line-clamp-1 mb-2">{product.shortDescription}</p>
                        <div className="flex items-center justify-between">
                          <span className="font-bold text-gray-900">₹{product.price}</span>
                          <button className="p-1 text-gray-400 hover:text-red-500 transition-colors">
                            <Heart className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Heart className="w-12 h-12 text-gray-400" />
              </div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">Your wishlist is empty</h2>
              <p className="text-gray-600 mb-6">Save products you love for later</p>
              <Link
                href="/shop"
                className="inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:bg-green-700 transition-colors"
              >
                Start Shopping
              </Link>
            </div>
          )}
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:block lg:col-span-12">
        <div className="py-8">
          {/* Header */}
          <div className="flex items-center mb-8">
            <button
              onClick={() => router.back()}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Back</span>
            </button>
          </div>

          <div className="flex items-center justify-between mb-8">
            <h1 className="text-4xl font-bold text-gray-800">My Wishlist</h1>
            <span className="text-gray-600">{wishlistItems.length} items saved</span>
          </div>

          {loading ? (
            <WishlistSkeleton />
          ) : error ? (
            <div className="text-center py-16">
              <div className="w-32 h-32 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-8">
                <X className="w-16 h-16 text-red-500" />
              </div>
              <h2 className="text-3xl font-bold text-gray-800 mb-4">Error loading wishlist</h2>
              <p className="text-xl text-gray-600 mb-8">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors text-lg"
              >
                Try Again
              </button>
            </div>
          ) : wishlistItems.length > 0 ? (
            <>
              {/* Add All to Cart */}
              <div className="mb-8">
                <button
                  onClick={addAllToCart}
                  className="bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors flex items-center space-x-3 text-lg"
                >
                  <ShoppingCart className="w-6 h-6" />
                  <span>Add All to Cart</span>
                </button>
              </div>

              {/* Wishlist Items */}
              <div className="grid grid-cols-3 gap-6 mb-12">
                {wishlistItems.map((product) => (
                  <div key={product.id} className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
                    <div className="relative mb-4">
                      <Link href={`/product/${product.id}`}>
                        <div className="w-full h-48 relative rounded-xl overflow-hidden">
                          <Image
                            src={product.image || '/placeholder-product.jpg'}
                            alt={product.name}
                            fill
                            className="object-cover hover:scale-105 transition-transform duration-300"
                            sizes="(max-width: 1200px) 50vw, 33vw"
                          />
                        </div>
                      </Link>
                      <button
                        onClick={() => removeFromWishlist(product.id)}
                        className="absolute top-3 right-3 p-2 bg-white rounded-full shadow-sm text-red-500 hover:bg-red-50 transition-colors"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                    
                    <Link href={`/product/${product.id}`}>
                      <h3 className="font-semibold text-gray-800 mb-2 hover:text-green-600 transition-colors">
                        {product.name}
                      </h3>
                    </Link>
                    
                    <p className="text-gray-600 mb-3 line-clamp-2">{product.shortDescription}</p>
                    
                    <div className="flex items-center space-x-2 mb-4">
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="font-medium text-gray-700">{product.rating}</span>
                      </div>
                      <span className="text-gray-500">({product.reviews} reviews)</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-xl font-bold text-gray-900">₹{product.price}</span>
                      <button
                        onClick={() => addToCart(product)}
                        className="bg-green-600 text-white px-4 py-2 rounded-xl font-medium hover:bg-green-700 transition-colors flex items-center space-x-2"
                      >
                        <ShoppingCart className="w-4 h-4" />
                        <span>Add to Cart</span>
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Recommendations */}
              <div>
                <h2 className="text-2xl font-semibold text-gray-800 mb-6">You might also like</h2>
                <div className="grid grid-cols-4 gap-6">
                  {recommendations.slice(0, 4).map((product) => (
                    <Link key={product.id} href={`/product/${product.id}`} className="block group">
                      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
                        <div className="relative mb-4">
                          <div className="w-full h-40 relative rounded-xl overflow-hidden">
                            <Image
                              src={product.image || '/placeholder-product.jpg'}
                              alt={product.name}
                              fill
                              className="object-cover group-hover:scale-105 transition-transform duration-300"
                              sizes="(max-width: 1200px) 50vw, 25vw"
                            />
                          </div>
                          <button className="absolute top-3 right-3 p-2 bg-white rounded-full shadow-sm text-gray-400 hover:text-red-500 transition-colors">
                            <Heart className="w-4 h-4" />
                          </button>
                        </div>
                        <h3 className="font-medium text-gray-800 mb-2 group-hover:text-green-600 transition-colors line-clamp-1">
                          {product.name}
                        </h3>
                        <p className="text-gray-600 text-sm line-clamp-2 mb-3">{product.shortDescription}</p>
                        <div className="flex items-center justify-between">
                          <span className="font-bold text-gray-900">₹{product.price}</span>
                          <div className="flex items-center space-x-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span className="text-sm font-medium text-gray-700">{product.rating}</span>
                          </div>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <div className="w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-8">
                <Heart className="w-16 h-16 text-gray-400" />
              </div>
              <h2 className="text-3xl font-bold text-gray-800 mb-4">Your wishlist is empty</h2>
              <p className="text-xl text-gray-600 mb-8">Save products you love for later and never lose track of them</p>
              <Link
                href="/shop"
                className="inline-flex items-center bg-green-600 text-white px-8 py-4 rounded-2xl font-semibold hover:bg-green-700 transition-colors text-lg"
              >
                Start Shopping
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Wishlist;