/**
 * Migration script to populate the new ProductCategory many-to-many relationship
 * with data from the existing single category relationship.
 * 
 * This script should be run after the database migration is applied.
 * 
 * Usage: node scripts/migrate-product-categories.js
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function migrateProductCategories() {
  console.log('Starting migration of product categories...');
  
  try {
    // Get all products that have a categoryId but no productCategories
    const products = await prisma.product.findMany({
      where: {
        categoryId: {
          not: null,
        },
      },
      include: {
        productCategories: true,
        category: true,
      },
    });

    console.log(`Found ${products.length} products with primary categories.`);

    let migratedCount = 0;
    let skippedCount = 0;

    for (const product of products) {
      // Check if this product already has the category in the many-to-many relationship
      const existingRelation = product.productCategories.find(
        pc => pc.categoryId === product.categoryId
      );

      if (existingRelation) {
        console.log(`Product "${product.name}" already has category relationship, skipping.`);
        skippedCount++;
        continue;
      }

      // Create the many-to-many relationship
      await prisma.productCategory.create({
        data: {
          productId: product.id,
          categoryId: product.categoryId,
        },
      });

      console.log(`Migrated product "${product.name}" to category "${product.category.name}"`);
      migratedCount++;
    }

    console.log(`Migration completed!`);
    console.log(`- Migrated: ${migratedCount} products`);
    console.log(`- Skipped: ${skippedCount} products`);
    
  } catch (error) {
    console.error('Error during migration:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
if (require.main === module) {
  migrateProductCategories()
    .then(() => {
      console.log('Migration script completed successfully.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateProductCategories };
