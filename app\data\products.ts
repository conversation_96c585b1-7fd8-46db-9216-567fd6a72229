import { Product } from '../types';

export const products: Product[] = [
  {
    id: '1',
    name: 'Botanical Cleanser',
    description: 'A gentle, plant-based cleanser that removes impurities while maintaining your skin\'s natural moisture barrier. Formulated with organic chamomile and green tea extracts.',
    shortDescription: 'Gentle plant-based cleanser',
    price: 28.99,
    image: 'https://images.pexels.com/photos/7755467/pexels-photo-7755467.jpeg?auto=compress&cs=tinysrgb&w=400',
    images: [
      {
        id: '1-1',
        url: 'https://images.pexels.com/photos/7755467/pexels-photo-7755467.jpeg?auto=compress&cs=tinysrgb&w=800',
        alt: 'Botanical Cleanser - Main Product',
        position: 0
      },
      {
        id: '1-2',
        url: 'https://images.pexels.com/photos/6621076/pexels-photo-6621076.jpeg?auto=compress&cs=tinysrgb&w=800',
        alt: 'Botanical Cleanser - Ingredients',
        position: 1
      },
      {
        id: '1-3',
        url: 'https://images.pexels.com/photos/7755398/pexels-photo-7755398.jpeg?auto=compress&cs=tinysrgb&w=800',
        alt: 'Botanical Cleanser - Application',
        position: 2
      }
    ],
    category: 'cleanser',
    featured: true,
    ingredients: ['Chamomile Extract', 'Green Tea', 'Aloe Vera', 'Coconut Oil'],
    benefits: ['Gentle cleansing', 'Anti-inflammatory', 'Hydrating'],
    rating: 4.8,
    reviews: 156
  },
  {
    id: '2',
    name: 'Hydrating Serum',
    description: 'Lightweight, fast-absorbing serum packed with hyaluronic acid and botanical extracts to plump and hydrate your skin for a radiant glow.',
    shortDescription: 'Deep hydration with botanicals',
    price: 45.99,
    image: 'https://images.pexels.com/photos/6621076/pexels-photo-6621076.jpeg?auto=compress&cs=tinysrgb&w=400',
    images: [
      {
        id: '2-1',
        url: 'https://images.pexels.com/photos/6621076/pexels-photo-6621076.jpeg?auto=compress&cs=tinysrgb&w=800',
        alt: 'Hydrating Serum - Main Product',
        position: 0
      },
      {
        id: '2-2',
        url: 'https://images.pexels.com/photos/7755467/pexels-photo-7755467.jpeg?auto=compress&cs=tinysrgb&w=800',
        alt: 'Hydrating Serum - Texture',
        position: 1
      },
      {
        id: '2-3',
        url: 'https://images.pexels.com/photos/7755398/pexels-photo-7755398.jpeg?auto=compress&cs=tinysrgb&w=800',
        alt: 'Hydrating Serum - Before After',
        position: 2
      },
      {
        id: '2-4',
        url: 'https://images.pexels.com/photos/6621076/pexels-photo-6621076.jpeg?auto=compress&cs=tinysrgb&w=800',
        alt: 'Hydrating Serum - Packaging',
        position: 3
      }
    ],
    category: 'serum',
    featured: true,
    ingredients: ['Hyaluronic Acid', 'Rose Hip Oil', 'Vitamin C', 'Peptides'],
    benefits: ['Deep hydration', 'Anti-aging', 'Brightening'],
    rating: 4.9,
    reviews: 203
  },
  {
    id: '3',
    name: 'Nourishing Moisturizer',
    description: 'Rich, creamy moisturizer with organic shea butter and botanical oils that deeply nourishes and protects your skin throughout the day.',
    shortDescription: 'Rich daily moisturizer',
    price: 35.99,
    image: 'https://images.pexels.com/photos/7755398/pexels-photo-7755398.jpeg?auto=compress&cs=tinysrgb&w=400',
    category: 'moisturizer',
    featured: false,
    ingredients: ['Shea Butter', 'Jojoba Oil', 'Ceramides', 'Vitamin E'],
    benefits: ['Long-lasting moisture', 'Skin barrier repair', 'Anti-aging'],
    rating: 4.7,
    reviews: 98
  },
  {
    id: '4',
    name: 'Rejuvenating Face Mask',
    description: 'Weekly treatment mask infused with clay minerals and botanical extracts to deeply cleanse pores and reveal brighter, smoother skin.',
    shortDescription: 'Weekly clay treatment',
    price: 32.99,
    image: 'https://images.pexels.com/photos/7755467/pexels-photo-7755467.jpeg?auto=compress&cs=tinysrgb&w=400',
    category: 'mask',
    featured: true,
    ingredients: ['Bentonite Clay', 'Rosemary Extract', 'Honey', 'Lavender Oil'],
    benefits: ['Deep cleansing', 'Pore refinement', 'Brightening'],
    rating: 4.6,
    reviews: 74
  },
  {
    id: '5',
    name: 'Gentle Exfoliator',
    description: 'Natural bamboo and fruit enzyme exfoliator that gently removes dead skin cells, revealing a fresh, radiant complexion.',
    shortDescription: 'Natural bamboo exfoliator',
    price: 29.99,
    image: 'https://images.pexels.com/photos/6621076/pexels-photo-6621076.jpeg?auto=compress&cs=tinysrgb&w=400',
    category: 'exfoliator',
    featured: false,
    ingredients: ['Bamboo Powder', 'Papaya Enzyme', 'Oat Extract', 'Chamomile'],
    benefits: ['Gentle exfoliation', 'Skin renewal', 'Smoothing'],
    rating: 4.5,
    reviews: 89
  },
  {
    id: '6',
    name: 'Eye Care Cream',
    description: 'Delicate eye cream with peptides and botanical extracts to reduce fine lines, puffiness, and dark circles for brighter-looking eyes.',
    shortDescription: 'Anti-aging eye treatment',
    price: 39.99,
    image: 'https://images.pexels.com/photos/7755398/pexels-photo-7755398.jpeg?auto=compress&cs=tinysrgb&w=400',
    category: 'eye-care',
    featured: false,
    ingredients: ['Peptides', 'Caffeine', 'Cucumber Extract', 'Retinol'],
    benefits: ['Reduces fine lines', 'Brightens dark circles', 'Firms skin'],
    rating: 4.4,
    reviews: 67
  }
];

export const categories = [
  { id: 'all', name: 'All Products', icon: '🌿' },
  { id: 'cleanser', name: 'Cleansers', icon: '🧼' },
  { id: 'serum', name: 'Serums', icon: '💧' },
  { id: 'moisturizer', name: 'Moisturizers', icon: '🧴' },
  { id: 'mask', name: 'Masks', icon: '🥒' },
  { id: 'exfoliator', name: 'Exfoliators', icon: '✨' },
  { id: 'eye-care', name: 'Eye Care', icon: '👁️' }
];