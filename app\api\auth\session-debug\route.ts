import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../lib/auth';
import { prisma } from '../../../lib/db';
import { getToken } from 'next-auth/jwt';

interface ExtendedSessionUser {
  id?: string;
  email?: string | null;
  name?: string | null;
  role?: string;
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });
    
    if (!session?.user) {
      return NextResponse.json({
        authenticated: false,
        message: 'No session found',
        token: token ? {
          sub: token.sub,
          email: token.email,
          role: token.role
        } : null
      });
    }

    // Check if user exists in database by email and by ID
    let dbUserByEmail = null;
    let dbUserById = null;

    if (session.user.email) {
      dbUserByEmail = await prisma.user.findUnique({
        where: { email: session.user.email },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          createdAt: true
        }
      });
    }

    if (session.user.id) {
      try {
        dbUserById = await prisma.user.findUnique({
          where: { id: session.user.id },
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
            createdAt: true
          }
        });
      } catch (error) {
        // ID might be invalid format - silently continue
      }
    }

    return NextResponse.json({
      authenticated: true,
      session: {
        user: {
          id: session.user.id,
          email: session.user.email,
          name: session.user.name,
          role: (session.user as ExtendedSessionUser).role
        }
      },
      token: token ? {
        sub: token.sub,
        email: token.email,
        role: token.role
      } : null,
      databaseLookups: {
        byEmail: dbUserByEmail,
        byId: dbUserById
      },
      validation: {
        hasSessionId: !!session.user.id,
        hasSessionEmail: !!session.user.email,
        userExistsByEmail: !!dbUserByEmail,
        userExistsById: !!dbUserById,
        idsMatch: dbUserByEmail?.id === session.user.id,
        emailsMatch: dbUserByEmail?.email === session.user.email
      }
    });

  } catch (error) {
    console.error('Session debug error:', error);
    return NextResponse.json(
      { error: 'Failed to check session', details: error },
      { status: 500 }
    );
  }
} 