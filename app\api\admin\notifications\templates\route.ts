import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../../lib/auth';
import { prisma } from '../../../../lib/db';
import { logger } from '../../../../lib/logger';

/**
 * GET /api/admin/notifications/templates
 * Get notification templates for admin
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Only allow admin users
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 401 });
    }

    const templates = await prisma.notificationTemplate.findMany({
      select: {
        id: true,
        name: true,
        title: true,
        message: true,
        emailSubject: true,
        emailTemplate: true,
        type: true,
        isActive: true,
        createdAt: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    logger.info('Admin fetched notification templates', {
      adminId: session.user.id,
      templateCount: templates.length,
    });

    return NextResponse.json({
      success: true,
      templates,
    });

  } catch (error) {
    logger.error('Failed to fetch notification templates', error as Error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
