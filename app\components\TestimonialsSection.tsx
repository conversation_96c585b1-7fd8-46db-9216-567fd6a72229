'use client';

import React, { useState, useEffect } from 'react';
import { Star, Quote } from 'lucide-react';

interface Testimonial {
  id: string;
  name: string;
  content: string;
  rating: number;
  image?: string;
  position?: string;
  company?: string;
}

interface TestimonialsSectionProps {
  title?: string;
  subtitle?: string;
  backgroundColor?: string;
}

const TestimonialsSection: React.FC<TestimonialsSectionProps> = ({
  title = "What Our Customers Say",
  subtitle = "Real reviews from real customers who love our natural skincare",
  backgroundColor = "#f0fdf4"
}) => {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        const response = await fetch('/api/testimonials?active=true');
        const data = await response.json();

        if (data.success) {
          setTestimonials(data.data);
        }
      } catch (error) {
        console.error('Error fetching testimonials:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);
  return (
    <div 
      className="px-4 py-8 lg:px-8 lg:py-12"
      style={{ backgroundColor }}
    >
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8 lg:mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-2 lg:text-3xl">
            {title}
          </h2>
          <p className="text-gray-600 lg:text-lg">
            {subtitle}
          </p>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-2xl p-6 shadow-sm animate-pulse">
                <div className="w-8 h-8 bg-gray-200 rounded mb-4"></div>
                <div className="flex space-x-1 mb-3">
                  {[...Array(5)].map((_, j) => (
                    <div key={j} className="w-4 h-4 bg-gray-200 rounded"></div>
                  ))}
                </div>
                <div className="space-y-2 mb-4">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                </div>
                <div className="border-t border-gray-100 pt-3">
                  <div className="h-4 bg-gray-200 rounded mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : testimonials.length === 0 ? (
          <div className="text-center py-12">
            <Quote className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No testimonials available at the moment.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {testimonials.map((testimonial) => (
              <div
                key={testimonial.id}
                className="bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300"
              >
                <div className="flex items-center mb-4">
                  <Quote className="w-8 h-8 text-green-500 opacity-50" />
                </div>

                <div className="flex items-center mb-3">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-4 h-4 ${
                        i < testimonial.rating
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>

                <p className="text-gray-700 text-sm mb-4 leading-relaxed">
                  "{testimonial.content}"
                </p>

                <div className="border-t border-gray-100 pt-3">
                  <p className="font-semibold text-gray-900 text-sm">
                    {testimonial.name}
                  </p>
                  {testimonial.position && testimonial.company && (
                    <p className="text-gray-500 text-xs">
                      {testimonial.position} at {testimonial.company}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="text-center mt-8">
          <div className="inline-flex items-center bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-medium">
            <Star className="w-4 h-4 mr-1 fill-current" />
            4.9/5 Average Rating from 500+ Reviews
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestimonialsSection;
