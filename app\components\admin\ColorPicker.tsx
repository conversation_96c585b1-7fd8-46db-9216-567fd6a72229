'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Palette, Check } from 'lucide-react';

interface ColorPickerProps {
  value: string;
  onChange: (color: string) => void;
  label?: string;
  presetColors?: string[];
  className?: string;
}

const ColorPicker: React.FC<ColorPickerProps> = ({
  value,
  onChange,
  label,
  presetColors = [
    '#22c55e', // green-500
    '#16a34a', // green-600
    '#15803d', // green-700
    '#f0fdf4', // green-50
    '#dcfce7', // green-100
    '#bbf7d0', // green-200
    '#86efac', // green-300
    '#4ade80', // green-400
    '#3b82f6', // blue-500
    '#6366f1', // indigo-500
    '#8b5cf6', // violet-500
    '#ec4899', // pink-500
    '#f59e0b', // amber-500
    '#ef4444', // red-500
    '#6b7280', // gray-500
    '#1f2937', // gray-800
  ],
  className = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [customColor, setCustomColor] = useState(value);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleColorSelect = (color: string) => {
    onChange(color);
    setCustomColor(color);
    setIsOpen(false);
  };

  const handleCustomColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const color = e.target.value;
    setCustomColor(color);
    onChange(color);
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>
      )}
      
      {/* Color Trigger */}
      <div
        onClick={() => setIsOpen(!isOpen)}
        className="w-full h-10 border border-gray-300 rounded-md cursor-pointer flex items-center px-3 bg-white hover:border-gray-400 transition-colors"
      >
        <div
          className="w-6 h-6 rounded border border-gray-300 mr-3 flex-shrink-0"
          style={{ backgroundColor: value }}
        />
        <span className="text-sm text-gray-700 flex-1">{value}</span>
        <Palette className="w-4 h-4 text-gray-400" />
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg p-4">
          {/* Preset Colors */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Preset Colors</h4>
            <div className="grid grid-cols-8 gap-2">
              {presetColors.map((color) => (
                <button
                  key={color}
                  onClick={() => handleColorSelect(color)}
                  className="w-8 h-8 rounded border border-gray-300 hover:scale-110 transition-transform relative"
                  style={{ backgroundColor: color }}
                  title={color}
                >
                  {value === color && (
                    <Check className="w-4 h-4 text-white absolute inset-0 m-auto drop-shadow-sm" />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Custom Color Input */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Custom Color</h4>
            <div className="flex items-center space-x-2">
              <input
                type="color"
                value={customColor}
                onChange={handleCustomColorChange}
                className="w-10 h-8 border border-gray-300 rounded cursor-pointer"
              />
              <input
                type="text"
                value={customColor}
                onChange={(e) => {
                  setCustomColor(e.target.value);
                  if (e.target.value.match(/^#[0-9A-Fa-f]{6}$/)) {
                    onChange(e.target.value);
                  }
                }}
                placeholder="#22c55e"
                className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
              />
            </div>
          </div>

          {/* Brand Color Suggestions */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Brand Colors</h4>
            <div className="grid grid-cols-4 gap-2">
              {[
                { name: 'Primary Green', color: '#22c55e' },
                { name: 'Dark Green', color: '#16a34a' },
                { name: 'Light Green', color: '#f0fdf4' },
                { name: 'Green Accent', color: '#dcfce7' },
              ].map(({ name, color }) => (
                <button
                  key={color}
                  onClick={() => handleColorSelect(color)}
                  className="flex flex-col items-center p-2 rounded hover:bg-gray-50 transition-colors"
                  title={name}
                >
                  <div
                    className="w-6 h-6 rounded border border-gray-300 mb-1"
                    style={{ backgroundColor: color }}
                  />
                  <span className="text-xs text-gray-600 text-center">{name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ColorPicker;
