'use client';

import React, { useState } from 'react';
import { Mail, Check, AlertCircle, Sparkles } from 'lucide-react';

interface NewsletterSignupProps {
  title?: string;
  subtitle?: string;
  backgroundColor?: string;
  className?: string;
  source?: string;
}

const NewsletterSignup: React.FC<NewsletterSignupProps> = ({
  title = "Stay Updated",
  subtitle = "Get the latest updates on new products and exclusive offers",
  backgroundColor = "#f0fdf4", // green-50
  className = "",
  source = "homepage"
}) => {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [whatsapp, setWhatsapp] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      setStatus('error');
      setMessage('Please enter your email address');
      return;
    }

    setIsSubmitting(true);
    setStatus('idle');

    try {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          name: name || undefined,
          whatsapp: whatsapp || undefined,
          source,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setStatus('success');
        setMessage(data.message || 'Successfully subscribed to newsletter!');
        setEmail('');
        setName('');
      } else {
        setStatus('error');
        setMessage(data.error || 'Failed to subscribe. Please try again.');
      }
    } catch (error) {
      setStatus('error');
      setMessage('Network error. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div 
      className={`rounded-2xl p-6 lg:p-8 ${className}`}
      style={{ backgroundColor }}
    >
      <div className="max-w-md mx-auto text-center">
        {/* Icon */}
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Mail className="w-8 h-8 text-green-600" />
        </div>

        {/* Title and Subtitle */}
        <h3 className="text-xl lg:text-2xl font-bold text-gray-900 mb-2">
          {title}
        </h3>
        <p className="text-gray-600 mb-6 text-sm lg:text-base">
          {subtitle}
        </p>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-3">
          {/* Compact Input Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <input
              type="text"
              placeholder="Your name (optional)"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white text-gray-900 placeholder-gray-500 text-sm"
            />
            <input
              type="tel"
              placeholder="WhatsApp (optional)"
              value={whatsapp}
              onChange={(e) => setWhatsapp(e.target.value)}
              className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white text-gray-900 placeholder-gray-500 text-sm"
            />
          </div>

          {/* Email Input */}
          <div className="flex gap-2">
            <input
              type="email"
              placeholder="Enter your email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="flex-1 px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white text-gray-900 placeholder-gray-500 text-sm"
            />
            <button
              type="submit"
              disabled={isSubmitting || status === 'success'}
              className={`px-6 py-2.5 rounded-lg font-semibold text-white transition-all duration-200 text-sm whitespace-nowrap ${
                status === 'success'
                  ? 'bg-green-600 cursor-default'
                  : isSubmitting
                  ? 'bg-green-400 cursor-not-allowed'
                  : 'bg-green-600 hover:bg-green-700 active:bg-green-800'
              }`}
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-1"></div>
                  Subscribing...
                </div>
              ) : status === 'success' ? (
                <div className="flex items-center">
                  <Check className="w-4 h-4 mr-1" />
                  Subscribed!
                </div>
              ) : (
                <div className="flex items-center">
                  <Sparkles className="w-4 h-4 mr-1" />
                  Subscribe
                </div>
              )}
            </button>
          </div>
        </form>

        {/* Status Message */}
        {message && (
          <div className={`mt-4 p-3 rounded-lg flex items-center justify-center text-sm ${
            status === 'success'
              ? 'bg-green-100 text-green-800 border border-green-200'
              : 'bg-red-100 text-red-800 border border-red-200'
          }`}>
            {status === 'success' ? (
              <Check className="w-4 h-4 mr-2 flex-shrink-0" />
            ) : (
              <AlertCircle className="w-4 h-4 mr-2 flex-shrink-0" />
            )}
            {message}
          </div>
        )}

        {/* Privacy Note */}
        <p className="text-xs text-gray-500 mt-4">
          We respect your privacy. Unsubscribe at any time.
        </p>
      </div>
    </div>
  );
};

export default NewsletterSignup;
