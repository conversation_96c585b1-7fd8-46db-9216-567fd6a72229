"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/notifications/route";
exports.ids = ["app/api/notifications/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnotifications%2Froute&page=%2Fapi%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnotifications%2Froute&page=%2Fapi%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_project_app_api_notifications_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/notifications/route.ts */ \"(rsc)/./app/api/notifications/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/notifications/route\",\n        pathname: \"/api/notifications\",\n        filename: \"route\",\n        bundlePath: \"app/api/notifications/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\api\\\\notifications\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_project_app_api_notifications_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/notifications/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnotifications%2Froute&page=%2Fapi%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/notifications/route.ts":
/*!****************************************!*\
  !*** ./app/api/notifications/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/auth */ \"(rsc)/./app/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/db */ \"(rsc)/./app/lib/db.ts\");\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/logger */ \"(rsc)/./app/lib/logger.ts\");\n\n\n\n\n\n/**\n * GET /api/notifications\n * Get notifications for the current user\n */ async function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const unreadOnly = searchParams.get(\"unreadOnly\") === \"true\";\n        const type = searchParams.get(\"type\");\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {\n            userId: session.user.id,\n            OR: [\n                {\n                    expiresAt: null\n                },\n                {\n                    expiresAt: {\n                        gt: new Date()\n                    }\n                }\n            ]\n        };\n        if (unreadOnly) {\n            where.isRead = false;\n        }\n        if (type) {\n            where.type = type;\n        }\n        const [notifications, totalCount, unreadCount] = await Promise.all([\n            _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.notification.findMany({\n                where,\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                skip,\n                take: limit,\n                select: {\n                    id: true,\n                    title: true,\n                    message: true,\n                    type: true,\n                    priority: true,\n                    isRead: true,\n                    data: true,\n                    createdAt: true\n                }\n            }),\n            _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.notification.count({\n                where\n            }),\n            _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.notification.count({\n                where: {\n                    userId: session.user.id,\n                    isRead: false,\n                    OR: [\n                        {\n                            expiresAt: null\n                        },\n                        {\n                            expiresAt: {\n                                gt: new Date()\n                            }\n                        }\n                    ]\n                }\n            })\n        ]);\n        const totalPages = Math.ceil(totalCount / limit);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                notifications,\n                pagination: {\n                    page,\n                    limit,\n                    totalCount,\n                    totalPages,\n                    hasNext: page < totalPages,\n                    hasPrev: page > 1\n                },\n                unreadCount\n            }\n        });\n    } catch (error) {\n        _lib_logger__WEBPACK_IMPORTED_MODULE_4__.logger.error(\"Failed to fetch user notifications\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/notifications/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/auth.ts":
/*!*************************!*\
  !*** ./app/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db */ \"(rsc)/./app/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst authOptions = {\n    // Remove PrismaAdapter when using JWT strategy\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const user = await _db__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user || !user.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2__.compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\",\n        maxAge: 7 * 24 * 60 * 60,\n        updateAge: 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 7 * 24 * 60 * 60\n    },\n    // Enable CSRF protection\n    cookies: {\n        sessionToken: {\n            name:  false ? 0 : `next-auth.session-token`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: \"development\" === \"production\"\n            }\n        },\n        callbackUrl: {\n            name:  false ? 0 : `next-auth.callback-url`,\n            options: {\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: \"development\" === \"production\"\n            }\n        },\n        csrfToken: {\n            name:  false ? 0 : `next-auth.csrf-token`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: \"development\" === \"production\"\n            }\n        }\n    },\n    callbacks: {\n        async jwt ({ token, user, account, profile }) {\n            // Handle initial sign in\n            if (account && user) {\n                // For OAuth providers, create/update user in database\n                if (account.provider === \"google\") {\n                    try {\n                        const email = user.email || profile?.email;\n                        if (!email) {\n                            throw new Error(\"No email found for Google account\");\n                        }\n                        // Check if user exists\n                        let dbUser = await _db__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n                            where: {\n                                email\n                            },\n                            select: {\n                                id: true,\n                                role: true,\n                                name: true,\n                                email: true\n                            }\n                        });\n                        // Create user if doesn't exist\n                        if (!dbUser) {\n                            dbUser = await _db__WEBPACK_IMPORTED_MODULE_3__.prisma.user.create({\n                                data: {\n                                    email,\n                                    name: user.name || profile?.name || email.split(\"@\")[0],\n                                    // Don't save Google profile picture\n                                    role: _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.CUSTOMER,\n                                    emailVerified: new Date()\n                                },\n                                select: {\n                                    id: true,\n                                    role: true,\n                                    name: true,\n                                    email: true\n                                }\n                            });\n                        }\n                        // Set token properties\n                        token.sub = dbUser.id;\n                        token.role = dbUser.role;\n                        token.email = dbUser.email;\n                        token.name = dbUser.name;\n                    } catch (error) {\n                        console.error(\"Error handling Google sign in:\", error);\n                        throw error; // This will prevent sign in\n                    }\n                } else if (user) {\n                    // For credentials provider\n                    token.sub = user.id;\n                    token.role = user.role;\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            // Populate session with user data from token\n            if (token && session.user) {\n                session.user = {\n                    ...session.user,\n                    id: token.sub,\n                    role: token.role,\n                    email: token.email,\n                    name: token.name\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Allows relative callback URLs\n            if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\n            else if (new URL(url).origin === baseUrl) return url;\n            return baseUrl;\n        }\n    },\n    events: {\n        async signIn ({ user, account, profile, isNewUser }) {\n            // Log sign in events in development only\n            if (true) {\n                console.log(\"Sign in event:\", {\n                    userId: user.id,\n                    email: user.email,\n                    provider: account?.provider,\n                    isNewUser\n                });\n            }\n        }\n    },\n    pages: {\n        signIn: \"/login\",\n        signOut: \"/\",\n        error: \"/login\"\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    // Only enable debug in development when explicitly set\n    debug:  true && process.env.NEXTAUTH_DEBUG === \"true\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"error\"\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQ1hGLGdCQUFnQkUsTUFBTSxJQUN0QixJQUFJSCx3REFBWUEsQ0FBQztJQUNmSSxLQUFLO1FBQUM7S0FBUTtBQUNoQixHQUFFO0FBRUosSUFBSUMsSUFBeUIsRUFBY0osZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL2FwcC9saWIvZGIudHM/NThmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz9cbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ2Vycm9yJ10sIC8vIE9ubHkgbG9nIGVycm9ycywgbm8gcXVlcmllcyBvciB3YXJuaW5nc1xuICB9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/logger.ts":
/*!***************************!*\
  !*** ./app/lib/logger.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogLevel: () => (/* binding */ LogLevel),\n/* harmony export */   createRequestLogger: () => (/* binding */ createRequestLogger),\n/* harmony export */   devLog: () => (/* binding */ devLog),\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\nvar LogLevel;\n(function(LogLevel) {\n    LogLevel[LogLevel[\"ERROR\"] = 0] = \"ERROR\";\n    LogLevel[LogLevel[\"WARN\"] = 1] = \"WARN\";\n    LogLevel[LogLevel[\"INFO\"] = 2] = \"INFO\";\n    LogLevel[LogLevel[\"DEBUG\"] = 3] = \"DEBUG\";\n})(LogLevel || (LogLevel = {}));\nclass Logger {\n    constructor(){\n        this.isDevelopment = \"development\" === \"development\";\n        // Reduce verbosity in development - only show warnings and errors\n        this.logLevel = this.isDevelopment ? 1 : 2;\n    }\n    shouldLog(level) {\n        return level <= this.logLevel;\n    }\n    formatMessage(entry) {\n        const { timestamp, level, message, context, error, userId, requestId } = entry;\n        const levelName = LogLevel[level];\n        let formatted = `[${timestamp}] ${levelName}: ${message}`;\n        if (userId) {\n            formatted += ` | User: ${userId}`;\n        }\n        if (requestId) {\n            formatted += ` | Request: ${requestId}`;\n        }\n        if (context && Object.keys(context).length > 0) {\n            formatted += ` | Context: ${JSON.stringify(context)}`;\n        }\n        if (error) {\n            formatted += ` | Error: ${error.message}`;\n            if (this.isDevelopment && error.stack) {\n                formatted += `\\nStack: ${error.stack}`;\n            }\n        }\n        return formatted;\n    }\n    log(level, message, context, error) {\n        if (!this.shouldLog(level)) return;\n        const entry = {\n            timestamp: new Date().toISOString(),\n            level,\n            message,\n            context,\n            error\n        };\n        const formatted = this.formatMessage(entry);\n        // In development, use console methods for better formatting\n        if (this.isDevelopment) {\n            switch(level){\n                case 0:\n                    console.error(formatted);\n                    break;\n                case 1:\n                    console.warn(formatted);\n                    break;\n                case 2:\n                    console.info(formatted);\n                    break;\n                case 3:\n                    console.debug(formatted);\n                    break;\n            }\n        } else {\n            // In production, use structured logging (JSON format)\n            console.log(JSON.stringify(entry));\n        }\n    }\n    error(message, error, context) {\n        this.log(0, message, context, error);\n    }\n    warn(message, context) {\n        this.log(1, message, context);\n    }\n    info(message, context) {\n        this.log(2, message, context);\n    }\n    debug(message, context) {\n        this.log(3, message, context);\n    }\n    // API-specific logging methods\n    apiRequest(method, path, userId, context) {\n        this.info(`API ${method} ${path}`, {\n            ...context,\n            userId,\n            type: \"api_request\"\n        });\n    }\n    apiResponse(method, path, statusCode, duration, context) {\n        this.info(`API ${method} ${path} - ${statusCode}`, {\n            ...context,\n            statusCode,\n            duration,\n            type: \"api_response\"\n        });\n    }\n    apiError(method, path, error, userId, context) {\n        this.error(`API ${method} ${path} failed`, error, {\n            ...context,\n            userId,\n            type: \"api_error\"\n        });\n    }\n    // Authentication logging\n    authSuccess(userId, method, context) {\n        this.info(`Authentication successful`, {\n            ...context,\n            userId,\n            method,\n            type: \"auth_success\"\n        });\n    }\n    authFailure(email, method, reason, context) {\n        this.warn(`Authentication failed`, {\n            ...context,\n            email,\n            method,\n            reason,\n            type: \"auth_failure\"\n        });\n    }\n    // Database logging\n    dbQuery(operation, table, duration, context) {\n        this.debug(`DB ${operation} on ${table}`, {\n            ...context,\n            operation,\n            table,\n            duration,\n            type: \"db_query\"\n        });\n    }\n    dbError(operation, table, error, context) {\n        this.error(`DB ${operation} on ${table} failed`, error, {\n            ...context,\n            operation,\n            table,\n            type: \"db_error\"\n        });\n    }\n    // Security logging\n    securityEvent(event, severity, context) {\n        const level = severity === \"high\" ? 0 : severity === \"medium\" ? 1 : 2;\n        this.log(level, `Security event: ${event}`, {\n            ...context,\n            severity,\n            type: \"security_event\"\n        });\n    }\n    // Rate limiting logging\n    rateLimitHit(identifier, limit, window, context) {\n        this.warn(`Rate limit exceeded`, {\n            ...context,\n            identifier,\n            limit,\n            window,\n            type: \"rate_limit\"\n        });\n    }\n    // Email logging\n    emailSent(to, subject, template, context) {\n        this.info(`Email sent`, {\n            ...context,\n            to,\n            subject,\n            template,\n            type: \"email_sent\"\n        });\n    }\n    emailError(to, subject, error, context) {\n        this.error(`Email failed to send`, error, {\n            ...context,\n            to,\n            subject,\n            type: \"email_error\"\n        });\n    }\n    // Performance logging\n    performance(operation, duration, context) {\n        const level = duration > 5000 ? 1 : 3;\n        this.log(level, `Performance: ${operation} took ${duration}ms`, {\n            ...context,\n            operation,\n            duration,\n            type: \"performance\"\n        });\n    }\n}\n// Create singleton instance\nconst logger = new Logger();\n// Request logging middleware helper\nfunction createRequestLogger(req) {\n    const requestId = crypto.randomUUID();\n    const startTime = Date.now();\n    return {\n        requestId,\n        log: (message, context)=>{\n            logger.info(message, {\n                ...context,\n                requestId\n            });\n        },\n        error: (message, error, context)=>{\n            logger.error(message, error, {\n                ...context,\n                requestId\n            });\n        },\n        end: (statusCode)=>{\n            const duration = Date.now() - startTime;\n            logger.apiResponse(req.method || \"UNKNOWN\", new URL(req.url || \"\").pathname, statusCode, duration, {\n                requestId\n            });\n        }\n    };\n}\n// Development-only logging helpers\nconst devLog = {\n    info: (message, data)=>{\n        if (true) {\n            console.log(`🔍 ${message}`, data ? JSON.stringify(data, null, 2) : \"\");\n        }\n    },\n    error: (message, error)=>{\n        if (true) {\n            console.error(`❌ ${message}`, error);\n        }\n    },\n    warn: (message, data)=>{\n        if (true) {\n            console.warn(`⚠️ ${message}`, data ? JSON.stringify(data, null, 2) : \"\");\n        }\n    },\n    success: (message, data)=>{\n        if (true) {\n            console.log(`✅ ${message}`, data ? JSON.stringify(data, null, 2) : \"\");\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/logger.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnotifications%2Froute&page=%2Fapi%2Fnotifications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();