'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

interface FlashSaleSettings {
  showFlashSale: boolean;
  flashSaleEndDate: string | null;
  flashSalePercentage: number | null;
  flashSaleTitle: string | null;
  flashSaleSubtitle: string | null;
  flashSaleBackgroundColor: string | null;
}

interface FlashSaleContextType {
  flashSaleSettings: FlashSaleSettings | null;
  loading: boolean;
  refreshSettings: () => Promise<void>;
}

const FlashSaleContext = createContext<FlashSaleContextType | undefined>(undefined);

export function FlashSaleProvider({ children }: { children: React.ReactNode }) {
  const [flashSaleSettings, setFlashSaleSettings] = useState<FlashSaleSettings | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchFlashSaleSettings = async () => {
    try {
      const response = await fetch('/api/homepage-settings');
      const data = await response.json();
      
      if (data.success && data.data.settings) {
        const settings = data.data.settings;
        setFlashSaleSettings({
          showFlashSale: settings.showFlashSale,
          flashSaleEndDate: settings.flashSaleEndDate,
          flashSalePercentage: settings.flashSalePercentage,
          flashSaleTitle: settings.flashSaleTitle,
          flashSaleSubtitle: settings.flashSaleSubtitle,
          flashSaleBackgroundColor: settings.flashSaleBackgroundColor,
        });
      }
    } catch (error) {
      console.error('Error fetching flash sale settings:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFlashSaleSettings();
  }, []);

  const refreshSettings = async () => {
    setLoading(true);
    await fetchFlashSaleSettings();
  };

  return (
    <FlashSaleContext.Provider value={{ flashSaleSettings, loading, refreshSettings }}>
      {children}
    </FlashSaleContext.Provider>
  );
}

export function useFlashSale() {
  const context = useContext(FlashSaleContext);
  if (context === undefined) {
    throw new Error('useFlashSale must be used within a FlashSaleProvider');
  }
  return context;
}