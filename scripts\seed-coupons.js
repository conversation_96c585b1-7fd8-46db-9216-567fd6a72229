const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const sampleCoupons = [
  {
    code: 'WELCOME10',
    name: 'Welcome Discount',
    description: 'Get 10% off on your first order',
    type: 'FIRST_TIME_CUSTOMER',
    discountType: 'PERCENTAGE',
    discountValue: 10,
    minimumAmount: 500,
    maximumDiscount: 200,
    usageLimit: 1000,
    userUsageLimit: 1,
    isActive: true,
    isStackable: false,
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    applicableProducts: [],
    applicableCategories: [],
    excludedProducts: [],
    excludedCategories: [],
    customerSegments: ['new']
  },
  {
    code: 'SAVE20',
    name: 'Store Wide 20% Off',
    description: 'Get 20% off on all products',
    type: 'STORE_WIDE',
    discountType: 'PERCENTAGE',
    discountValue: 20,
    minimumAmount: 1000,
    maximumDiscount: 500,
    usageLimit: 500,
    userUsageLimit: 2,
    isActive: true,
    isStackable: false,
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    applicableProducts: [],
    applicableCategories: [],
    excludedProducts: [],
    excludedCategories: [],
    customerSegments: []
  },
  {
    code: 'FLAT100',
    name: 'Flat ₹100 Off',
    description: 'Get flat ₹100 off on orders above ₹800',
    type: 'MINIMUM_PURCHASE',
    discountType: 'FIXED_AMOUNT',
    discountValue: 100,
    minimumAmount: 800,
    maximumDiscount: null,
    usageLimit: 200,
    userUsageLimit: 3,
    isActive: true,
    isStackable: true,
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
    applicableProducts: [],
    applicableCategories: [],
    excludedProducts: [],
    excludedCategories: [],
    customerSegments: []
  },
  {
    code: 'SKINCARE15',
    name: 'Skincare Special',
    description: '15% off on all skincare products',
    type: 'CATEGORY_SPECIFIC',
    discountType: 'PERCENTAGE',
    discountValue: 15,
    minimumAmount: 300,
    maximumDiscount: 300,
    usageLimit: 100,
    userUsageLimit: 1,
    isActive: true,
    isStackable: true,
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000), // 21 days from now
    applicableProducts: [],
    applicableCategories: [], // Will be populated with actual category IDs
    excludedProducts: [],
    excludedCategories: [],
    customerSegments: []
  },
  {
    code: 'FREESHIP',
    name: 'Free Shipping',
    description: 'Free shipping on all orders',
    type: 'STORE_WIDE',
    discountType: 'FREE_SHIPPING',
    discountValue: 0,
    minimumAmount: 299,
    maximumDiscount: null,
    usageLimit: null,
    userUsageLimit: null,
    isActive: true,
    isStackable: true,
    validFrom: new Date(),
    validUntil: null, // No expiry
    applicableProducts: [],
    applicableCategories: [],
    excludedProducts: [],
    excludedCategories: [],
    customerSegments: []
  },
  {
    code: 'LOYALTY25',
    name: 'Loyalty Reward',
    description: '25% off for loyal customers',
    type: 'LOYALTY_REWARD',
    discountType: 'PERCENTAGE',
    discountValue: 25,
    minimumAmount: 1500,
    maximumDiscount: 750,
    usageLimit: 50,
    userUsageLimit: 1,
    isActive: true,
    isStackable: false,
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now (expires soon)
    applicableProducts: [],
    applicableCategories: [],
    excludedProducts: [],
    excludedCategories: [],
    customerSegments: ['loyal', 'premium']
  },
  {
    code: 'SUMMER50',
    name: 'Summer Sale',
    description: 'Flat ₹50 off on summer collection',
    type: 'SEASONAL',
    discountType: 'FIXED_AMOUNT',
    discountValue: 50,
    minimumAmount: 400,
    maximumDiscount: null,
    usageLimit: 300,
    userUsageLimit: 2,
    isActive: true,
    isStackable: true,
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
    applicableProducts: [],
    applicableCategories: [],
    excludedProducts: [],
    excludedCategories: [],
    customerSegments: []
  },
  {
    code: 'BUNDLE30',
    name: 'Bundle Deal',
    description: '30% off when you buy 3 or more items',
    type: 'BUNDLE_DEAL',
    discountType: 'PERCENTAGE',
    discountValue: 30,
    minimumAmount: 1200,
    maximumDiscount: 600,
    usageLimit: 150,
    userUsageLimit: 1,
    isActive: true,
    isStackable: false,
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // 10 days from now
    applicableProducts: [],
    applicableCategories: [],
    excludedProducts: [],
    excludedCategories: [],
    customerSegments: []
  }
];

async function seedCoupons() {
  try {
    console.log('🌱 Seeding coupons...');

    // Get some categories to assign to category-specific coupons
    const categories = await prisma.category.findMany({
      where: { isActive: true },
      take: 3
    });

    // Update category-specific coupon with actual category IDs
    if (categories.length > 0) {
      const skincareCategory = categories.find(cat => 
        cat.name.toLowerCase().includes('skin') || 
        cat.name.toLowerCase().includes('beauty') ||
        cat.name.toLowerCase().includes('care')
      );
      
      if (skincareCategory) {
        const couponIndex = sampleCoupons.findIndex(c => c.code === 'SKINCARE15');
        if (couponIndex !== -1) {
          sampleCoupons[couponIndex].applicableCategories = [skincareCategory.id];
        }
      }
    }

    // Create coupons
    for (const couponData of sampleCoupons) {
      try {
        const existingCoupon = await prisma.coupon.findUnique({
          where: { code: couponData.code }
        });

        if (!existingCoupon) {
          await prisma.coupon.create({
            data: couponData
          });
          console.log(`✅ Created coupon: ${couponData.code}`);
        } else {
          console.log(`⚠️  Coupon already exists: ${couponData.code}`);
        }
      } catch (error) {
        console.error(`❌ Error creating coupon ${couponData.code}:`, error.message);
      }
    }

    console.log('🎉 Coupon seeding completed!');
  } catch (error) {
    console.error('❌ Error seeding coupons:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding function
if (require.main === module) {
  seedCoupons();
}

module.exports = { seedCoupons };