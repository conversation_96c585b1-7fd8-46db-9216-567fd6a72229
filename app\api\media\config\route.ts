import { NextRequest, NextResponse } from 'next/server';
import { checkR2Config } from '../../../lib/r2';

export async function GET(request: NextRequest) {
  try {
    const config = checkR2Config();
    
    return NextResponse.json({
      success: true,
      config,
      message: 'R2 configuration check completed'
    });
  } catch (error) {
    console.error('Config check error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to check configuration' },
      { status: 500 }
    );
  }
}
