const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixVariationPricingMode() {
  try {
    console.log('Starting to fix variation pricing modes...');
    
    // Update all existing variations that have INCREMENT mode to REPLACE mode
    const result = await prisma.productVariant.updateMany({
      where: {
        pricingMode: 'INCREMENT'
      },
      data: {
        pricingMode: 'REPLACE'
      }
    });
    
    console.log(`Updated ${result.count} variations from INCREMENT to REPLACE mode`);
    
    // Also update any variations that might have null/undefined pricingMode
    const nullResult = await prisma.productVariant.updateMany({
      where: {
        pricingMode: null
      },
      data: {
        pricingMode: 'REPLACE'
      }
    });
    
    console.log(`Updated ${nullResult.count} variations with null pricingMode to REPLACE mode`);
    
    console.log('Variation pricing mode fix completed successfully!');
  } catch (error) {
    console.error('Error fixing variation pricing modes:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixVariationPricingMode();