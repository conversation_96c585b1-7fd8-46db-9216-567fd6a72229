const fs = require('fs');
const path = require('path');

// Transform the product structure to match frontend expectations
function transformProductStructure() {
  try {
    console.log('🔄 Transforming product structure...\n');

    // Read the current products file
    const productsPath = path.join(process.cwd(), 'csvjson-updated.json');
    if (!fs.existsSync(productsPath)) {
      console.error('❌ Products file not found:', productsPath);
      process.exit(1);
    }

    const fileData = JSON.parse(fs.readFileSync(productsPath, 'utf8'));
    const productsData = fileData.products || fileData;

    if (!Array.isArray(productsData)) {
      console.error('❌ Invalid products file format. Expected an array.');
      process.exit(1);
    }

    // Transform products to match frontend structure
    const transformedProducts = productsData.map(product => {
      // Create category objects from categoryNames
      const categories = (product.categoryNames || []).map((categoryName, index) => ({
        id: `${categoryName.toLowerCase().replace(/\s+/g, '-')}-${index}`,
        name: categoryName,
        slug: categoryName.toLowerCase().replace(/\s+/g, '-')
      }));

      // Set primary category as the first one
      const primaryCategory = categories[0] || {
        id: 'uncategorized',
        name: 'Uncategorized',
        slug: 'uncategorized'
      };

      return {
        ...product,
        category: primaryCategory,
        categories: categories,
        // Ensure shortDescription is present
        shortDescription: product.shortDescription || '',
        // Add default values for missing fields
        rating: product.rating || 4.5,
        reviews: product.reviews || 0,
        ingredients: product.ingredients || [],
        benefits: product.benefits || [],
        featured: product.isFeatured || false,
        // Handle images
        images: product.images || (product.image ? [{
          id: '1',
          url: product.image,
          alt: product.name,
          position: 1
        }] : [])
      };
    });

    // Write transformed products
    const outputPath = path.join(process.cwd(), 'csvjson-transformed.json');
    fs.writeFileSync(outputPath, JSON.stringify({ products: transformedProducts }, null, 2), 'utf8');

    console.log('✅ Product structure transformation complete!');
    console.log(`📁 Transformed file saved: ${outputPath}`);
    console.log(`📊 Total products transformed: ${transformedProducts.length}`);

    // Show sample of transformed data
    console.log('\n📋 Sample transformed product:');
    console.log(JSON.stringify(transformedProducts[0], null, 2));

  } catch (error) {
    console.error('❌ Error transforming product structure:', error);
    process.exit(1);
  }
}

// Run the transformation
transformProductStructure();