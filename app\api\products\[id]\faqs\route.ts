import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/db';

// GET /api/products/[id]/faqs - Get all FAQs for a product
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const faqs = await prisma.productFAQ.findMany({
      where: {
        productId: params.id,
        isActive: true,
      },
      orderBy: {
        position: 'asc',
      },
    });

    return NextResponse.json({
      success: true,
      data: faqs,
    });
  } catch (error) {
    console.error('Error fetching FAQs:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch FAQs' },
      { status: 500 }
    );
  }
}

// POST /api/products/[id]/faqs - Create a new FAQ for a product
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { question, answer, position } = body;

    if (!question || !answer) {
      return NextResponse.json(
        { success: false, error: 'Question and answer are required' },
        { status: 400 }
      );
    }

    // Get the next position if not provided
    let faqPosition = position;
    if (faqPosition === undefined) {
      const lastFaq = await prisma.productFAQ.findFirst({
        where: { productId: params.id },
        orderBy: { position: 'desc' },
      });
      faqPosition = lastFaq ? lastFaq.position + 1 : 0;
    }

    const faq = await prisma.productFAQ.create({
      data: {
        question,
        answer,
        position: faqPosition,
        productId: params.id,
      },
    });

    return NextResponse.json({
      success: true,
      data: faq,
      message: 'FAQ created successfully',
    });
  } catch (error) {
    console.error('Error creating FAQ:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create FAQ' },
      { status: 500 }
    );
  }
}
