'use client';

import React, { useState, useEffect } from 'react';
import { formatPrice } from '../lib/currency';

interface ProductVariation {
  id: string;
  name: string;
  value: string;
  price?: number;
  pricingMode?: 'REPLACE' | 'INCREMENT' | 'FIXED';
}

interface ProductVariationSelectorProps {
  productId: string;
  basePrice: number;
  onVariationChange?: (variation: ProductVariation | null, totalPrice: number) => void;
}

const ProductVariationSelector: React.FC<ProductVariationSelectorProps> = ({
  productId,
  basePrice,
  onVariationChange,
}) => {
  const [variations, setVariations] = useState<ProductVariation[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedVariations, setSelectedVariations] = useState<Record<string, ProductVariation>>({});

  useEffect(() => {
    fetchVariations();
  }, [productId]);

  useEffect(() => {
    // Auto-select the last (highest) variation when variations load
    if (variations.length > 0 && Object.keys(selectedVariations).length === 0) {
      const grouped = variations.reduce((acc, variation) => {
        if (!acc[variation.name]) {
          acc[variation.name] = [];
        }
        acc[variation.name].push(variation);
        return acc;
      }, {} as Record<string, ProductVariation[]>);

      const newSelectedVariations: Record<string, ProductVariation> = {};
      
      Object.entries(grouped).forEach(([variationName, options]) => {
        // Select the last option (typically the highest/largest)
        newSelectedVariations[variationName] = options[options.length - 1];
      });

      setSelectedVariations(newSelectedVariations);
      
      // Calculate and trigger price update
      const selectedValues = Object.values(newSelectedVariations);
      const finalPrice = calculateFinalPrice(selectedValues);

      const selectedVariation = Object.keys(newSelectedVariations).length === 1
        ? Object.values(newSelectedVariations)[0]
        : null;

      if (onVariationChange) {
        onVariationChange(selectedVariation, finalPrice);
      }
    }
  }, [variations]); // Remove dependencies to prevent re-running

  const fetchVariations = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/products/${productId}/variations`);
      const result = await response.json();
      
      if (result.success) {
        setVariations(result.data);
      }
    } catch (error) {
      console.error('Error fetching variations:', error);
    } finally {
      setLoading(false);
    }
  };

  // Group variations by name
  const groupedVariations = variations.reduce((acc, variation) => {
    if (!acc[variation.name]) {
      acc[variation.name] = [];
    }
    acc[variation.name].push(variation);
    return acc;
  }, {} as Record<string, ProductVariation[]>);

  const calculateFinalPrice = (selectedValues: ProductVariation[]): number => {
    if (selectedValues.length === 0) {
      return 0; // Return 0 if no variation selected
    }

    // For single variation products, use the selected variation's price
    if (selectedValues.length === 1) {
      const variation = selectedValues[0];
      if (variation.price !== undefined && variation.price !== null && variation.price > 0) {
        // ALWAYS use REPLACE mode - ignore pricingMode from database
        // This ensures variant price replaces base price, not adds to it
        return variation.price;
      }
    }
    
    // For multiple variations, find the one with highest price
    const validVariations = selectedValues.filter(v =>
      v.price !== undefined && v.price !== null && v.price > 0
    );
    
    if (validVariations.length > 0) {
      // Use the highest priced variation
      const highestPricedVariation = validVariations.reduce((max, current) =>
        (current.price ?? 0) > (max.price ?? 0) ? current : max
      );
      
      // ALWAYS use REPLACE mode - ignore pricingMode from database
      return highestPricedVariation.price ?? 0;
    }

    return 0; // Return 0 if no valid price found
  };

  const handleVariationSelect = (variationName: string, variation: ProductVariation) => {
    const newSelectedVariations = {
      ...selectedVariations,
      [variationName]: variation,
    };
    setSelectedVariations(newSelectedVariations);

    const selectedValues = Object.values(newSelectedVariations);
    const finalPrice = calculateFinalPrice(selectedValues);

    // Get the currently selected variation (for single variation products)
    const selectedVariation = Object.keys(newSelectedVariations).length === 1
      ? Object.values(newSelectedVariations)[0]
      : null;

    if (onVariationChange) {
      onVariationChange(selectedVariation, finalPrice);
    }
  };

  const isVariationAvailable = (variation: ProductVariation) => {
    return true; // All variations are available since we removed quantity tracking
  };

  const getSelectedVariationInfo = () => {
    const selectedValues = Object.values(selectedVariations);
    if (selectedValues.length === 0) return null;

    const finalPrice = calculateFinalPrice(selectedValues);

    return {
      totalPrice: finalPrice,
      selectedValues,
    };
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
          <div className="flex space-x-2">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-10 w-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (variations.length === 0) {
    return null; // No variations available
  }

  const selectedInfo = getSelectedVariationInfo();

  return (
    <div className="space-y-6">
      {Object.entries(groupedVariations).map(([variationName, variationOptions]) => (
        <div key={variationName} className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900 capitalize">
              {variationName}
            </h4>
            {selectedVariations[variationName] && (
              <span className="text-sm text-gray-600">
                Selected: {selectedVariations[variationName].value}
              </span>
            )}
          </div>
          
          <div className="flex flex-wrap gap-2">
            {variationOptions.map((variation) => {
              const isSelected = selectedVariations[variationName]?.id === variation.id;
              const isAvailable = isVariationAvailable(variation);
              
              return (
                <button
                  key={variation.id}
                  onClick={() => isAvailable && handleVariationSelect(variationName, variation)}
                  disabled={!isAvailable}
                  className={`
                    px-4 py-2 border rounded-lg text-sm font-medium transition-colors
                    ${isSelected
                      ? 'border-green-500 bg-green-50 text-green-700'
                      : isAvailable
                      ? 'border-gray-300 bg-white text-gray-700 hover:border-gray-400'
                      : 'border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed'
                    }
                  `}
                >
                  <span>{variation.value}</span>
                </button>
              );
            })}
          </div>
        </div>
      ))}

      {/* Removed redundant price display - price is shown in ProductDetail component */}

      {/* Variation Selection Requirement */}
      {Object.keys(groupedVariations).length > 0 && Object.keys(selectedVariations).length < Object.keys(groupedVariations).length && (
        <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            Please select all required options before adding to cart.
          </p>
        </div>
      )}
    </div>
  );
};

export default ProductVariationSelector;
