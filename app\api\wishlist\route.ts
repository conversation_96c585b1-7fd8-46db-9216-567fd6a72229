import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../lib/auth';
import { prisma } from '../../lib/db';
import { wishlistNotifications } from '../../lib/notification-helpers';

// GET /api/wishlist - Get user's wishlist items
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const wishlistItems = await prisma.wishlistItem.findMany({
      where: {
        userId: session.user.id,
      },
      include: {
        product: {
          include: {
            images: {
              orderBy: { id: 'asc' },
              take: 1,
            },
            productCategories: {
              include: {
                category: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Transform the data to match the expected format
    const transformedItems = wishlistItems.map((item: any) => ({
      id: item.product.id,
      name: item.product.name,
      slug: item.product.slug,
      price: item.product.price,
      shortDescription: item.product.shortDescription,
      image: item.product.images[0]?.url || '/placeholder-product.jpg',
      rating: 4.5, // Default rating - you might want to calculate this from reviews
      reviews: 0, // Default reviews count - you might want to calculate this
      wishlistItemId: item.id,
    }));

    return NextResponse.json({ items: transformedItems });
  } catch (error) {
    console.error('Error fetching wishlist:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/wishlist - Add item to wishlist
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { productId } = await request.json();

    if (!productId) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
    });

    if (!product) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 });
    }

    // Check if item is already in wishlist
    const existingItem = await prisma.wishlistItem.findUnique({
      where: {
        userId_productId: {
          userId: session.user.id,
          productId: productId,
        },
      },
    });

    if (existingItem) {
      return NextResponse.json({ error: 'Item already in wishlist' }, { status: 400 });
    }

    // Add to wishlist
    const wishlistItem = await prisma.wishlistItem.create({
      data: {
        userId: session.user.id,
        productId: productId,
      },
    });

    // Send wishlist added notification
    try {
      await wishlistNotifications.itemAdded(session.user.id, {
        productId: product.id,
        productName: product.name,
        price: product.price || undefined,
        currency: 'INR',
      });
    } catch (notificationError) {
      console.error('Failed to send wishlist added notification:', notificationError);
      // Don't fail the wishlist addition if notification fails
    }

    return NextResponse.json({ success: true, item: wishlistItem });
  } catch (error) {
    console.error('Error adding to wishlist:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE /api/wishlist - Remove item from wishlist
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('productId');

    if (!productId) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }

    // Get product details before removing
    const product = await prisma.product.findUnique({
      where: { id: productId },
    });

    // Remove from wishlist
    const deletedItem = await prisma.wishlistItem.deleteMany({
      where: {
        userId: session.user.id,
        productId: productId,
      },
    });

    if (deletedItem.count === 0) {
      return NextResponse.json({ error: 'Item not found in wishlist' }, { status: 404 });
    }

    // Send wishlist removed notification
    try {
      if (product) {
        await wishlistNotifications.itemRemoved(session.user.id, {
          productId: product.id,
          productName: product.name,
        });
      }
    } catch (notificationError) {
      console.error('Failed to send wishlist removed notification:', notificationError);
      // Don't fail the wishlist removal if notification fails
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error removing from wishlist:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}