"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk";
exports.ids = ["vendor-chunks/@aws-sdk"];
exports.modules = {

/***/ "(ssr)/./node_modules/@aws-sdk/core/dist-es/submodules/client/setFeature.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@aws-sdk/core/dist-es/submodules/client/setFeature.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setFeature: () => (/* binding */ setFeature)\n/* harmony export */ });\nfunction setFeature(context, feature, value) {\n    if (!context.__aws_sdk_context) {\n        context.__aws_sdk_context = {\n            features: {},\n        };\n    }\n    else if (!context.__aws_sdk_context.features) {\n        context.__aws_sdk_context.features = {};\n    }\n    context.__aws_sdk_context.features[feature] = value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY29yZS9kaXN0LWVzL3N1Ym1vZHVsZXMvY2xpZW50L3NldEZlYXR1cmUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2hlcmJhbGljaW91cy1uZXh0anMvLi9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY29yZS9kaXN0LWVzL3N1Ym1vZHVsZXMvY2xpZW50L3NldEZlYXR1cmUuanM/YTBkNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gc2V0RmVhdHVyZShjb250ZXh0LCBmZWF0dXJlLCB2YWx1ZSkge1xuICAgIGlmICghY29udGV4dC5fX2F3c19zZGtfY29udGV4dCkge1xuICAgICAgICBjb250ZXh0Ll9fYXdzX3Nka19jb250ZXh0ID0ge1xuICAgICAgICAgICAgZmVhdHVyZXM6IHt9LFxuICAgICAgICB9O1xuICAgIH1cbiAgICBlbHNlIGlmICghY29udGV4dC5fX2F3c19zZGtfY29udGV4dC5mZWF0dXJlcykge1xuICAgICAgICBjb250ZXh0Ll9fYXdzX3Nka19jb250ZXh0LmZlYXR1cmVzID0ge307XG4gICAgfVxuICAgIGNvbnRleHQuX19hd3Nfc2RrX2NvbnRleHQuZmVhdHVyZXNbZmVhdHVyZV0gPSB2YWx1ZTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/core/dist-es/submodules/client/setFeature.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/bucket-endpoint-middleware.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/bucket-endpoint-middleware.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bucketEndpointMiddleware: () => (/* binding */ bucketEndpointMiddleware),\n/* harmony export */   bucketEndpointMiddlewareOptions: () => (/* binding */ bucketEndpointMiddlewareOptions)\n/* harmony export */ });\nfunction bucketEndpointMiddleware(options) {\n    return (next, context) => async (args) => {\n        if (options.bucketEndpoint) {\n            const endpoint = context.endpointV2;\n            if (endpoint) {\n                const bucket = args.input.Bucket;\n                if (typeof bucket === \"string\") {\n                    try {\n                        const bucketEndpointUrl = new URL(bucket);\n                        context.endpointV2 = {\n                            ...endpoint,\n                            url: bucketEndpointUrl,\n                        };\n                    }\n                    catch (e) {\n                        const warning = `@aws-sdk/middleware-sdk-s3: bucketEndpoint=true was set but Bucket=${bucket} could not be parsed as URL.`;\n                        if (context.logger?.constructor?.name === \"NoOpLogger\") {\n                            console.warn(warning);\n                        }\n                        else {\n                            context.logger?.warn?.(warning);\n                        }\n                        throw e;\n                    }\n                }\n            }\n        }\n        return next(args);\n    };\n}\nconst bucketEndpointMiddlewareOptions = {\n    name: \"bucketEndpointMiddleware\",\n    override: true,\n    relation: \"after\",\n    toMiddleware: \"endpointV2Middleware\",\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/bucket-endpoint-middleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/check-content-length-header.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/check-content-length-header.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkContentLengthHeader: () => (/* binding */ checkContentLengthHeader),\n/* harmony export */   checkContentLengthHeaderMiddlewareOptions: () => (/* binding */ checkContentLengthHeaderMiddlewareOptions),\n/* harmony export */   getCheckContentLengthHeaderPlugin: () => (/* binding */ getCheckContentLengthHeaderPlugin)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(ssr)/./node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/smithy-client */ \"(ssr)/./node_modules/@smithy/smithy-client/dist-es/index.js\");\n\n\nconst CONTENT_LENGTH_HEADER = \"content-length\";\nconst DECODED_CONTENT_LENGTH_HEADER = \"x-amz-decoded-content-length\";\nfunction checkContentLengthHeader() {\n    return (next, context) => async (args) => {\n        const { request } = args;\n        if (_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest.isInstance(request)) {\n            if (!(CONTENT_LENGTH_HEADER in request.headers) && !(DECODED_CONTENT_LENGTH_HEADER in request.headers)) {\n                const message = `Are you using a Stream of unknown length as the Body of a PutObject request? Consider using Upload instead from @aws-sdk/lib-storage.`;\n                if (typeof context?.logger?.warn === \"function\" && !(context.logger instanceof _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.NoOpLogger)) {\n                    context.logger.warn(message);\n                }\n                else {\n                    console.warn(message);\n                }\n            }\n        }\n        return next({ ...args });\n    };\n}\nconst checkContentLengthHeaderMiddlewareOptions = {\n    step: \"finalizeRequest\",\n    tags: [\"CHECK_CONTENT_LENGTH_HEADER\"],\n    name: \"getCheckContentLengthHeaderPlugin\",\n    override: true,\n};\nconst getCheckContentLengthHeaderPlugin = (unused) => ({\n    applyToStack: (clientStack) => {\n        clientStack.add(checkContentLengthHeader(), checkContentLengthHeaderMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/check-content-length-header.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/index.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_OPTIONS: () => (/* reexport safe */ _s3_express_index__WEBPACK_IMPORTED_MODULE_4__.NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_OPTIONS),\n/* harmony export */   S3ExpressIdentityCache: () => (/* reexport safe */ _s3_express_index__WEBPACK_IMPORTED_MODULE_4__.S3ExpressIdentityCache),\n/* harmony export */   S3ExpressIdentityCacheEntry: () => (/* reexport safe */ _s3_express_index__WEBPACK_IMPORTED_MODULE_4__.S3ExpressIdentityCacheEntry),\n/* harmony export */   S3ExpressIdentityProviderImpl: () => (/* reexport safe */ _s3_express_index__WEBPACK_IMPORTED_MODULE_4__.S3ExpressIdentityProviderImpl),\n/* harmony export */   SignatureV4S3Express: () => (/* reexport safe */ _s3_express_index__WEBPACK_IMPORTED_MODULE_4__.SignatureV4S3Express),\n/* harmony export */   checkContentLengthHeader: () => (/* reexport safe */ _check_content_length_header__WEBPACK_IMPORTED_MODULE_0__.checkContentLengthHeader),\n/* harmony export */   checkContentLengthHeaderMiddlewareOptions: () => (/* reexport safe */ _check_content_length_header__WEBPACK_IMPORTED_MODULE_0__.checkContentLengthHeaderMiddlewareOptions),\n/* harmony export */   getCheckContentLengthHeaderPlugin: () => (/* reexport safe */ _check_content_length_header__WEBPACK_IMPORTED_MODULE_0__.getCheckContentLengthHeaderPlugin),\n/* harmony export */   getRegionRedirectMiddlewarePlugin: () => (/* reexport safe */ _region_redirect_middleware__WEBPACK_IMPORTED_MODULE_2__.getRegionRedirectMiddlewarePlugin),\n/* harmony export */   getS3ExpiresMiddlewarePlugin: () => (/* reexport safe */ _s3_expires_middleware__WEBPACK_IMPORTED_MODULE_3__.getS3ExpiresMiddlewarePlugin),\n/* harmony export */   getS3ExpressHttpSigningPlugin: () => (/* reexport safe */ _s3_express_index__WEBPACK_IMPORTED_MODULE_4__.getS3ExpressHttpSigningPlugin),\n/* harmony export */   getS3ExpressPlugin: () => (/* reexport safe */ _s3_express_index__WEBPACK_IMPORTED_MODULE_4__.getS3ExpressPlugin),\n/* harmony export */   getThrow200ExceptionsPlugin: () => (/* reexport safe */ _throw_200_exceptions__WEBPACK_IMPORTED_MODULE_6__.getThrow200ExceptionsPlugin),\n/* harmony export */   getValidateBucketNamePlugin: () => (/* reexport safe */ _validate_bucket_name__WEBPACK_IMPORTED_MODULE_7__.getValidateBucketNamePlugin),\n/* harmony export */   regionRedirectEndpointMiddleware: () => (/* reexport safe */ _region_redirect_endpoint_middleware__WEBPACK_IMPORTED_MODULE_1__.regionRedirectEndpointMiddleware),\n/* harmony export */   regionRedirectEndpointMiddlewareOptions: () => (/* reexport safe */ _region_redirect_endpoint_middleware__WEBPACK_IMPORTED_MODULE_1__.regionRedirectEndpointMiddlewareOptions),\n/* harmony export */   regionRedirectMiddleware: () => (/* reexport safe */ _region_redirect_middleware__WEBPACK_IMPORTED_MODULE_2__.regionRedirectMiddleware),\n/* harmony export */   regionRedirectMiddlewareOptions: () => (/* reexport safe */ _region_redirect_middleware__WEBPACK_IMPORTED_MODULE_2__.regionRedirectMiddlewareOptions),\n/* harmony export */   resolveS3Config: () => (/* reexport safe */ _s3Configuration__WEBPACK_IMPORTED_MODULE_5__.resolveS3Config),\n/* harmony export */   s3ExpiresMiddleware: () => (/* reexport safe */ _s3_expires_middleware__WEBPACK_IMPORTED_MODULE_3__.s3ExpiresMiddleware),\n/* harmony export */   s3ExpiresMiddlewareOptions: () => (/* reexport safe */ _s3_expires_middleware__WEBPACK_IMPORTED_MODULE_3__.s3ExpiresMiddlewareOptions),\n/* harmony export */   s3ExpressHttpSigningMiddleware: () => (/* reexport safe */ _s3_express_index__WEBPACK_IMPORTED_MODULE_4__.s3ExpressHttpSigningMiddleware),\n/* harmony export */   s3ExpressHttpSigningMiddlewareOptions: () => (/* reexport safe */ _s3_express_index__WEBPACK_IMPORTED_MODULE_4__.s3ExpressHttpSigningMiddlewareOptions),\n/* harmony export */   s3ExpressMiddleware: () => (/* reexport safe */ _s3_express_index__WEBPACK_IMPORTED_MODULE_4__.s3ExpressMiddleware),\n/* harmony export */   s3ExpressMiddlewareOptions: () => (/* reexport safe */ _s3_express_index__WEBPACK_IMPORTED_MODULE_4__.s3ExpressMiddlewareOptions),\n/* harmony export */   throw200ExceptionsMiddleware: () => (/* reexport safe */ _throw_200_exceptions__WEBPACK_IMPORTED_MODULE_6__.throw200ExceptionsMiddleware),\n/* harmony export */   throw200ExceptionsMiddlewareOptions: () => (/* reexport safe */ _throw_200_exceptions__WEBPACK_IMPORTED_MODULE_6__.throw200ExceptionsMiddlewareOptions),\n/* harmony export */   validateBucketNameMiddleware: () => (/* reexport safe */ _validate_bucket_name__WEBPACK_IMPORTED_MODULE_7__.validateBucketNameMiddleware),\n/* harmony export */   validateBucketNameMiddlewareOptions: () => (/* reexport safe */ _validate_bucket_name__WEBPACK_IMPORTED_MODULE_7__.validateBucketNameMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _check_content_length_header__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check-content-length-header */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/check-content-length-header.js\");\n/* harmony import */ var _region_redirect_endpoint_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./region-redirect-endpoint-middleware */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/region-redirect-endpoint-middleware.js\");\n/* harmony import */ var _region_redirect_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./region-redirect-middleware */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/region-redirect-middleware.js\");\n/* harmony import */ var _s3_expires_middleware__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./s3-expires-middleware */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-expires-middleware.js\");\n/* harmony import */ var _s3_express_index__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./s3-express/index */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/index.js\");\n/* harmony import */ var _s3Configuration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./s3Configuration */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3Configuration.js\");\n/* harmony import */ var _throw_200_exceptions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./throw-200-exceptions */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/throw-200-exceptions.js\");\n/* harmony import */ var _validate_bucket_name__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./validate-bucket-name */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/validate-bucket-name.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1zZGstczMvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQThDO0FBQ1E7QUFDVDtBQUNMO0FBQ0w7QUFDRDtBQUNLO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtc2RrLXMzL2Rpc3QtZXMvaW5kZXguanM/Y2MwYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9jaGVjay1jb250ZW50LWxlbmd0aC1oZWFkZXJcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3JlZ2lvbi1yZWRpcmVjdC1lbmRwb2ludC1taWRkbGV3YXJlXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9yZWdpb24tcmVkaXJlY3QtbWlkZGxld2FyZVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vczMtZXhwaXJlcy1taWRkbGV3YXJlXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9zMy1leHByZXNzL2luZGV4XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9zM0NvbmZpZ3VyYXRpb25cIjtcbmV4cG9ydCAqIGZyb20gXCIuL3Rocm93LTIwMC1leGNlcHRpb25zXCI7XG5leHBvcnQgKiBmcm9tIFwiLi92YWxpZGF0ZS1idWNrZXQtbmFtZVwiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/region-redirect-endpoint-middleware.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/region-redirect-endpoint-middleware.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   regionRedirectEndpointMiddleware: () => (/* binding */ regionRedirectEndpointMiddleware),\n/* harmony export */   regionRedirectEndpointMiddlewareOptions: () => (/* binding */ regionRedirectEndpointMiddlewareOptions)\n/* harmony export */ });\nconst regionRedirectEndpointMiddleware = (config) => {\n    return (next, context) => async (args) => {\n        const originalRegion = await config.region();\n        const regionProviderRef = config.region;\n        let unlock = () => { };\n        if (context.__s3RegionRedirect) {\n            Object.defineProperty(config, \"region\", {\n                writable: false,\n                value: async () => {\n                    return context.__s3RegionRedirect;\n                },\n            });\n            unlock = () => Object.defineProperty(config, \"region\", {\n                writable: true,\n                value: regionProviderRef,\n            });\n        }\n        try {\n            const result = await next(args);\n            if (context.__s3RegionRedirect) {\n                unlock();\n                const region = await config.region();\n                if (originalRegion !== region) {\n                    throw new Error(\"Region was not restored following S3 region redirect.\");\n                }\n            }\n            return result;\n        }\n        catch (e) {\n            unlock();\n            throw e;\n        }\n    };\n};\nconst regionRedirectEndpointMiddlewareOptions = {\n    tags: [\"REGION_REDIRECT\", \"S3\"],\n    name: \"regionRedirectEndpointMiddleware\",\n    override: true,\n    relation: \"before\",\n    toMiddleware: \"endpointV2Middleware\",\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/region-redirect-endpoint-middleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/region-redirect-middleware.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/region-redirect-middleware.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRegionRedirectMiddlewarePlugin: () => (/* binding */ getRegionRedirectMiddlewarePlugin),\n/* harmony export */   regionRedirectMiddleware: () => (/* binding */ regionRedirectMiddleware),\n/* harmony export */   regionRedirectMiddlewareOptions: () => (/* binding */ regionRedirectMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _region_redirect_endpoint_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./region-redirect-endpoint-middleware */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/region-redirect-endpoint-middleware.js\");\n\nfunction regionRedirectMiddleware(clientConfig) {\n    return (next, context) => async (args) => {\n        try {\n            return await next(args);\n        }\n        catch (err) {\n            if (clientConfig.followRegionRedirects) {\n                if (err?.$metadata?.httpStatusCode === 301 ||\n                    (err?.$metadata?.httpStatusCode === 400 && err?.name === \"IllegalLocationConstraintException\")) {\n                    try {\n                        const actualRegion = err.$response.headers[\"x-amz-bucket-region\"];\n                        context.logger?.debug(`Redirecting from ${await clientConfig.region()} to ${actualRegion}`);\n                        context.__s3RegionRedirect = actualRegion;\n                    }\n                    catch (e) {\n                        throw new Error(\"Region redirect failed: \" + e);\n                    }\n                    return next(args);\n                }\n            }\n            throw err;\n        }\n    };\n}\nconst regionRedirectMiddlewareOptions = {\n    step: \"initialize\",\n    tags: [\"REGION_REDIRECT\", \"S3\"],\n    name: \"regionRedirectMiddleware\",\n    override: true,\n};\nconst getRegionRedirectMiddlewarePlugin = (clientConfig) => ({\n    applyToStack: (clientStack) => {\n        clientStack.add(regionRedirectMiddleware(clientConfig), regionRedirectMiddlewareOptions);\n        clientStack.addRelativeTo((0,_region_redirect_endpoint_middleware__WEBPACK_IMPORTED_MODULE_0__.regionRedirectEndpointMiddleware)(clientConfig), _region_redirect_endpoint_middleware__WEBPACK_IMPORTED_MODULE_0__.regionRedirectEndpointMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/region-redirect-middleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-expires-middleware.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-expires-middleware.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getS3ExpiresMiddlewarePlugin: () => (/* binding */ getS3ExpiresMiddlewarePlugin),\n/* harmony export */   s3ExpiresMiddleware: () => (/* binding */ s3ExpiresMiddleware),\n/* harmony export */   s3ExpiresMiddlewareOptions: () => (/* binding */ s3ExpiresMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(ssr)/./node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/smithy-client */ \"(ssr)/./node_modules/@smithy/smithy-client/dist-es/index.js\");\n\n\nconst s3ExpiresMiddleware = (config) => {\n    return (next, context) => async (args) => {\n        const result = await next(args);\n        const { response } = result;\n        if (_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpResponse.isInstance(response)) {\n            if (response.headers.expires) {\n                response.headers.expiresstring = response.headers.expires;\n                try {\n                    (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.parseRfc7231DateTime)(response.headers.expires);\n                }\n                catch (e) {\n                    context.logger?.warn(`AWS SDK Warning for ${context.clientName}::${context.commandName} response parsing (${response.headers.expires}): ${e}`);\n                    delete response.headers.expires;\n                }\n            }\n        }\n        return result;\n    };\n};\nconst s3ExpiresMiddlewareOptions = {\n    tags: [\"S3\"],\n    name: \"s3ExpiresMiddleware\",\n    override: true,\n    relation: \"after\",\n    toMiddleware: \"deserializerMiddleware\",\n};\nconst getS3ExpiresMiddlewarePlugin = (clientConfig) => ({\n    applyToStack: (clientStack) => {\n        clientStack.addRelativeTo(s3ExpiresMiddleware(clientConfig), s3ExpiresMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-expires-middleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/S3ExpressIdentityCache.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/S3ExpressIdentityCache.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   S3ExpressIdentityCache: () => (/* binding */ S3ExpressIdentityCache)\n/* harmony export */ });\nclass S3ExpressIdentityCache {\n    data;\n    lastPurgeTime = Date.now();\n    static EXPIRED_CREDENTIAL_PURGE_INTERVAL_MS = 30_000;\n    constructor(data = {}) {\n        this.data = data;\n    }\n    get(key) {\n        const entry = this.data[key];\n        if (!entry) {\n            return;\n        }\n        return entry;\n    }\n    set(key, entry) {\n        this.data[key] = entry;\n        return entry;\n    }\n    delete(key) {\n        delete this.data[key];\n    }\n    async purgeExpired() {\n        const now = Date.now();\n        if (this.lastPurgeTime + S3ExpressIdentityCache.EXPIRED_CREDENTIAL_PURGE_INTERVAL_MS > now) {\n            return;\n        }\n        for (const key in this.data) {\n            const entry = this.data[key];\n            if (!entry.isRefreshing) {\n                const credential = await entry.identity;\n                if (credential.expiration) {\n                    if (credential.expiration.getTime() < now) {\n                        delete this.data[key];\n                    }\n                }\n            }\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1zZGstczMvZGlzdC1lcy9zMy1leHByZXNzL2NsYXNzZXMvUzNFeHByZXNzSWRlbnRpdHlDYWNoZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtc2RrLXMzL2Rpc3QtZXMvczMtZXhwcmVzcy9jbGFzc2VzL1MzRXhwcmVzc0lkZW50aXR5Q2FjaGUuanM/ZGU4MSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgUzNFeHByZXNzSWRlbnRpdHlDYWNoZSB7XG4gICAgZGF0YTtcbiAgICBsYXN0UHVyZ2VUaW1lID0gRGF0ZS5ub3coKTtcbiAgICBzdGF0aWMgRVhQSVJFRF9DUkVERU5USUFMX1BVUkdFX0lOVEVSVkFMX01TID0gMzBfMDAwO1xuICAgIGNvbnN0cnVjdG9yKGRhdGEgPSB7fSkge1xuICAgICAgICB0aGlzLmRhdGEgPSBkYXRhO1xuICAgIH1cbiAgICBnZXQoa2V5KSB7XG4gICAgICAgIGNvbnN0IGVudHJ5ID0gdGhpcy5kYXRhW2tleV07XG4gICAgICAgIGlmICghZW50cnkpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZW50cnk7XG4gICAgfVxuICAgIHNldChrZXksIGVudHJ5KSB7XG4gICAgICAgIHRoaXMuZGF0YVtrZXldID0gZW50cnk7XG4gICAgICAgIHJldHVybiBlbnRyeTtcbiAgICB9XG4gICAgZGVsZXRlKGtleSkge1xuICAgICAgICBkZWxldGUgdGhpcy5kYXRhW2tleV07XG4gICAgfVxuICAgIGFzeW5jIHB1cmdlRXhwaXJlZCgpIHtcbiAgICAgICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcbiAgICAgICAgaWYgKHRoaXMubGFzdFB1cmdlVGltZSArIFMzRXhwcmVzc0lkZW50aXR5Q2FjaGUuRVhQSVJFRF9DUkVERU5USUFMX1BVUkdFX0lOVEVSVkFMX01TID4gbm93KSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gdGhpcy5kYXRhKSB7XG4gICAgICAgICAgICBjb25zdCBlbnRyeSA9IHRoaXMuZGF0YVtrZXldO1xuICAgICAgICAgICAgaWYgKCFlbnRyeS5pc1JlZnJlc2hpbmcpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBjcmVkZW50aWFsID0gYXdhaXQgZW50cnkuaWRlbnRpdHk7XG4gICAgICAgICAgICAgICAgaWYgKGNyZWRlbnRpYWwuZXhwaXJhdGlvbikge1xuICAgICAgICAgICAgICAgICAgICBpZiAoY3JlZGVudGlhbC5leHBpcmF0aW9uLmdldFRpbWUoKSA8IG5vdykge1xuICAgICAgICAgICAgICAgICAgICAgICAgZGVsZXRlIHRoaXMuZGF0YVtrZXldO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/S3ExpressIdentityCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/S3ExpressIdentityCacheEntry.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/S3ExpressIdentityCacheEntry.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   S3ExpressIdentityCacheEntry: () => (/* binding */ S3ExpressIdentityCacheEntry)\n/* harmony export */ });\nclass S3ExpressIdentityCacheEntry {\n    _identity;\n    isRefreshing;\n    accessed;\n    constructor(_identity, isRefreshing = false, accessed = Date.now()) {\n        this._identity = _identity;\n        this.isRefreshing = isRefreshing;\n        this.accessed = accessed;\n    }\n    get identity() {\n        this.accessed = Date.now();\n        return this._identity;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1zZGstczMvZGlzdC1lcy9zMy1leHByZXNzL2NsYXNzZXMvUzNFeHByZXNzSWRlbnRpdHlDYWNoZUVudHJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLXNkay1zMy9kaXN0LWVzL3MzLWV4cHJlc3MvY2xhc3Nlcy9TM0V4cHJlc3NJZGVudGl0eUNhY2hlRW50cnkuanM/NTQzOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgUzNFeHByZXNzSWRlbnRpdHlDYWNoZUVudHJ5IHtcbiAgICBfaWRlbnRpdHk7XG4gICAgaXNSZWZyZXNoaW5nO1xuICAgIGFjY2Vzc2VkO1xuICAgIGNvbnN0cnVjdG9yKF9pZGVudGl0eSwgaXNSZWZyZXNoaW5nID0gZmFsc2UsIGFjY2Vzc2VkID0gRGF0ZS5ub3coKSkge1xuICAgICAgICB0aGlzLl9pZGVudGl0eSA9IF9pZGVudGl0eTtcbiAgICAgICAgdGhpcy5pc1JlZnJlc2hpbmcgPSBpc1JlZnJlc2hpbmc7XG4gICAgICAgIHRoaXMuYWNjZXNzZWQgPSBhY2Nlc3NlZDtcbiAgICB9XG4gICAgZ2V0IGlkZW50aXR5KCkge1xuICAgICAgICB0aGlzLmFjY2Vzc2VkID0gRGF0ZS5ub3coKTtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2lkZW50aXR5O1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/S3ExpressIdentityCacheEntry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/S3ExpressIdentityProviderImpl.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/S3ExpressIdentityProviderImpl.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   S3ExpressIdentityProviderImpl: () => (/* binding */ S3ExpressIdentityProviderImpl)\n/* harmony export */ });\n/* harmony import */ var _S3ExpressIdentityCache__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./S3ExpressIdentityCache */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/S3ExpressIdentityCache.js\");\n/* harmony import */ var _S3ExpressIdentityCacheEntry__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./S3ExpressIdentityCacheEntry */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/S3ExpressIdentityCacheEntry.js\");\n\n\nclass S3ExpressIdentityProviderImpl {\n    createSessionFn;\n    cache;\n    static REFRESH_WINDOW_MS = 60_000;\n    constructor(createSessionFn, cache = new _S3ExpressIdentityCache__WEBPACK_IMPORTED_MODULE_0__.S3ExpressIdentityCache()) {\n        this.createSessionFn = createSessionFn;\n        this.cache = cache;\n    }\n    async getS3ExpressIdentity(awsIdentity, identityProperties) {\n        const key = identityProperties.Bucket;\n        const { cache } = this;\n        const entry = cache.get(key);\n        if (entry) {\n            return entry.identity.then((identity) => {\n                const isExpired = (identity.expiration?.getTime() ?? 0) < Date.now();\n                if (isExpired) {\n                    return cache.set(key, new _S3ExpressIdentityCacheEntry__WEBPACK_IMPORTED_MODULE_1__.S3ExpressIdentityCacheEntry(this.getIdentity(key))).identity;\n                }\n                const isExpiringSoon = (identity.expiration?.getTime() ?? 0) < Date.now() + S3ExpressIdentityProviderImpl.REFRESH_WINDOW_MS;\n                if (isExpiringSoon && !entry.isRefreshing) {\n                    entry.isRefreshing = true;\n                    this.getIdentity(key).then((id) => {\n                        cache.set(key, new _S3ExpressIdentityCacheEntry__WEBPACK_IMPORTED_MODULE_1__.S3ExpressIdentityCacheEntry(Promise.resolve(id)));\n                    });\n                }\n                return identity;\n            });\n        }\n        return cache.set(key, new _S3ExpressIdentityCacheEntry__WEBPACK_IMPORTED_MODULE_1__.S3ExpressIdentityCacheEntry(this.getIdentity(key))).identity;\n    }\n    async getIdentity(key) {\n        await this.cache.purgeExpired().catch((error) => {\n            console.warn(\"Error while clearing expired entries in S3ExpressIdentityCache: \\n\" + error);\n        });\n        const session = await this.createSessionFn(key);\n        if (!session.Credentials?.AccessKeyId || !session.Credentials?.SecretAccessKey) {\n            throw new Error(\"s3#createSession response credential missing AccessKeyId or SecretAccessKey.\");\n        }\n        const identity = {\n            accessKeyId: session.Credentials.AccessKeyId,\n            secretAccessKey: session.Credentials.SecretAccessKey,\n            sessionToken: session.Credentials.SessionToken,\n            expiration: session.Credentials.Expiration ? new Date(session.Credentials.Expiration) : undefined,\n        };\n        return identity;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/S3ExpressIdentityProviderImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/SignatureV4S3Express.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/SignatureV4S3Express.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignatureV4S3Express: () => (/* binding */ SignatureV4S3Express)\n/* harmony export */ });\n/* harmony import */ var _smithy_signature_v4__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/signature-v4 */ \"(ssr)/./node_modules/@smithy/signature-v4/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../constants */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/constants.js\");\n\n\nclass SignatureV4S3Express extends _smithy_signature_v4__WEBPACK_IMPORTED_MODULE_0__.SignatureV4 {\n    async signWithCredentials(requestToSign, credentials, options) {\n        const credentialsWithoutSessionToken = getCredentialsWithoutSessionToken(credentials);\n        requestToSign.headers[_constants__WEBPACK_IMPORTED_MODULE_1__.SESSION_TOKEN_HEADER] = credentials.sessionToken;\n        const privateAccess = this;\n        setSingleOverride(privateAccess, credentialsWithoutSessionToken);\n        return privateAccess.signRequest(requestToSign, options ?? {});\n    }\n    async presignWithCredentials(requestToSign, credentials, options) {\n        const credentialsWithoutSessionToken = getCredentialsWithoutSessionToken(credentials);\n        delete requestToSign.headers[_constants__WEBPACK_IMPORTED_MODULE_1__.SESSION_TOKEN_HEADER];\n        requestToSign.headers[_constants__WEBPACK_IMPORTED_MODULE_1__.SESSION_TOKEN_QUERY_PARAM] = credentials.sessionToken;\n        requestToSign.query = requestToSign.query ?? {};\n        requestToSign.query[_constants__WEBPACK_IMPORTED_MODULE_1__.SESSION_TOKEN_QUERY_PARAM] = credentials.sessionToken;\n        const privateAccess = this;\n        setSingleOverride(privateAccess, credentialsWithoutSessionToken);\n        return this.presign(requestToSign, options);\n    }\n}\nfunction getCredentialsWithoutSessionToken(credentials) {\n    const credentialsWithoutSessionToken = {\n        accessKeyId: credentials.accessKeyId,\n        secretAccessKey: credentials.secretAccessKey,\n        expiration: credentials.expiration,\n    };\n    return credentialsWithoutSessionToken;\n}\nfunction setSingleOverride(privateAccess, credentialsWithoutSessionToken) {\n    const id = setTimeout(() => {\n        throw new Error(\"SignatureV4S3Express credential override was created but not called.\");\n    }, 10);\n    const currentCredentialProvider = privateAccess.credentialProvider;\n    const overrideCredentialsProviderOnce = () => {\n        clearTimeout(id);\n        privateAccess.credentialProvider = currentCredentialProvider;\n        return Promise.resolve(credentialsWithoutSessionToken);\n    };\n    privateAccess.credentialProvider = overrideCredentialsProviderOnce;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/SignatureV4S3Express.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/constants.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/constants.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_ENV_NAME: () => (/* binding */ NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_ENV_NAME),\n/* harmony export */   NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_INI_NAME: () => (/* binding */ NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_INI_NAME),\n/* harmony export */   NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_OPTIONS: () => (/* binding */ NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_OPTIONS),\n/* harmony export */   S3_EXPRESS_AUTH_SCHEME: () => (/* binding */ S3_EXPRESS_AUTH_SCHEME),\n/* harmony export */   S3_EXPRESS_BACKEND: () => (/* binding */ S3_EXPRESS_BACKEND),\n/* harmony export */   S3_EXPRESS_BUCKET_TYPE: () => (/* binding */ S3_EXPRESS_BUCKET_TYPE),\n/* harmony export */   SESSION_TOKEN_HEADER: () => (/* binding */ SESSION_TOKEN_HEADER),\n/* harmony export */   SESSION_TOKEN_QUERY_PARAM: () => (/* binding */ SESSION_TOKEN_QUERY_PARAM)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_config_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-config-provider */ \"(ssr)/./node_modules/@smithy/util-config-provider/dist-es/index.js\");\n\nconst S3_EXPRESS_BUCKET_TYPE = \"Directory\";\nconst S3_EXPRESS_BACKEND = \"S3Express\";\nconst S3_EXPRESS_AUTH_SCHEME = \"sigv4-s3express\";\nconst SESSION_TOKEN_QUERY_PARAM = \"X-Amz-S3session-Token\";\nconst SESSION_TOKEN_HEADER = SESSION_TOKEN_QUERY_PARAM.toLowerCase();\nconst NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_ENV_NAME = \"AWS_S3_DISABLE_EXPRESS_SESSION_AUTH\";\nconst NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_INI_NAME = \"s3_disable_express_session_auth\";\nconst NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_OPTIONS = {\n    environmentVariableSelector: (env) => (0,_smithy_util_config_provider__WEBPACK_IMPORTED_MODULE_0__.booleanSelector)(env, NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_ENV_NAME, _smithy_util_config_provider__WEBPACK_IMPORTED_MODULE_0__.SelectorType.ENV),\n    configFileSelector: (profile) => (0,_smithy_util_config_provider__WEBPACK_IMPORTED_MODULE_0__.booleanSelector)(profile, NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_INI_NAME, _smithy_util_config_provider__WEBPACK_IMPORTED_MODULE_0__.SelectorType.CONFIG),\n    default: false,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1zZGstczMvZGlzdC1lcy9zMy1leHByZXNzL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBNkU7QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNQLDBDQUEwQyw2RUFBZSxxREFBcUQsc0VBQVk7QUFDMUgscUNBQXFDLDZFQUFlLHlEQUF5RCxzRUFBWTtBQUN6SDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLXNkay1zMy9kaXN0LWVzL3MzLWV4cHJlc3MvY29uc3RhbnRzLmpzP2QwNjYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYm9vbGVhblNlbGVjdG9yLCBTZWxlY3RvclR5cGUgfSBmcm9tIFwiQHNtaXRoeS91dGlsLWNvbmZpZy1wcm92aWRlclwiO1xuZXhwb3J0IGNvbnN0IFMzX0VYUFJFU1NfQlVDS0VUX1RZUEUgPSBcIkRpcmVjdG9yeVwiO1xuZXhwb3J0IGNvbnN0IFMzX0VYUFJFU1NfQkFDS0VORCA9IFwiUzNFeHByZXNzXCI7XG5leHBvcnQgY29uc3QgUzNfRVhQUkVTU19BVVRIX1NDSEVNRSA9IFwic2lndjQtczNleHByZXNzXCI7XG5leHBvcnQgY29uc3QgU0VTU0lPTl9UT0tFTl9RVUVSWV9QQVJBTSA9IFwiWC1BbXotUzNzZXNzaW9uLVRva2VuXCI7XG5leHBvcnQgY29uc3QgU0VTU0lPTl9UT0tFTl9IRUFERVIgPSBTRVNTSU9OX1RPS0VOX1FVRVJZX1BBUkFNLnRvTG93ZXJDYXNlKCk7XG5leHBvcnQgY29uc3QgTk9ERV9ESVNBQkxFX1MzX0VYUFJFU1NfU0VTU0lPTl9BVVRIX0VOVl9OQU1FID0gXCJBV1NfUzNfRElTQUJMRV9FWFBSRVNTX1NFU1NJT05fQVVUSFwiO1xuZXhwb3J0IGNvbnN0IE5PREVfRElTQUJMRV9TM19FWFBSRVNTX1NFU1NJT05fQVVUSF9JTklfTkFNRSA9IFwiczNfZGlzYWJsZV9leHByZXNzX3Nlc3Npb25fYXV0aFwiO1xuZXhwb3J0IGNvbnN0IE5PREVfRElTQUJMRV9TM19FWFBSRVNTX1NFU1NJT05fQVVUSF9PUFRJT05TID0ge1xuICAgIGVudmlyb25tZW50VmFyaWFibGVTZWxlY3RvcjogKGVudikgPT4gYm9vbGVhblNlbGVjdG9yKGVudiwgTk9ERV9ESVNBQkxFX1MzX0VYUFJFU1NfU0VTU0lPTl9BVVRIX0VOVl9OQU1FLCBTZWxlY3RvclR5cGUuRU5WKSxcbiAgICBjb25maWdGaWxlU2VsZWN0b3I6IChwcm9maWxlKSA9PiBib29sZWFuU2VsZWN0b3IocHJvZmlsZSwgTk9ERV9ESVNBQkxFX1MzX0VYUFJFU1NfU0VTU0lPTl9BVVRIX0lOSV9OQU1FLCBTZWxlY3RvclR5cGUuQ09ORklHKSxcbiAgICBkZWZhdWx0OiBmYWxzZSxcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/functions/s3ExpressHttpSigningMiddleware.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/functions/s3ExpressHttpSigningMiddleware.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getS3ExpressHttpSigningPlugin: () => (/* binding */ getS3ExpressHttpSigningPlugin),\n/* harmony export */   s3ExpressHttpSigningMiddleware: () => (/* binding */ s3ExpressHttpSigningMiddleware),\n/* harmony export */   s3ExpressHttpSigningMiddlewareOptions: () => (/* binding */ s3ExpressHttpSigningMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _smithy_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core */ \"(ssr)/./node_modules/@smithy/core/dist-es/index.js\");\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/protocol-http */ \"(ssr)/./node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/types */ \"(ssr)/./node_modules/@smithy/types/dist-es/index.js\");\n/* harmony import */ var _smithy_util_middleware__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @smithy/util-middleware */ \"(ssr)/./node_modules/@smithy/util-middleware/dist-es/index.js\");\n/* harmony import */ var _signS3Express__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./signS3Express */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/functions/signS3Express.js\");\n\n\n\n\n\nconst defaultErrorHandler = (signingProperties) => (error) => {\n    throw error;\n};\nconst defaultSuccessHandler = (httpResponse, signingProperties) => { };\nconst s3ExpressHttpSigningMiddlewareOptions = _smithy_core__WEBPACK_IMPORTED_MODULE_0__.httpSigningMiddlewareOptions;\nconst s3ExpressHttpSigningMiddleware = (config) => (next, context) => async (args) => {\n    if (!_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__.HttpRequest.isInstance(args.request)) {\n        return next(args);\n    }\n    const smithyContext = (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_3__.getSmithyContext)(context);\n    const scheme = smithyContext.selectedHttpAuthScheme;\n    if (!scheme) {\n        throw new Error(`No HttpAuthScheme was selected: unable to sign request`);\n    }\n    const { httpAuthOption: { signingProperties = {} }, identity, signer, } = scheme;\n    let request;\n    if (context.s3ExpressIdentity) {\n        request = await (0,_signS3Express__WEBPACK_IMPORTED_MODULE_4__.signS3Express)(context.s3ExpressIdentity, signingProperties, args.request, await config.signer());\n    }\n    else {\n        request = await signer.sign(args.request, identity, signingProperties);\n    }\n    const output = await next({\n        ...args,\n        request,\n    }).catch((signer.errorHandler || defaultErrorHandler)(signingProperties));\n    (signer.successHandler || defaultSuccessHandler)(output.response, signingProperties);\n    return output;\n};\nconst getS3ExpressHttpSigningPlugin = (config) => ({\n    applyToStack: (clientStack) => {\n        clientStack.addRelativeTo(s3ExpressHttpSigningMiddleware(config), _smithy_core__WEBPACK_IMPORTED_MODULE_0__.httpSigningMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/functions/s3ExpressHttpSigningMiddleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/functions/s3ExpressMiddleware.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/functions/s3ExpressMiddleware.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getS3ExpressPlugin: () => (/* binding */ getS3ExpressPlugin),\n/* harmony export */   s3ExpressMiddleware: () => (/* binding */ s3ExpressMiddleware),\n/* harmony export */   s3ExpressMiddlewareOptions: () => (/* binding */ s3ExpressMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/core */ \"(ssr)/./node_modules/@aws-sdk/core/dist-es/submodules/client/setFeature.js\");\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(ssr)/./node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../constants */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/constants.js\");\n\n\n\nconst s3ExpressMiddleware = (options) => {\n    return (next, context) => async (args) => {\n        if (context.endpointV2) {\n            const endpoint = context.endpointV2;\n            const isS3ExpressAuth = endpoint.properties?.authSchemes?.[0]?.name === _constants__WEBPACK_IMPORTED_MODULE_1__.S3_EXPRESS_AUTH_SCHEME;\n            const isS3ExpressBucket = endpoint.properties?.backend === _constants__WEBPACK_IMPORTED_MODULE_1__.S3_EXPRESS_BACKEND ||\n                endpoint.properties?.bucketType === _constants__WEBPACK_IMPORTED_MODULE_1__.S3_EXPRESS_BUCKET_TYPE;\n            if (isS3ExpressBucket) {\n                (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_2__.setFeature)(context, \"S3_EXPRESS_BUCKET\", \"J\");\n                context.isS3ExpressBucket = true;\n            }\n            if (isS3ExpressAuth) {\n                const requestBucket = args.input.Bucket;\n                if (requestBucket) {\n                    const s3ExpressIdentity = await options.s3ExpressIdentityProvider.getS3ExpressIdentity(await options.credentials(), {\n                        Bucket: requestBucket,\n                    });\n                    context.s3ExpressIdentity = s3ExpressIdentity;\n                    if (_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest.isInstance(args.request) && s3ExpressIdentity.sessionToken) {\n                        args.request.headers[_constants__WEBPACK_IMPORTED_MODULE_1__.SESSION_TOKEN_HEADER] = s3ExpressIdentity.sessionToken;\n                    }\n                }\n            }\n        }\n        return next(args);\n    };\n};\nconst s3ExpressMiddlewareOptions = {\n    name: \"s3ExpressMiddleware\",\n    step: \"build\",\n    tags: [\"S3\", \"S3_EXPRESS\"],\n    override: true,\n};\nconst getS3ExpressPlugin = (options) => ({\n    applyToStack: (clientStack) => {\n        clientStack.add(s3ExpressMiddleware(options), s3ExpressMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/functions/s3ExpressMiddleware.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/functions/signS3Express.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/functions/signS3Express.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   signS3Express: () => (/* binding */ signS3Express)\n/* harmony export */ });\nconst signS3Express = async (s3ExpressIdentity, signingOptions, request, sigV4MultiRegionSigner) => {\n    const signedRequest = await sigV4MultiRegionSigner.signWithCredentials(request, s3ExpressIdentity, {});\n    if (signedRequest.headers[\"X-Amz-Security-Token\"] || signedRequest.headers[\"x-amz-security-token\"]) {\n        throw new Error(\"X-Amz-Security-Token must not be set for s3-express requests.\");\n    }\n    return signedRequest;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1zZGstczMvZGlzdC1lcy9zMy1leHByZXNzL2Z1bmN0aW9ucy9zaWduUzNFeHByZXNzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQLHlHQUF5RztBQUN6RztBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLXNkay1zMy9kaXN0LWVzL3MzLWV4cHJlc3MvZnVuY3Rpb25zL3NpZ25TM0V4cHJlc3MuanM/Yjc3OSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3Qgc2lnblMzRXhwcmVzcyA9IGFzeW5jIChzM0V4cHJlc3NJZGVudGl0eSwgc2lnbmluZ09wdGlvbnMsIHJlcXVlc3QsIHNpZ1Y0TXVsdGlSZWdpb25TaWduZXIpID0+IHtcbiAgICBjb25zdCBzaWduZWRSZXF1ZXN0ID0gYXdhaXQgc2lnVjRNdWx0aVJlZ2lvblNpZ25lci5zaWduV2l0aENyZWRlbnRpYWxzKHJlcXVlc3QsIHMzRXhwcmVzc0lkZW50aXR5LCB7fSk7XG4gICAgaWYgKHNpZ25lZFJlcXVlc3QuaGVhZGVyc1tcIlgtQW16LVNlY3VyaXR5LVRva2VuXCJdIHx8IHNpZ25lZFJlcXVlc3QuaGVhZGVyc1tcIngtYW16LXNlY3VyaXR5LXRva2VuXCJdKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIlgtQW16LVNlY3VyaXR5LVRva2VuIG11c3Qgbm90IGJlIHNldCBmb3IgczMtZXhwcmVzcyByZXF1ZXN0cy5cIik7XG4gICAgfVxuICAgIHJldHVybiBzaWduZWRSZXF1ZXN0O1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/functions/signS3Express.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/index.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/index.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_OPTIONS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_4__.NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_OPTIONS),\n/* harmony export */   S3ExpressIdentityCache: () => (/* reexport safe */ _classes_S3ExpressIdentityCache__WEBPACK_IMPORTED_MODULE_0__.S3ExpressIdentityCache),\n/* harmony export */   S3ExpressIdentityCacheEntry: () => (/* reexport safe */ _classes_S3ExpressIdentityCacheEntry__WEBPACK_IMPORTED_MODULE_1__.S3ExpressIdentityCacheEntry),\n/* harmony export */   S3ExpressIdentityProviderImpl: () => (/* reexport safe */ _classes_S3ExpressIdentityProviderImpl__WEBPACK_IMPORTED_MODULE_2__.S3ExpressIdentityProviderImpl),\n/* harmony export */   SignatureV4S3Express: () => (/* reexport safe */ _classes_SignatureV4S3Express__WEBPACK_IMPORTED_MODULE_3__.SignatureV4S3Express),\n/* harmony export */   getS3ExpressHttpSigningPlugin: () => (/* reexport safe */ _functions_s3ExpressHttpSigningMiddleware__WEBPACK_IMPORTED_MODULE_6__.getS3ExpressHttpSigningPlugin),\n/* harmony export */   getS3ExpressPlugin: () => (/* reexport safe */ _functions_s3ExpressMiddleware__WEBPACK_IMPORTED_MODULE_5__.getS3ExpressPlugin),\n/* harmony export */   s3ExpressHttpSigningMiddleware: () => (/* reexport safe */ _functions_s3ExpressHttpSigningMiddleware__WEBPACK_IMPORTED_MODULE_6__.s3ExpressHttpSigningMiddleware),\n/* harmony export */   s3ExpressHttpSigningMiddlewareOptions: () => (/* reexport safe */ _functions_s3ExpressHttpSigningMiddleware__WEBPACK_IMPORTED_MODULE_6__.s3ExpressHttpSigningMiddlewareOptions),\n/* harmony export */   s3ExpressMiddleware: () => (/* reexport safe */ _functions_s3ExpressMiddleware__WEBPACK_IMPORTED_MODULE_5__.s3ExpressMiddleware),\n/* harmony export */   s3ExpressMiddlewareOptions: () => (/* reexport safe */ _functions_s3ExpressMiddleware__WEBPACK_IMPORTED_MODULE_5__.s3ExpressMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _classes_S3ExpressIdentityCache__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./classes/S3ExpressIdentityCache */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/S3ExpressIdentityCache.js\");\n/* harmony import */ var _classes_S3ExpressIdentityCacheEntry__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./classes/S3ExpressIdentityCacheEntry */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/S3ExpressIdentityCacheEntry.js\");\n/* harmony import */ var _classes_S3ExpressIdentityProviderImpl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./classes/S3ExpressIdentityProviderImpl */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/S3ExpressIdentityProviderImpl.js\");\n/* harmony import */ var _classes_SignatureV4S3Express__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./classes/SignatureV4S3Express */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/classes/SignatureV4S3Express.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/constants.js\");\n/* harmony import */ var _functions_s3ExpressMiddleware__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./functions/s3ExpressMiddleware */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/functions/s3ExpressMiddleware.js\");\n/* harmony import */ var _functions_s3ExpressHttpSigningMiddleware__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./functions/s3ExpressHttpSigningMiddleware */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/functions/s3ExpressHttpSigningMiddleware.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1zZGstczMvZGlzdC1lcy9zMy1leHByZXNzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUEwRTtBQUNVO0FBQ0k7QUFDbEI7QUFDSztBQUMyQztBQUM2QyIsInNvdXJjZXMiOlsid2VicGFjazovL2hlcmJhbGljaW91cy1uZXh0anMvLi9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1zZGstczMvZGlzdC1lcy9zMy1leHByZXNzL2luZGV4LmpzPzY0ZjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgUzNFeHByZXNzSWRlbnRpdHlDYWNoZSB9IGZyb20gXCIuL2NsYXNzZXMvUzNFeHByZXNzSWRlbnRpdHlDYWNoZVwiO1xuZXhwb3J0IHsgUzNFeHByZXNzSWRlbnRpdHlDYWNoZUVudHJ5IH0gZnJvbSBcIi4vY2xhc3Nlcy9TM0V4cHJlc3NJZGVudGl0eUNhY2hlRW50cnlcIjtcbmV4cG9ydCB7IFMzRXhwcmVzc0lkZW50aXR5UHJvdmlkZXJJbXBsIH0gZnJvbSBcIi4vY2xhc3Nlcy9TM0V4cHJlc3NJZGVudGl0eVByb3ZpZGVySW1wbFwiO1xuZXhwb3J0IHsgU2lnbmF0dXJlVjRTM0V4cHJlc3MgfSBmcm9tIFwiLi9jbGFzc2VzL1NpZ25hdHVyZVY0UzNFeHByZXNzXCI7XG5leHBvcnQgeyBOT0RFX0RJU0FCTEVfUzNfRVhQUkVTU19TRVNTSU9OX0FVVEhfT1BUSU9OUyB9IGZyb20gXCIuL2NvbnN0YW50c1wiO1xuZXhwb3J0IHsgZ2V0UzNFeHByZXNzUGx1Z2luLCBzM0V4cHJlc3NNaWRkbGV3YXJlLCBzM0V4cHJlc3NNaWRkbGV3YXJlT3B0aW9ucyB9IGZyb20gXCIuL2Z1bmN0aW9ucy9zM0V4cHJlc3NNaWRkbGV3YXJlXCI7XG5leHBvcnQgeyBnZXRTM0V4cHJlc3NIdHRwU2lnbmluZ1BsdWdpbiwgczNFeHByZXNzSHR0cFNpZ25pbmdNaWRkbGV3YXJlLCBzM0V4cHJlc3NIdHRwU2lnbmluZ01pZGRsZXdhcmVPcHRpb25zLCB9IGZyb20gXCIuL2Z1bmN0aW9ucy9zM0V4cHJlc3NIdHRwU2lnbmluZ01pZGRsZXdhcmVcIjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3Configuration.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3Configuration.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveS3Config: () => (/* binding */ resolveS3Config)\n/* harmony export */ });\n/* harmony import */ var _s3_express__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./s3-express */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3-express/index.js\");\n\nconst resolveS3Config = (input, { session, }) => {\n    const [s3ClientProvider, CreateSessionCommandCtor] = session;\n    const { forcePathStyle, useAccelerateEndpoint, disableMultiregionAccessPoints, followRegionRedirects, s3ExpressIdentityProvider, bucketEndpoint, } = input;\n    return Object.assign(input, {\n        forcePathStyle: forcePathStyle ?? false,\n        useAccelerateEndpoint: useAccelerateEndpoint ?? false,\n        disableMultiregionAccessPoints: disableMultiregionAccessPoints ?? false,\n        followRegionRedirects: followRegionRedirects ?? false,\n        s3ExpressIdentityProvider: s3ExpressIdentityProvider ??\n            new _s3_express__WEBPACK_IMPORTED_MODULE_0__.S3ExpressIdentityProviderImpl(async (key) => s3ClientProvider().send(new CreateSessionCommandCtor({\n                Bucket: key,\n            }))),\n        bucketEndpoint: bucketEndpoint ?? false,\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1zZGstczMvZGlzdC1lcy9zM0NvbmZpZ3VyYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNkQ7QUFDdEQsa0NBQWtDLFVBQVU7QUFDbkQ7QUFDQSxZQUFZLDJJQUEySTtBQUN2SjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0Isc0VBQTZCO0FBQzdDO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsS0FBSztBQUNMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLXNkay1zMy9kaXN0LWVzL3MzQ29uZmlndXJhdGlvbi5qcz8wNDM2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFMzRXhwcmVzc0lkZW50aXR5UHJvdmlkZXJJbXBsIH0gZnJvbSBcIi4vczMtZXhwcmVzc1wiO1xuZXhwb3J0IGNvbnN0IHJlc29sdmVTM0NvbmZpZyA9IChpbnB1dCwgeyBzZXNzaW9uLCB9KSA9PiB7XG4gICAgY29uc3QgW3MzQ2xpZW50UHJvdmlkZXIsIENyZWF0ZVNlc3Npb25Db21tYW5kQ3Rvcl0gPSBzZXNzaW9uO1xuICAgIGNvbnN0IHsgZm9yY2VQYXRoU3R5bGUsIHVzZUFjY2VsZXJhdGVFbmRwb2ludCwgZGlzYWJsZU11bHRpcmVnaW9uQWNjZXNzUG9pbnRzLCBmb2xsb3dSZWdpb25SZWRpcmVjdHMsIHMzRXhwcmVzc0lkZW50aXR5UHJvdmlkZXIsIGJ1Y2tldEVuZHBvaW50LCB9ID0gaW5wdXQ7XG4gICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oaW5wdXQsIHtcbiAgICAgICAgZm9yY2VQYXRoU3R5bGU6IGZvcmNlUGF0aFN0eWxlID8/IGZhbHNlLFxuICAgICAgICB1c2VBY2NlbGVyYXRlRW5kcG9pbnQ6IHVzZUFjY2VsZXJhdGVFbmRwb2ludCA/PyBmYWxzZSxcbiAgICAgICAgZGlzYWJsZU11bHRpcmVnaW9uQWNjZXNzUG9pbnRzOiBkaXNhYmxlTXVsdGlyZWdpb25BY2Nlc3NQb2ludHMgPz8gZmFsc2UsXG4gICAgICAgIGZvbGxvd1JlZ2lvblJlZGlyZWN0czogZm9sbG93UmVnaW9uUmVkaXJlY3RzID8/IGZhbHNlLFxuICAgICAgICBzM0V4cHJlc3NJZGVudGl0eVByb3ZpZGVyOiBzM0V4cHJlc3NJZGVudGl0eVByb3ZpZGVyID8/XG4gICAgICAgICAgICBuZXcgUzNFeHByZXNzSWRlbnRpdHlQcm92aWRlckltcGwoYXN5bmMgKGtleSkgPT4gczNDbGllbnRQcm92aWRlcigpLnNlbmQobmV3IENyZWF0ZVNlc3Npb25Db21tYW5kQ3Rvcih7XG4gICAgICAgICAgICAgICAgQnVja2V0OiBrZXksXG4gICAgICAgICAgICB9KSkpLFxuICAgICAgICBidWNrZXRFbmRwb2ludDogYnVja2V0RW5kcG9pbnQgPz8gZmFsc2UsXG4gICAgfSk7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/s3Configuration.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/throw-200-exceptions.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/throw-200-exceptions.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getThrow200ExceptionsPlugin: () => (/* binding */ getThrow200ExceptionsPlugin),\n/* harmony export */   throw200ExceptionsMiddleware: () => (/* binding */ throw200ExceptionsMiddleware),\n/* harmony export */   throw200ExceptionsMiddlewareOptions: () => (/* binding */ throw200ExceptionsMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(ssr)/./node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_util_stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-stream */ \"(ssr)/./node_modules/@smithy/util-stream/dist-es/index.js\");\n\n\nconst THROW_IF_EMPTY_BODY = {\n    CopyObjectCommand: true,\n    UploadPartCopyCommand: true,\n    CompleteMultipartUploadCommand: true,\n};\nconst MAX_BYTES_TO_INSPECT = 3000;\nconst throw200ExceptionsMiddleware = (config) => (next, context) => async (args) => {\n    const result = await next(args);\n    const { response } = result;\n    if (!_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpResponse.isInstance(response)) {\n        return result;\n    }\n    const { statusCode, body: sourceBody } = response;\n    if (statusCode < 200 || statusCode >= 300) {\n        return result;\n    }\n    const isSplittableStream = typeof sourceBody?.stream === \"function\" ||\n        typeof sourceBody?.pipe === \"function\" ||\n        typeof sourceBody?.tee === \"function\";\n    if (!isSplittableStream) {\n        return result;\n    }\n    let bodyCopy = sourceBody;\n    let body = sourceBody;\n    if (sourceBody && typeof sourceBody === \"object\" && !(sourceBody instanceof Uint8Array)) {\n        [bodyCopy, body] = await (0,_smithy_util_stream__WEBPACK_IMPORTED_MODULE_1__.splitStream)(sourceBody);\n    }\n    response.body = body;\n    const bodyBytes = await collectBody(bodyCopy, {\n        streamCollector: async (stream) => {\n            return (0,_smithy_util_stream__WEBPACK_IMPORTED_MODULE_1__.headStream)(stream, MAX_BYTES_TO_INSPECT);\n        },\n    });\n    if (typeof bodyCopy?.destroy === \"function\") {\n        bodyCopy.destroy();\n    }\n    const bodyStringTail = config.utf8Encoder(bodyBytes.subarray(bodyBytes.length - 16));\n    if (bodyBytes.length === 0 && THROW_IF_EMPTY_BODY[context.commandName]) {\n        const err = new Error(\"S3 aborted request\");\n        err.name = \"InternalError\";\n        throw err;\n    }\n    if (bodyStringTail && bodyStringTail.endsWith(\"</Error>\")) {\n        response.statusCode = 400;\n    }\n    return result;\n};\nconst collectBody = (streamBody = new Uint8Array(), context) => {\n    if (streamBody instanceof Uint8Array) {\n        return Promise.resolve(streamBody);\n    }\n    return context.streamCollector(streamBody) || Promise.resolve(new Uint8Array());\n};\nconst throw200ExceptionsMiddlewareOptions = {\n    relation: \"after\",\n    toMiddleware: \"deserializerMiddleware\",\n    tags: [\"THROW_200_EXCEPTIONS\", \"S3\"],\n    name: \"throw200ExceptionsMiddleware\",\n    override: true,\n};\nconst getThrow200ExceptionsPlugin = (config) => ({\n    applyToStack: (clientStack) => {\n        clientStack.addRelativeTo(throw200ExceptionsMiddleware(config), throw200ExceptionsMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/throw-200-exceptions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/validate-bucket-name.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/validate-bucket-name.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getValidateBucketNamePlugin: () => (/* binding */ getValidateBucketNamePlugin),\n/* harmony export */   validateBucketNameMiddleware: () => (/* binding */ validateBucketNameMiddleware),\n/* harmony export */   validateBucketNameMiddlewareOptions: () => (/* binding */ validateBucketNameMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_util_arn_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/util-arn-parser */ \"(ssr)/./node_modules/@aws-sdk/util-arn-parser/dist-es/index.js\");\n/* harmony import */ var _bucket_endpoint_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./bucket-endpoint-middleware */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/bucket-endpoint-middleware.js\");\n\n\nfunction validateBucketNameMiddleware({ bucketEndpoint }) {\n    return (next) => async (args) => {\n        const { input: { Bucket }, } = args;\n        if (!bucketEndpoint && typeof Bucket === \"string\" && !(0,_aws_sdk_util_arn_parser__WEBPACK_IMPORTED_MODULE_0__.validate)(Bucket) && Bucket.indexOf(\"/\") >= 0) {\n            const err = new Error(`Bucket name shouldn't contain '/', received '${Bucket}'`);\n            err.name = \"InvalidBucketName\";\n            throw err;\n        }\n        return next({ ...args });\n    };\n}\nconst validateBucketNameMiddlewareOptions = {\n    step: \"initialize\",\n    tags: [\"VALIDATE_BUCKET_NAME\"],\n    name: \"validateBucketNameMiddleware\",\n    override: true,\n};\nconst getValidateBucketNamePlugin = (options) => ({\n    applyToStack: (clientStack) => {\n        clientStack.add(validateBucketNameMiddleware(options), validateBucketNameMiddlewareOptions);\n        clientStack.addRelativeTo((0,_bucket_endpoint_middleware__WEBPACK_IMPORTED_MODULE_1__.bucketEndpointMiddleware)(options), _bucket_endpoint_middleware__WEBPACK_IMPORTED_MODULE_1__.bucketEndpointMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/validate-bucket-name.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/s3-request-presigner/dist-es/constants.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@aws-sdk/s3-request-presigner/dist-es/constants.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALGORITHM_IDENTIFIER: () => (/* binding */ ALGORITHM_IDENTIFIER),\n/* harmony export */   ALGORITHM_QUERY_PARAM: () => (/* binding */ ALGORITHM_QUERY_PARAM),\n/* harmony export */   AMZ_DATE_QUERY_PARAM: () => (/* binding */ AMZ_DATE_QUERY_PARAM),\n/* harmony export */   CREDENTIAL_QUERY_PARAM: () => (/* binding */ CREDENTIAL_QUERY_PARAM),\n/* harmony export */   EXPIRES_QUERY_PARAM: () => (/* binding */ EXPIRES_QUERY_PARAM),\n/* harmony export */   HOST_HEADER: () => (/* binding */ HOST_HEADER),\n/* harmony export */   SHA256_HEADER: () => (/* binding */ SHA256_HEADER),\n/* harmony export */   SIGNED_HEADERS_QUERY_PARAM: () => (/* binding */ SIGNED_HEADERS_QUERY_PARAM),\n/* harmony export */   UNSIGNED_PAYLOAD: () => (/* binding */ UNSIGNED_PAYLOAD)\n/* harmony export */ });\nconst UNSIGNED_PAYLOAD = \"UNSIGNED-PAYLOAD\";\nconst SHA256_HEADER = \"X-Amz-Content-Sha256\";\nconst ALGORITHM_QUERY_PARAM = \"X-Amz-Algorithm\";\nconst CREDENTIAL_QUERY_PARAM = \"X-Amz-Credential\";\nconst AMZ_DATE_QUERY_PARAM = \"X-Amz-Date\";\nconst SIGNED_HEADERS_QUERY_PARAM = \"X-Amz-SignedHeaders\";\nconst EXPIRES_QUERY_PARAM = \"X-Amz-Expires\";\nconst HOST_HEADER = \"host\";\nconst ALGORITHM_IDENTIFIER = \"AWS4-HMAC-SHA256\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1zZGsvczMtcmVxdWVzdC1wcmVzaWduZXIvZGlzdC1lcy9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL25vZGVfbW9kdWxlcy9AYXdzLXNkay9zMy1yZXF1ZXN0LXByZXNpZ25lci9kaXN0LWVzL2NvbnN0YW50cy5qcz85YTgyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBVTlNJR05FRF9QQVlMT0FEID0gXCJVTlNJR05FRC1QQVlMT0FEXCI7XG5leHBvcnQgY29uc3QgU0hBMjU2X0hFQURFUiA9IFwiWC1BbXotQ29udGVudC1TaGEyNTZcIjtcbmV4cG9ydCBjb25zdCBBTEdPUklUSE1fUVVFUllfUEFSQU0gPSBcIlgtQW16LUFsZ29yaXRobVwiO1xuZXhwb3J0IGNvbnN0IENSRURFTlRJQUxfUVVFUllfUEFSQU0gPSBcIlgtQW16LUNyZWRlbnRpYWxcIjtcbmV4cG9ydCBjb25zdCBBTVpfREFURV9RVUVSWV9QQVJBTSA9IFwiWC1BbXotRGF0ZVwiO1xuZXhwb3J0IGNvbnN0IFNJR05FRF9IRUFERVJTX1FVRVJZX1BBUkFNID0gXCJYLUFtei1TaWduZWRIZWFkZXJzXCI7XG5leHBvcnQgY29uc3QgRVhQSVJFU19RVUVSWV9QQVJBTSA9IFwiWC1BbXotRXhwaXJlc1wiO1xuZXhwb3J0IGNvbnN0IEhPU1RfSEVBREVSID0gXCJob3N0XCI7XG5leHBvcnQgY29uc3QgQUxHT1JJVEhNX0lERU5USUZJRVIgPSBcIkFXUzQtSE1BQy1TSEEyNTZcIjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/s3-request-presigner/dist-es/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/s3-request-presigner/dist-es/getSignedUrl.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@aws-sdk/s3-request-presigner/dist-es/getSignedUrl.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSignedUrl: () => (/* binding */ getSignedUrl)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_util_format_url__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/util-format-url */ \"(ssr)/./node_modules/@aws-sdk/util-format-url/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(ssr)/./node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/protocol-http */ \"(ssr)/./node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _presigner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./presigner */ \"(ssr)/./node_modules/@aws-sdk/s3-request-presigner/dist-es/presigner.js\");\n\n\n\n\nconst getSignedUrl = async (client, command, options = {}) => {\n    let s3Presigner;\n    let region;\n    if (typeof client.config.endpointProvider === \"function\") {\n        const endpointV2 = await (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_1__.getEndpointFromInstructions)(command.input, command.constructor, client.config);\n        const authScheme = endpointV2.properties?.authSchemes?.[0];\n        if (authScheme?.name === \"sigv4a\") {\n            region = authScheme?.signingRegionSet?.join(\",\");\n        }\n        else {\n            region = authScheme?.signingRegion;\n        }\n        s3Presigner = new _presigner__WEBPACK_IMPORTED_MODULE_3__.S3RequestPresigner({\n            ...client.config,\n            signingName: authScheme?.signingName,\n            region: async () => region,\n        });\n    }\n    else {\n        s3Presigner = new _presigner__WEBPACK_IMPORTED_MODULE_3__.S3RequestPresigner(client.config);\n    }\n    const presignInterceptMiddleware = (next, context) => async (args) => {\n        const { request } = args;\n        if (!_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_2__.HttpRequest.isInstance(request)) {\n            throw new Error(\"Request to be presigned is not an valid HTTP request.\");\n        }\n        delete request.headers[\"amz-sdk-invocation-id\"];\n        delete request.headers[\"amz-sdk-request\"];\n        delete request.headers[\"x-amz-user-agent\"];\n        let presigned;\n        const presignerOptions = {\n            ...options,\n            signingRegion: options.signingRegion ?? context[\"signing_region\"] ?? region,\n            signingService: options.signingService ?? context[\"signing_service\"],\n        };\n        if (context.s3ExpressIdentity) {\n            presigned = await s3Presigner.presignWithCredentials(request, context.s3ExpressIdentity, presignerOptions);\n        }\n        else {\n            presigned = await s3Presigner.presign(request, presignerOptions);\n        }\n        return {\n            response: {},\n            output: {\n                $metadata: { httpStatusCode: 200 },\n                presigned,\n            },\n        };\n    };\n    const middlewareName = \"presignInterceptMiddleware\";\n    const clientStack = client.middlewareStack.clone();\n    clientStack.addRelativeTo(presignInterceptMiddleware, {\n        name: middlewareName,\n        relation: \"before\",\n        toMiddleware: \"awsAuthMiddleware\",\n        override: true,\n    });\n    const handler = command.resolveMiddleware(clientStack, client.config, {});\n    const { output } = await handler({ input: command.input });\n    const { presigned } = output;\n    return (0,_aws_sdk_util_format_url__WEBPACK_IMPORTED_MODULE_0__.formatUrl)(presigned);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/s3-request-presigner/dist-es/getSignedUrl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/s3-request-presigner/dist-es/index.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@aws-sdk/s3-request-presigner/dist-es/index.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   S3RequestPresigner: () => (/* reexport safe */ _presigner__WEBPACK_IMPORTED_MODULE_1__.S3RequestPresigner),\n/* harmony export */   getSignedUrl: () => (/* reexport safe */ _getSignedUrl__WEBPACK_IMPORTED_MODULE_0__.getSignedUrl)\n/* harmony export */ });\n/* harmony import */ var _getSignedUrl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getSignedUrl */ \"(ssr)/./node_modules/@aws-sdk/s3-request-presigner/dist-es/getSignedUrl.js\");\n/* harmony import */ var _presigner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./presigner */ \"(ssr)/./node_modules/@aws-sdk/s3-request-presigner/dist-es/presigner.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1zZGsvczMtcmVxdWVzdC1wcmVzaWduZXIvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQStCO0FBQ0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3MzLXJlcXVlc3QtcHJlc2lnbmVyL2Rpc3QtZXMvaW5kZXguanM/ZjVjOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9nZXRTaWduZWRVcmxcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3ByZXNpZ25lclwiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/s3-request-presigner/dist-es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/s3-request-presigner/dist-es/presigner.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@aws-sdk/s3-request-presigner/dist-es/presigner.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   S3RequestPresigner: () => (/* binding */ S3RequestPresigner)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_signature_v4_multi_region__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/signature-v4-multi-region */ \"(ssr)/./node_modules/@aws-sdk/signature-v4-multi-region/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/@aws-sdk/s3-request-presigner/dist-es/constants.js\");\n\n\nclass S3RequestPresigner {\n    signer;\n    constructor(options) {\n        const resolvedOptions = {\n            service: options.signingName || options.service || \"s3\",\n            uriEscapePath: options.uriEscapePath || false,\n            applyChecksum: options.applyChecksum || false,\n            ...options,\n        };\n        this.signer = new _aws_sdk_signature_v4_multi_region__WEBPACK_IMPORTED_MODULE_0__.SignatureV4MultiRegion(resolvedOptions);\n    }\n    presign(requestToSign, { unsignableHeaders = new Set(), hoistableHeaders = new Set(), unhoistableHeaders = new Set(), ...options } = {}) {\n        this.prepareRequest(requestToSign, {\n            unsignableHeaders,\n            unhoistableHeaders,\n            hoistableHeaders,\n        });\n        return this.signer.presign(requestToSign, {\n            expiresIn: 900,\n            unsignableHeaders,\n            unhoistableHeaders,\n            ...options,\n        });\n    }\n    presignWithCredentials(requestToSign, credentials, { unsignableHeaders = new Set(), hoistableHeaders = new Set(), unhoistableHeaders = new Set(), ...options } = {}) {\n        this.prepareRequest(requestToSign, {\n            unsignableHeaders,\n            unhoistableHeaders,\n            hoistableHeaders,\n        });\n        return this.signer.presignWithCredentials(requestToSign, credentials, {\n            expiresIn: 900,\n            unsignableHeaders,\n            unhoistableHeaders,\n            ...options,\n        });\n    }\n    prepareRequest(requestToSign, { unsignableHeaders = new Set(), unhoistableHeaders = new Set(), hoistableHeaders = new Set(), } = {}) {\n        unsignableHeaders.add(\"content-type\");\n        Object.keys(requestToSign.headers)\n            .map((header) => header.toLowerCase())\n            .filter((header) => header.startsWith(\"x-amz-server-side-encryption\"))\n            .forEach((header) => {\n            if (!hoistableHeaders.has(header)) {\n                unhoistableHeaders.add(header);\n            }\n        });\n        requestToSign.headers[_constants__WEBPACK_IMPORTED_MODULE_1__.SHA256_HEADER] = _constants__WEBPACK_IMPORTED_MODULE_1__.UNSIGNED_PAYLOAD;\n        const currentHostHeader = requestToSign.headers.host;\n        const port = requestToSign.port;\n        const expectedHostHeader = `${requestToSign.hostname}${requestToSign.port != null ? \":\" + port : \"\"}`;\n        if (!currentHostHeader || (currentHostHeader === requestToSign.hostname && requestToSign.port != null)) {\n            requestToSign.headers.host = expectedHostHeader;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/s3-request-presigner/dist-es/presigner.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/signature-v4-multi-region/dist-es/SignatureV4MultiRegion.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@aws-sdk/signature-v4-multi-region/dist-es/SignatureV4MultiRegion.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignatureV4MultiRegion: () => (/* binding */ SignatureV4MultiRegion)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_middleware_sdk_s3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/middleware-sdk-s3 */ \"(ssr)/./node_modules/@aws-sdk/middleware-sdk-s3/dist-es/index.js\");\n/* harmony import */ var _smithy_signature_v4__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/signature-v4 */ \"(ssr)/./node_modules/@smithy/signature-v4/dist-es/index.js\");\n/* harmony import */ var _signature_v4_crt_container__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./signature-v4-crt-container */ \"(ssr)/./node_modules/@aws-sdk/signature-v4-multi-region/dist-es/signature-v4-crt-container.js\");\n\n\n\nclass SignatureV4MultiRegion {\n    sigv4aSigner;\n    sigv4Signer;\n    signerOptions;\n    static sigv4aDependency() {\n        if (typeof _signature_v4_crt_container__WEBPACK_IMPORTED_MODULE_2__.signatureV4CrtContainer.CrtSignerV4 === \"function\") {\n            return \"crt\";\n        }\n        else if (typeof _smithy_signature_v4__WEBPACK_IMPORTED_MODULE_1__.signatureV4aContainer.SignatureV4a === \"function\") {\n            return \"js\";\n        }\n        return \"none\";\n    }\n    constructor(options) {\n        this.sigv4Signer = new _aws_sdk_middleware_sdk_s3__WEBPACK_IMPORTED_MODULE_0__.SignatureV4S3Express(options);\n        this.signerOptions = options;\n    }\n    async sign(requestToSign, options = {}) {\n        if (options.signingRegion === \"*\") {\n            return this.getSigv4aSigner().sign(requestToSign, options);\n        }\n        return this.sigv4Signer.sign(requestToSign, options);\n    }\n    async signWithCredentials(requestToSign, credentials, options = {}) {\n        if (options.signingRegion === \"*\") {\n            const signer = this.getSigv4aSigner();\n            const CrtSignerV4 = _signature_v4_crt_container__WEBPACK_IMPORTED_MODULE_2__.signatureV4CrtContainer.CrtSignerV4;\n            if (CrtSignerV4 && signer instanceof CrtSignerV4) {\n                return signer.signWithCredentials(requestToSign, credentials, options);\n            }\n            else {\n                throw new Error(`signWithCredentials with signingRegion '*' is only supported when using the CRT dependency @aws-sdk/signature-v4-crt. ` +\n                    `Please check whether you have installed the \"@aws-sdk/signature-v4-crt\" package explicitly. ` +\n                    `You must also register the package by calling [require(\"@aws-sdk/signature-v4-crt\");] ` +\n                    `or an ESM equivalent such as [import \"@aws-sdk/signature-v4-crt\";]. ` +\n                    `For more information please go to https://github.com/aws/aws-sdk-js-v3#functionality-requiring-aws-common-runtime-crt`);\n            }\n        }\n        return this.sigv4Signer.signWithCredentials(requestToSign, credentials, options);\n    }\n    async presign(originalRequest, options = {}) {\n        if (options.signingRegion === \"*\") {\n            const signer = this.getSigv4aSigner();\n            const CrtSignerV4 = _signature_v4_crt_container__WEBPACK_IMPORTED_MODULE_2__.signatureV4CrtContainer.CrtSignerV4;\n            if (CrtSignerV4 && signer instanceof CrtSignerV4) {\n                return signer.presign(originalRequest, options);\n            }\n            else {\n                throw new Error(`presign with signingRegion '*' is only supported when using the CRT dependency @aws-sdk/signature-v4-crt. ` +\n                    `Please check whether you have installed the \"@aws-sdk/signature-v4-crt\" package explicitly. ` +\n                    `You must also register the package by calling [require(\"@aws-sdk/signature-v4-crt\");] ` +\n                    `or an ESM equivalent such as [import \"@aws-sdk/signature-v4-crt\";]. ` +\n                    `For more information please go to https://github.com/aws/aws-sdk-js-v3#functionality-requiring-aws-common-runtime-crt`);\n            }\n        }\n        return this.sigv4Signer.presign(originalRequest, options);\n    }\n    async presignWithCredentials(originalRequest, credentials, options = {}) {\n        if (options.signingRegion === \"*\") {\n            throw new Error(\"Method presignWithCredentials is not supported for [signingRegion=*].\");\n        }\n        return this.sigv4Signer.presignWithCredentials(originalRequest, credentials, options);\n    }\n    getSigv4aSigner() {\n        if (!this.sigv4aSigner) {\n            const CrtSignerV4 = _signature_v4_crt_container__WEBPACK_IMPORTED_MODULE_2__.signatureV4CrtContainer.CrtSignerV4;\n            const JsSigV4aSigner = _smithy_signature_v4__WEBPACK_IMPORTED_MODULE_1__.signatureV4aContainer.SignatureV4a;\n            if (this.signerOptions.runtime === \"node\") {\n                if (!CrtSignerV4 && !JsSigV4aSigner) {\n                    throw new Error(\"Neither CRT nor JS SigV4a implementation is available. \" +\n                        \"Please load either @aws-sdk/signature-v4-crt or @aws-sdk/signature-v4a. \" +\n                        \"For more information please go to \" +\n                        \"https://github.com/aws/aws-sdk-js-v3#functionality-requiring-aws-common-runtime-crt\");\n                }\n                if (CrtSignerV4 && typeof CrtSignerV4 === \"function\") {\n                    this.sigv4aSigner = new CrtSignerV4({\n                        ...this.signerOptions,\n                        signingAlgorithm: 1,\n                    });\n                }\n                else if (JsSigV4aSigner && typeof JsSigV4aSigner === \"function\") {\n                    this.sigv4aSigner = new JsSigV4aSigner({\n                        ...this.signerOptions,\n                    });\n                }\n                else {\n                    throw new Error(\"Available SigV4a implementation is not a valid constructor. \" +\n                        \"Please ensure you've properly imported @aws-sdk/signature-v4-crt or @aws-sdk/signature-v4a.\" +\n                        \"For more information please go to \" +\n                        \"https://github.com/aws/aws-sdk-js-v3#functionality-requiring-aws-common-runtime-crt\");\n                }\n            }\n            else {\n                if (!JsSigV4aSigner || typeof JsSigV4aSigner !== \"function\") {\n                    throw new Error(\"JS SigV4a implementation is not available or not a valid constructor. \" +\n                        \"Please check whether you have installed the @aws-sdk/signature-v4a package explicitly. The CRT implementation is not available for browsers. \" +\n                        \"You must also register the package by calling [require('@aws-sdk/signature-v4a');] \" +\n                        \"or an ESM equivalent such as [import '@aws-sdk/signature-v4a';]. \" +\n                        \"For more information please go to \" +\n                        \"https://github.com/aws/aws-sdk-js-v3#using-javascript-non-crt-implementation-of-sigv4a\");\n                }\n                this.sigv4aSigner = new JsSigV4aSigner({\n                    ...this.signerOptions,\n                });\n            }\n        }\n        return this.sigv4aSigner;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/signature-v4-multi-region/dist-es/SignatureV4MultiRegion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/signature-v4-multi-region/dist-es/index.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@aws-sdk/signature-v4-multi-region/dist-es/index.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignatureV4MultiRegion: () => (/* reexport safe */ _SignatureV4MultiRegion__WEBPACK_IMPORTED_MODULE_0__.SignatureV4MultiRegion),\n/* harmony export */   signatureV4CrtContainer: () => (/* reexport safe */ _signature_v4_crt_container__WEBPACK_IMPORTED_MODULE_1__.signatureV4CrtContainer)\n/* harmony export */ });\n/* harmony import */ var _SignatureV4MultiRegion__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SignatureV4MultiRegion */ \"(ssr)/./node_modules/@aws-sdk/signature-v4-multi-region/dist-es/SignatureV4MultiRegion.js\");\n/* harmony import */ var _signature_v4_crt_container__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./signature-v4-crt-container */ \"(ssr)/./node_modules/@aws-sdk/signature-v4-multi-region/dist-es/signature-v4-crt-container.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1zZGsvc2lnbmF0dXJlLXY0LW11bHRpLXJlZ2lvbi9kaXN0LWVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBeUM7QUFDSSIsInNvdXJjZXMiOlsid2VicGFjazovL2hlcmJhbGljaW91cy1uZXh0anMvLi9ub2RlX21vZHVsZXMvQGF3cy1zZGsvc2lnbmF0dXJlLXY0LW11bHRpLXJlZ2lvbi9kaXN0LWVzL2luZGV4LmpzPzAzNDUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vU2lnbmF0dXJlVjRNdWx0aVJlZ2lvblwiO1xuZXhwb3J0ICogZnJvbSBcIi4vc2lnbmF0dXJlLXY0LWNydC1jb250YWluZXJcIjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/signature-v4-multi-region/dist-es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/signature-v4-multi-region/dist-es/signature-v4-crt-container.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/@aws-sdk/signature-v4-multi-region/dist-es/signature-v4-crt-container.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   signatureV4CrtContainer: () => (/* binding */ signatureV4CrtContainer)\n/* harmony export */ });\nconst signatureV4CrtContainer = {\n    CrtSignerV4: null,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1zZGsvc2lnbmF0dXJlLXY0LW11bHRpLXJlZ2lvbi9kaXN0LWVzL3NpZ25hdHVyZS12NC1jcnQtY29udGFpbmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3NpZ25hdHVyZS12NC1tdWx0aS1yZWdpb24vZGlzdC1lcy9zaWduYXR1cmUtdjQtY3J0LWNvbnRhaW5lci5qcz83MzA0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBzaWduYXR1cmVWNENydENvbnRhaW5lciA9IHtcbiAgICBDcnRTaWduZXJWNDogbnVsbCxcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/signature-v4-multi-region/dist-es/signature-v4-crt-container.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/util-arn-parser/dist-es/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/@aws-sdk/util-arn-parser/dist-es/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   build: () => (/* binding */ build),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   validate: () => (/* binding */ validate)\n/* harmony export */ });\nconst validate = (str) => typeof str === \"string\" && str.indexOf(\"arn:\") === 0 && str.split(\":\").length >= 6;\nconst parse = (arn) => {\n    const segments = arn.split(\":\");\n    if (segments.length < 6 || segments[0] !== \"arn\")\n        throw new Error(\"Malformed ARN\");\n    const [, partition, service, region, accountId, ...resource] = segments;\n    return {\n        partition,\n        service,\n        region,\n        accountId,\n        resource: resource.join(\":\"),\n    };\n};\nconst build = (arnObject) => {\n    const { partition = \"aws\", service, region, accountId, resource } = arnObject;\n    if ([service, region, accountId, resource].some((segment) => typeof segment !== \"string\")) {\n        throw new Error(\"Input ARN object is invalid\");\n    }\n    return `arn:${partition}:${service}:${region}:${accountId}:${resource}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1hcm4tcGFyc2VyL2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU87QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsWUFBWSwwREFBMEQ7QUFDdEU7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLFVBQVUsR0FBRyxRQUFRLEdBQUcsT0FBTyxHQUFHLFVBQVUsR0FBRyxTQUFTO0FBQzFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL25vZGVfbW9kdWxlcy9AYXdzLXNkay91dGlsLWFybi1wYXJzZXIvZGlzdC1lcy9pbmRleC5qcz8yNjljIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCB2YWxpZGF0ZSA9IChzdHIpID0+IHR5cGVvZiBzdHIgPT09IFwic3RyaW5nXCIgJiYgc3RyLmluZGV4T2YoXCJhcm46XCIpID09PSAwICYmIHN0ci5zcGxpdChcIjpcIikubGVuZ3RoID49IDY7XG5leHBvcnQgY29uc3QgcGFyc2UgPSAoYXJuKSA9PiB7XG4gICAgY29uc3Qgc2VnbWVudHMgPSBhcm4uc3BsaXQoXCI6XCIpO1xuICAgIGlmIChzZWdtZW50cy5sZW5ndGggPCA2IHx8IHNlZ21lbnRzWzBdICE9PSBcImFyblwiKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJNYWxmb3JtZWQgQVJOXCIpO1xuICAgIGNvbnN0IFssIHBhcnRpdGlvbiwgc2VydmljZSwgcmVnaW9uLCBhY2NvdW50SWQsIC4uLnJlc291cmNlXSA9IHNlZ21lbnRzO1xuICAgIHJldHVybiB7XG4gICAgICAgIHBhcnRpdGlvbixcbiAgICAgICAgc2VydmljZSxcbiAgICAgICAgcmVnaW9uLFxuICAgICAgICBhY2NvdW50SWQsXG4gICAgICAgIHJlc291cmNlOiByZXNvdXJjZS5qb2luKFwiOlwiKSxcbiAgICB9O1xufTtcbmV4cG9ydCBjb25zdCBidWlsZCA9IChhcm5PYmplY3QpID0+IHtcbiAgICBjb25zdCB7IHBhcnRpdGlvbiA9IFwiYXdzXCIsIHNlcnZpY2UsIHJlZ2lvbiwgYWNjb3VudElkLCByZXNvdXJjZSB9ID0gYXJuT2JqZWN0O1xuICAgIGlmIChbc2VydmljZSwgcmVnaW9uLCBhY2NvdW50SWQsIHJlc291cmNlXS5zb21lKChzZWdtZW50KSA9PiB0eXBlb2Ygc2VnbWVudCAhPT0gXCJzdHJpbmdcIikpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiSW5wdXQgQVJOIG9iamVjdCBpcyBpbnZhbGlkXCIpO1xuICAgIH1cbiAgICByZXR1cm4gYGFybjoke3BhcnRpdGlvbn06JHtzZXJ2aWNlfToke3JlZ2lvbn06JHthY2NvdW50SWR9OiR7cmVzb3VyY2V9YDtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/util-arn-parser/dist-es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@aws-sdk/util-format-url/dist-es/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/@aws-sdk/util-format-url/dist-es/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatUrl: () => (/* binding */ formatUrl)\n/* harmony export */ });\n/* harmony import */ var _smithy_querystring_builder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/querystring-builder */ \"(ssr)/./node_modules/@smithy/querystring-builder/dist-es/index.js\");\n\nfunction formatUrl(request) {\n    const { port, query } = request;\n    let { protocol, path, hostname } = request;\n    if (protocol && protocol.slice(-1) !== \":\") {\n        protocol += \":\";\n    }\n    if (port) {\n        hostname += `:${port}`;\n    }\n    if (path && path.charAt(0) !== \"/\") {\n        path = `/${path}`;\n    }\n    let queryString = query ? (0,_smithy_querystring_builder__WEBPACK_IMPORTED_MODULE_0__.buildQueryString)(query) : \"\";\n    if (queryString && queryString[0] !== \"?\") {\n        queryString = `?${queryString}`;\n    }\n    let auth = \"\";\n    if (request.username != null || request.password != null) {\n        const username = request.username ?? \"\";\n        const password = request.password ?? \"\";\n        auth = `${username}:${password}@`;\n    }\n    let fragment = \"\";\n    if (request.fragment) {\n        fragment = `#${request.fragment}`;\n    }\n    return `${protocol}//${auth}${hostname}${path}${queryString}${fragment}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@aws-sdk/util-format-url/dist-es/index.js\n");

/***/ })

};
;