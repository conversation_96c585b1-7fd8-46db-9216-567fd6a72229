const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Define the 13 herbal categories based on the provided list
const herbalCategories = [
  {
    name: "Cleansers & Face Wash",
    slug: "cleansers-face-wash",
    description: "Gentle cleansers and face washes for daily skincare routine",
    isActive: true
  },
  {
    name: "Scrubs & Exfoliants",
    slug: "scrubs-exfoliants",
    description: "Deep cleansing scrubs and exfoliants for smooth skin",
    isActive: true
  },
  {
    name: "Ubtan & Masks",
    slug: "ubtan-masks",
    description: "Traditional ubtans and nourishing face masks",
    isActive: true
  },
  {
    name: "Creams & Moisturizers",
    slug: "creams-moisturizers",
    description: "Hydrating creams and moisturizers for all skin types",
    isActive: true
  },
  {
    name: "Gels & Serums",
    slug: "gels-serums",
    description: "Concentrated gels and serums for targeted skincare",
    isActive: true
  },
  {
    name: "Facial Oils & Elixirs",
    slug: "facial-oils-elixirs",
    description: "Nourishing facial oils and elixirs for radiant skin",
    isActive: true
  },
  {
    name: "Toners",
    slug: "toners",
    description: "Refreshing toners to balance and prep skin",
    isActive: true
  },
  {
    name: "Facial Kits",
    slug: "facial-kits",
    description: "Complete facial treatment kits for spa-like experience",
    isActive: true
  },
  {
    name: "Hair Oils",
    slug: "hair-oils",
    description: "Natural hair oils for healthy hair and scalp",
    isActive: true
  },
  {
    name: "Hair Masks",
    slug: "hair-masks",
    description: "Deep conditioning hair masks for damaged hair",
    isActive: true
  },
  {
    name: "Eye & Lip Care",
    slug: "eye-lip-care",
    description: "Specialized care for delicate eye and lip areas",
    isActive: true
  },
  {
    name: "Massage & Body Oils",
    slug: "massage-body-oils",
    description: "Luxurious massage and body oils for relaxation",
    isActive: true
  },
  {
    name: "Combo & Complete Care",
    slug: "combo-complete-care",
    description: "Complete skincare combinations and sets",
    isActive: true
  }
];

async function createHerbalCategories() {
  console.log('🌿 Creating 13 herbal categories...');
  
  try {
    // First, remove existing categories to avoid conflicts
    console.log('🗑️  Removing existing categories...');
    await prisma.category.deleteMany({});
    
    // Create all 13 herbal categories
    const createdCategories = [];
    
    for (const category of herbalCategories) {
      const createdCategory = await prisma.category.create({
        data: category
      });
      createdCategories.push(createdCategory);
      console.log(`✅ Created: ${createdCategory.name}`);
    }
    
    console.log(`\n🎉 Successfully created ${createdCategories.length} herbal categories:`);
    createdCategories.forEach(cat => {
      console.log(`   • ${cat.name} (${cat.slug})`);
    });
    
    return createdCategories;
    
  } catch (error) {
    console.error('❌ Error creating categories:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if executed directly
if (require.main === module) {
  createHerbalCategories()
    .then(() => {
      console.log('\n✨ All herbal categories created successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Failed to create categories:', error);
      process.exit(1);
    });
}

module.exports = { createHerbalCategories, herbalCategories };