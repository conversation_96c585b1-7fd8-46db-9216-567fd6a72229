'use client'

import React, { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';
import { CartItem, Product, AppliedCoupon, CartCouponState } from '../types';

interface CartState {
  items: CartItem[];
  total: number;
  itemCount: number;
  subtotal: number;
  coupons: CartCouponState;
  finalTotal: number;
}

type CartAction =
  | { type: 'ADD_ITEM'; payload: Product; selectedVariants?: Array<{id: string; name: string; value: string; price?: number}> }
  | { type: 'REMOVE_ITEM'; payload: string }
  | { type: 'UPDATE_QUANTITY'; payload: { id: string; quantity: number } }
  | { type: 'CLEAR_CART' }
  | { type: 'APPLY_COUPON'; payload: AppliedCoupon }
  | { type: 'REMOVE_COUPON'; payload: string }
  | { type: 'CLEAR_COUPONS' };

const CartContext = createContext<{
  state: CartState;
  dispatch: React.Dispatch<CartAction>;
} | null>(null);

// localStorage utilities
const CART_STORAGE_KEY = 'herbalicious_cart';

const saveCartToStorage = (state: CartState) => {
  try {
    if (typeof window !== 'undefined') {
      localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(state));
    }
  } catch (error) {
    console.error('Error saving cart to localStorage:', error);
  }
};

const loadCartFromStorage = (): CartState | null => {
  try {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(CART_STORAGE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    }
  } catch (error) {
    console.error('Error loading cart from localStorage:', error);
  }
  return null;
};

// Helper function to generate unique variant key
const generateVariantKey = (productId: string, selectedVariants?: Array<{id: string; name: string; value: string; price?: number}>) => {
  if (!selectedVariants || selectedVariants.length === 0) {
    return productId;
  }
  
  // Sort variants by name to ensure consistent key generation
  const sortedVariants = [...selectedVariants].sort((a, b) => a.name.localeCompare(b.name));
  const variantString = sortedVariants.map(v => `${v.name}:${v.value}`).join('|');
  return `${productId}__${variantString}`;
};

// Helper function to get item identifier (with fallback for backward compatibility)
const getItemIdentifier = (item: any) => {
  return item.variantKey || item.product?.id || item.id;
};

const getInitialCartState = (): CartState => {
  const storedCart = loadCartFromStorage();
  if (storedCart) {
    return storedCart;
  }
  
  return {
    items: [],
    total: 0,
    subtotal: 0,
    itemCount: 0,
    finalTotal: 0,
    coupons: {
      appliedCoupons: [],
      totalDiscount: 0,
      availableCoupons: []
    }
  };
};

const calculateTotals = (items: CartItem[], appliedCoupons: AppliedCoupon[]) => {
  const subtotal = items.reduce((sum, item) => sum + item.product.price * item.quantity, 0);
  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);
  const totalDiscount = appliedCoupons.reduce((sum, coupon) => sum + coupon.discountAmount, 0);
  const finalTotal = subtotal - totalDiscount;

  return {
    subtotal,
    itemCount,
    total: subtotal, // Keep for backward compatibility
    finalTotal,
    totalDiscount
  };
};

const cartReducer = (state: CartState, action: CartAction): CartState => {
  let newState: CartState;
  
  switch (action.type) {
    case 'ADD_ITEM': {
      const variantKey = generateVariantKey(action.payload.id, action.selectedVariants);
      const existingItem = state.items.find(item => getItemIdentifier(item) === variantKey);
      
      let updatedItems: CartItem[];
      if (existingItem) {
        // Same product with same variants - increase quantity
        updatedItems = state.items.map(item =>
          getItemIdentifier(item) === variantKey
            ? { ...item, quantity: item.quantity + 1, variantKey }
            : item
        );
      } else {
        // New product or different variant combination - add as new item
        const newCartItem: CartItem = {
          product: action.payload,
          quantity: 1,
          selectedVariants: action.selectedVariants || [],
          variantKey
        };
        updatedItems = [...state.items, newCartItem];
      }

      const totals = calculateTotals(updatedItems, state.coupons.appliedCoupons);
      
      newState = {
        ...state,
        items: updatedItems,
        ...totals,
        coupons: {
          ...state.coupons,
          totalDiscount: totals.totalDiscount
        }
      };
      break;
    }
    
    case 'REMOVE_ITEM': {
      const filteredItems = state.items.filter(item => getItemIdentifier(item) !== action.payload);
      const totals = calculateTotals(filteredItems, state.coupons.appliedCoupons);
      
      newState = {
        ...state,
        items: filteredItems,
        ...totals,
        coupons: {
          ...state.coupons,
          totalDiscount: totals.totalDiscount
        }
      };
      break;
    }
    
    case 'UPDATE_QUANTITY': {
      const updatedItems = state.items.map(item =>
        getItemIdentifier(item) === action.payload.id
          ? { ...item, quantity: action.payload.quantity }
          : item
      ).filter(item => item.quantity > 0);
      
      const totals = calculateTotals(updatedItems, state.coupons.appliedCoupons);
      
      newState = {
        ...state,
        items: updatedItems,
        ...totals,
        coupons: {
          ...state.coupons,
          totalDiscount: totals.totalDiscount
        }
      };
      break;
    }

    case 'APPLY_COUPON': {
      // Check if coupon is already applied
      const isAlreadyApplied = state.coupons.appliedCoupons.some(
        coupon => coupon.coupon.id === action.payload.coupon.id
      );

      if (isAlreadyApplied) {
        return state;
      }

      // Check stacking rules
      const hasNonStackableCoupon = state.coupons.appliedCoupons.some(
        coupon => !coupon.coupon.isStackable
      );

      if (hasNonStackableCoupon && !action.payload.coupon.isStackable) {
        return state;
      }

      const updatedAppliedCoupons = [...state.coupons.appliedCoupons, action.payload];
      const totals = calculateTotals(state.items, updatedAppliedCoupons);

      newState = {
        ...state,
        ...totals,
        coupons: {
          ...state.coupons,
          appliedCoupons: updatedAppliedCoupons,
          totalDiscount: totals.totalDiscount
        }
      };
      break;
    }

    case 'REMOVE_COUPON': {
      const updatedAppliedCoupons = state.coupons.appliedCoupons.filter(
        coupon => coupon.coupon.id !== action.payload
      );
      const totals = calculateTotals(state.items, updatedAppliedCoupons);

      newState = {
        ...state,
        ...totals,
        coupons: {
          ...state.coupons,
          appliedCoupons: updatedAppliedCoupons,
          totalDiscount: totals.totalDiscount
        }
      };
      break;
    }

    case 'CLEAR_COUPONS': {
      const totals = calculateTotals(state.items, []);

      newState = {
        ...state,
        ...totals,
        coupons: {
          appliedCoupons: [],
          totalDiscount: 0,
          availableCoupons: []
        }
      };
      break;
    }
    
    case 'CLEAR_CART': {
      newState = {
        items: [],
        total: 0,
        subtotal: 0,
        itemCount: 0,
        finalTotal: 0,
        coupons: {
          appliedCoupons: [],
          totalDiscount: 0,
          availableCoupons: []
        }
      };
      break;
    }
    
    default:
      return state;
  }

  // Save to localStorage after state change
  saveCartToStorage(newState);
  return newState;
};

export const CartProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(cartReducer, getInitialCartState());

  return (
    <CartContext.Provider value={{ state, dispatch }}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};