'use client'

import React from 'react';
import { Loader2 } from 'lucide-react';

// Simple spinner loader for admin pages
export const AdminSpinner: React.FC<{ size?: 'sm' | 'md' | 'lg'; className?: string }> = ({ 
  size = 'md', 
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  return (
    <Loader2 className={`animate-spin text-green-600 ${sizeClasses[size]} ${className}`} />
  );
};

// Full page loader for admin
export const AdminPageLoader: React.FC<{ message?: string }> = ({ 
  message = 'Loading...' 
}) => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="text-center">
      <AdminSpinner size="lg" className="mx-auto mb-4" />
      <p className="text-gray-600 text-lg">{message}</p>
    </div>
  </div>
);

// Inline loader for admin sections
export const AdminInlineLoader: React.FC<{ message?: string; size?: 'sm' | 'md' | 'lg' }> = ({ 
  message = 'Loading...', 
  size = 'md' 
}) => (
  <div className="flex items-center justify-center py-12">
    <div className="text-center">
      <AdminSpinner size={size} className="mx-auto mb-3" />
      <p className="text-gray-600">{message}</p>
    </div>
  </div>
);

// Table loader for admin
export const AdminTableLoader: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => (
  <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            {Array.from({ length: columns }).map((_, index) => (
              <th key={index} className="px-6 py-3">
                <div className="animate-pulse bg-gray-300 h-4 rounded"></div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {Array.from({ length: rows }).map((_, rowIndex) => (
            <tr key={rowIndex}>
              {Array.from({ length: columns }).map((_, colIndex) => (
                <td key={colIndex} className="px-6 py-4 whitespace-nowrap">
                  <div className="animate-pulse bg-gray-200 h-4 rounded"></div>
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  </div>
);

// Button loader for admin actions
export const AdminButtonLoader: React.FC<{ children: React.ReactNode; loading?: boolean }> = ({ 
  children, 
  loading = false 
}) => (
  <div className="flex items-center space-x-2">
    {loading && <AdminSpinner size="sm" />}
    <span>{children}</span>
  </div>
);

// Card loader for admin dashboard
export const AdminCardLoader: React.FC = () => (
  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div className="animate-pulse">
      <div className="flex items-center justify-between mb-4">
        <div className="bg-gray-300 h-6 w-32 rounded"></div>
        <div className="bg-gray-300 h-8 w-8 rounded"></div>
      </div>
      <div className="bg-gray-200 h-8 w-20 rounded mb-2"></div>
      <div className="bg-gray-200 h-4 w-24 rounded"></div>
    </div>
  </div>
);

// Stats grid loader for admin dashboard
export const AdminStatsLoader: React.FC<{ count?: number }> = ({ count = 4 }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    {Array.from({ length: count }).map((_, index) => (
      <AdminCardLoader key={index} />
    ))}
  </div>
);

// Form loader for admin
export const AdminFormLoader: React.FC = () => (
  <div className="space-y-6">
    <div className="animate-pulse">
      <div className="bg-gray-300 h-6 w-32 rounded mb-2"></div>
      <div className="bg-gray-200 h-10 w-full rounded"></div>
    </div>
    <div className="animate-pulse">
      <div className="bg-gray-300 h-6 w-24 rounded mb-2"></div>
      <div className="bg-gray-200 h-10 w-full rounded"></div>
    </div>
    <div className="animate-pulse">
      <div className="bg-gray-300 h-6 w-28 rounded mb-2"></div>
      <div className="bg-gray-200 h-24 w-full rounded"></div>
    </div>
    <div className="flex justify-end space-x-3">
      <div className="animate-pulse bg-gray-200 h-10 w-20 rounded"></div>
      <div className="animate-pulse bg-gray-300 h-10 w-24 rounded"></div>
    </div>
  </div>
);