import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../lib/auth';
import { prisma } from '../../../lib/db';
import { CouponUpdateInput } from '../../../types';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || (session.user as any).role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const coupon = await prisma.coupon.findUnique({ 
      where: { id: params.id },
      include: {
        usages: {
          include: {
            user: {
              select: { id: true, name: true, email: true }
            },
            order: {
              select: { id: true, orderNumber: true, total: true }
            }
          }
        }
      }
    });

    if (!coupon) {
      return NextResponse.json(
        { error: 'Coupon not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(coupon);
  } catch (error) {
    console.error('Error fetching coupon:', error);
    return NextResponse.json(
      { error: 'Failed to fetch coupon' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || (session.user as any).role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data: CouponUpdateInput = await request.json();

    // Validate required fields
    if (!data.name || !data.type || !data.discountType || data.discountValue === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if coupon exists
    const existingCoupon = await prisma.coupon.findUnique({
      where: { id: params.id }
    });

    if (!existingCoupon) {
      return NextResponse.json(
        { error: 'Coupon not found' },
        { status: 404 }
      );
    }

    // Check if code is being changed and if new code already exists
    if (data.code && data.code !== existingCoupon.code) {
      const codeExists = await prisma.coupon.findUnique({
        where: { code: data.code.toUpperCase() }
      });

      if (codeExists) {
        return NextResponse.json(
          { error: 'Coupon code already exists' },
          { status: 400 }
        );
      }
    }

    const updatedCoupon = await prisma.coupon.update({
      where: { id: params.id },
      data: {
        code: data.code ? data.code.toUpperCase() : existingCoupon.code,
        name: data.name,
        description: data.description,
        type: data.type,
        discountType: data.discountType,
        discountValue: data.discountValue,
        minimumAmount: data.minimumAmount,
        maximumDiscount: data.maximumDiscount,
        usageLimit: data.usageLimit,
        userUsageLimit: data.userUsageLimit,
        isActive: data.isActive ?? existingCoupon.isActive,
        isStackable: data.isStackable ?? existingCoupon.isStackable,
        showInModule: data.showInModule ?? existingCoupon.showInModule,
        validFrom: data.validFrom ? new Date(data.validFrom) : existingCoupon.validFrom,
        validUntil: data.validUntil ? new Date(data.validUntil) : null,
        applicableProducts: data.applicableProducts || existingCoupon.applicableProducts,
        applicableCategories: data.applicableCategories || existingCoupon.applicableCategories,
        excludedProducts: data.excludedProducts || existingCoupon.excludedProducts,
        excludedCategories: data.excludedCategories || existingCoupon.excludedCategories,
        customerSegments: data.customerSegments || existingCoupon.customerSegments
      }
    });

    return NextResponse.json(updatedCoupon);
  } catch (error) {
    console.error('Error updating coupon:', error);
    return NextResponse.json(
      { error: 'Failed to update coupon' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || (session.user as any).role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if coupon exists
    const existingCoupon = await prisma.coupon.findUnique({
      where: { id: params.id },
      include: {
        usages: true
      }
    });

    if (!existingCoupon) {
      return NextResponse.json(
        { error: 'Coupon not found' },
        { status: 404 }
      );
    }

    // Check if coupon has been used
    if (existingCoupon.usages.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete coupon that has been used. Consider deactivating it instead.' },
        { status: 400 }
      );
    }

    await prisma.coupon.delete({
      where: { id: params.id }
    });

    return NextResponse.json({ message: 'Coupon deleted successfully' });
  } catch (error) {
    console.error('Error deleting coupon:', error);
    return NextResponse.json(
      { error: 'Failed to delete coupon' },
      { status: 500 }
    );
  }
}