"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/[id]/variations/route";
exports.ids = ["app/api/products/[id]/variations/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_project_app_api_products_id_variations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/products/[id]/variations/route.ts */ \"(rsc)/./app/api/products/[id]/variations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/[id]/variations/route\",\n        pathname: \"/api/products/[id]/variations\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/[id]/variations/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\api\\\\products\\\\[id]\\\\variations\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_project_app_api_products_id_variations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/products/[id]/variations/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/products/[id]/variations/route.ts":
/*!***************************************************!*\
  !*** ./app/api/products/[id]/variations/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../lib/auth */ \"(rsc)/./app/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../lib/db */ \"(rsc)/./app/lib/db.ts\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../lib/errors */ \"(rsc)/./app/lib/errors.ts\");\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../lib/logger */ \"(rsc)/./app/lib/logger.ts\");\n\n\n\n\n\n\nconst GET = (0,_lib_errors__WEBPACK_IMPORTED_MODULE_4__.asyncHandler)(async (request, { params })=>{\n    const productId = params.id;\n    _lib_logger__WEBPACK_IMPORTED_MODULE_5__.logger.apiRequest(\"GET\", `/api/products/${productId}/variations`);\n    // Fetch all variations for the product\n    const variations = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.productVariant.findMany({\n        where: {\n            productId: productId\n        },\n        orderBy: [\n            {\n                name: \"asc\"\n            },\n            {\n                value: \"asc\"\n            }\n        ]\n    });\n    _lib_logger__WEBPACK_IMPORTED_MODULE_5__.logger.info(\"Product variations fetched\", {\n        productId,\n        count: variations.length\n    });\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        data: variations\n    });\n});\nconst POST = (0,_lib_errors__WEBPACK_IMPORTED_MODULE_4__.asyncHandler)(async (request, { params })=>{\n    const productId = params.id;\n    _lib_logger__WEBPACK_IMPORTED_MODULE_5__.logger.apiRequest(\"POST\", `/api/products/${productId}/variations`);\n    // Check authentication\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n    if (!session?.user) {\n        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_4__.AuthenticationError();\n    }\n    if (session.user.role !== \"ADMIN\") {\n        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_4__.AuthorizationError();\n    }\n    const body = await request.json();\n    const { name, value, price, pricingMode } = body;\n    // Validate required fields\n    if (!name || !value) {\n        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_4__.ValidationError(\"Name and value are required\");\n    }\n    // Validate pricing mode\n    const validPricingModes = [\n        \"REPLACE\",\n        \"INCREMENT\",\n        \"FIXED\"\n    ];\n    const validatedPricingMode = pricingMode && validPricingModes.includes(pricingMode) ? pricingMode : \"REPLACE\";\n    // Check if product exists\n    const product = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.product.findUnique({\n        where: {\n            id: productId\n        }\n    });\n    if (!product) {\n        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_4__.NotFoundError(\"Product\");\n    }\n    // Check if variation with same name and value already exists\n    const existingVariation = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.productVariant.findFirst({\n        where: {\n            productId: productId,\n            name: name,\n            value: value\n        }\n    });\n    if (existingVariation) {\n        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_4__.ConflictError(\"Variation with this name and value already exists\");\n    }\n    // Parse and validate price\n    let parsedPrice = null;\n    if (price !== undefined && price !== null && price !== \"\") {\n        const numPrice = typeof price === \"string\" ? parseFloat(price) : Number(price);\n        if (!isNaN(numPrice) && isFinite(numPrice)) {\n            parsedPrice = numPrice;\n        }\n    }\n    // Create the variation\n    const variation = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.productVariant.create({\n        data: {\n            name: name.trim(),\n            value: value.trim(),\n            price: parsedPrice,\n            pricingMode: validatedPricingMode,\n            productId: productId\n        }\n    });\n    _lib_logger__WEBPACK_IMPORTED_MODULE_5__.logger.info(\"Product variation created\", {\n        productId,\n        variationId: variation.id,\n        name,\n        value,\n        userId: session.user.id\n    });\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        data: variation,\n        message: \"Variation created successfully\"\n    });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/products/[id]/variations/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/auth.ts":
/*!*************************!*\
  !*** ./app/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db */ \"(rsc)/./app/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst authOptions = {\n    // Remove PrismaAdapter when using JWT strategy\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const user = await _db__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user || !user.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2__.compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\",\n        maxAge: 7 * 24 * 60 * 60,\n        updateAge: 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 7 * 24 * 60 * 60\n    },\n    // Enable CSRF protection\n    cookies: {\n        sessionToken: {\n            name:  false ? 0 : `next-auth.session-token`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: \"development\" === \"production\"\n            }\n        },\n        callbackUrl: {\n            name:  false ? 0 : `next-auth.callback-url`,\n            options: {\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: \"development\" === \"production\"\n            }\n        },\n        csrfToken: {\n            name:  false ? 0 : `next-auth.csrf-token`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: \"development\" === \"production\"\n            }\n        }\n    },\n    callbacks: {\n        async jwt ({ token, user, account, profile }) {\n            // Handle initial sign in\n            if (account && user) {\n                // For OAuth providers, create/update user in database\n                if (account.provider === \"google\") {\n                    try {\n                        const email = user.email || profile?.email;\n                        if (!email) {\n                            throw new Error(\"No email found for Google account\");\n                        }\n                        // Check if user exists\n                        let dbUser = await _db__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n                            where: {\n                                email\n                            },\n                            select: {\n                                id: true,\n                                role: true,\n                                name: true,\n                                email: true\n                            }\n                        });\n                        // Create user if doesn't exist\n                        if (!dbUser) {\n                            dbUser = await _db__WEBPACK_IMPORTED_MODULE_3__.prisma.user.create({\n                                data: {\n                                    email,\n                                    name: user.name || profile?.name || email.split(\"@\")[0],\n                                    // Don't save Google profile picture\n                                    role: _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.CUSTOMER,\n                                    emailVerified: new Date()\n                                },\n                                select: {\n                                    id: true,\n                                    role: true,\n                                    name: true,\n                                    email: true\n                                }\n                            });\n                        }\n                        // Set token properties\n                        token.sub = dbUser.id;\n                        token.role = dbUser.role;\n                        token.email = dbUser.email;\n                        token.name = dbUser.name;\n                    } catch (error) {\n                        console.error(\"Error handling Google sign in:\", error);\n                        throw error; // This will prevent sign in\n                    }\n                } else if (user) {\n                    // For credentials provider\n                    token.sub = user.id;\n                    token.role = user.role;\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            // Populate session with user data from token\n            if (token && session.user) {\n                session.user = {\n                    ...session.user,\n                    id: token.sub,\n                    role: token.role,\n                    email: token.email,\n                    name: token.name\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Allows relative callback URLs\n            if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\n            else if (new URL(url).origin === baseUrl) return url;\n            return baseUrl;\n        }\n    },\n    events: {\n        async signIn ({ user, account, profile, isNewUser }) {\n            // Log sign in events in development only\n            if (true) {\n                console.log(\"Sign in event:\", {\n                    userId: user.id,\n                    email: user.email,\n                    provider: account?.provider,\n                    isNewUser\n                });\n            }\n        }\n    },\n    pages: {\n        signIn: \"/login\",\n        signOut: \"/\",\n        error: \"/login\"\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    // Only enable debug in development when explicitly set\n    debug:  true && process.env.NEXTAUTH_DEBUG === \"true\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"error\"\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQ1hGLGdCQUFnQkUsTUFBTSxJQUN0QixJQUFJSCx3REFBWUEsQ0FBQztJQUNmSSxLQUFLO1FBQUM7S0FBUTtBQUNoQixHQUFFO0FBRUosSUFBSUMsSUFBeUIsRUFBY0osZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL2FwcC9saWIvZGIudHM/NThmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz9cbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ2Vycm9yJ10sIC8vIE9ubHkgbG9nIGVycm9ycywgbm8gcXVlcmllcyBvciB3YXJuaW5nc1xuICB9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/errors.ts":
/*!***************************!*\
  !*** ./app/lib/errors.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppError: () => (/* binding */ AppError),\n/* harmony export */   AuthenticationError: () => (/* binding */ AuthenticationError),\n/* harmony export */   AuthorizationError: () => (/* binding */ AuthorizationError),\n/* harmony export */   ConflictError: () => (/* binding */ ConflictError),\n/* harmony export */   DatabaseError: () => (/* binding */ DatabaseError),\n/* harmony export */   ErrorResponses: () => (/* binding */ ErrorResponses),\n/* harmony export */   ExternalServiceError: () => (/* binding */ ExternalServiceError),\n/* harmony export */   NotFoundError: () => (/* binding */ NotFoundError),\n/* harmony export */   RateLimitError: () => (/* binding */ RateLimitError),\n/* harmony export */   ValidationError: () => (/* binding */ ValidationError),\n/* harmony export */   asyncHandler: () => (/* binding */ asyncHandler),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   handlePrismaError: () => (/* binding */ handlePrismaError),\n/* harmony export */   handleZodError: () => (/* binding */ handleZodError),\n/* harmony export */   isAppError: () => (/* binding */ isAppError)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v4/classic/errors.js\");\n\n\nclass AppError extends Error {\n    constructor(message, statusCode = 500, code = \"INTERNAL_ERROR\", details){\n        super(message);\n        this.statusCode = statusCode;\n        this.code = code;\n        this.details = details;\n        this.name = \"AppError\";\n        // Maintains proper stack trace for where our error was thrown (only available on V8)\n        if (Error.captureStackTrace) {\n            Error.captureStackTrace(this, AppError);\n        }\n    }\n}\nclass ValidationError extends AppError {\n    constructor(message, details){\n        super(message, 400, \"VALIDATION_ERROR\", details);\n        this.name = \"ValidationError\";\n    }\n}\nclass AuthenticationError extends AppError {\n    constructor(message = \"Authentication required\"){\n        super(message, 401, \"AUTHENTICATION_ERROR\");\n        this.name = \"AuthenticationError\";\n    }\n}\nclass AuthorizationError extends AppError {\n    constructor(message = \"Insufficient permissions\"){\n        super(message, 403, \"AUTHORIZATION_ERROR\");\n        this.name = \"AuthorizationError\";\n    }\n}\nclass NotFoundError extends AppError {\n    constructor(resource = \"Resource\"){\n        super(`${resource} not found`, 404, \"NOT_FOUND_ERROR\");\n        this.name = \"NotFoundError\";\n    }\n}\nclass ConflictError extends AppError {\n    constructor(message){\n        super(message, 409, \"CONFLICT_ERROR\");\n        this.name = \"ConflictError\";\n    }\n}\nclass RateLimitError extends AppError {\n    constructor(message = \"Rate limit exceeded\"){\n        super(message, 429, \"RATE_LIMIT_ERROR\");\n        this.name = \"RateLimitError\";\n    }\n}\nclass DatabaseError extends AppError {\n    constructor(message, details){\n        super(message, 500, \"DATABASE_ERROR\", details);\n        this.name = \"DatabaseError\";\n    }\n}\nclass ExternalServiceError extends AppError {\n    constructor(service, message, details){\n        super(`${service} service error: ${message}`, 502, \"EXTERNAL_SERVICE_ERROR\", details);\n        this.name = \"ExternalServiceError\";\n    }\n}\nfunction handlePrismaError(error) {\n    const prismaError = error;\n    switch(prismaError.code){\n        case \"P2002\":\n            // Unique constraint violation\n            const target = prismaError.meta?.target?.[0] || \"field\";\n            return new ConflictError(`${target} already exists`);\n        case \"P2003\":\n            // Foreign key constraint violation\n            const constraint = prismaError.meta?.constraint;\n            if (constraint?.includes(\"userId\")) {\n                return new AuthenticationError(\"Invalid user session\");\n            }\n            return new ValidationError(\"Invalid reference to related record\");\n        case \"P2025\":\n            // Record not found\n            return new NotFoundError();\n        case \"P2014\":\n            // Required relation violation\n            return new ValidationError(\"Missing required relationship\");\n        case \"P2000\":\n            // Value too long\n            return new ValidationError(\"Input value is too long\");\n        case \"P2001\":\n            // Record does not exist\n            return new NotFoundError();\n        case \"P2004\":\n            // Constraint failed\n            return new ValidationError(\"Data constraint violation\");\n        default:\n            return new DatabaseError(\"Database operation failed\", {\n                code: prismaError.code,\n                message: prismaError.message\n            });\n    }\n}\n// Zod error handling\nfunction handleZodError(error) {\n    const errors = error.issues.map((err)=>({\n            field: err.path.join(\".\"),\n            message: err.message,\n            code: err.code\n        }));\n    return new ValidationError(\"Validation failed\", {\n        errors\n    });\n}\n// Generic error handler for API routes\nfunction handleApiError(error) {\n    const isDevelopment = \"development\" === \"development\";\n    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;\n    // Log error details server-side\n    if (error instanceof Error) {\n        console.error(`[${new Date().toISOString()}] API Error:`, {\n            requestId,\n            name: error.name,\n            message: error.message,\n            stack: isDevelopment ? error.stack : undefined,\n            code: error.code\n        });\n    } else {\n        console.error(`[${new Date().toISOString()}] API Error:`, {\n            requestId,\n            error\n        });\n    }\n    // Handle known error types\n    if (error instanceof AppError) {\n        const response = {\n            success: false,\n            error: {\n                code: error.code,\n                message: isDevelopment ? error.message : getProductionMessage(error.code),\n                requestId,\n                ...isDevelopment && error.details && {\n                    details: error.details\n                }\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: error.statusCode\n        });\n    }\n    // Handle Zod validation errors\n    if (error instanceof zod__WEBPACK_IMPORTED_MODULE_1__.ZodError) {\n        const validationError = handleZodError(error);\n        const response = {\n            success: false,\n            error: {\n                code: validationError.code,\n                message: \"Validation failed\",\n                requestId,\n                ...isDevelopment && {\n                    details: validationError.details\n                }\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: validationError.statusCode\n        });\n    }\n    // Handle Prisma errors\n    if (error && typeof error === \"object\" && \"code\" in error && typeof error.code === \"string\") {\n        const prismaError = handlePrismaError(error);\n        const response = {\n            success: false,\n            error: {\n                code: prismaError.code,\n                message: isDevelopment ? prismaError.message : getProductionMessage(prismaError.code),\n                requestId,\n                ...isDevelopment && prismaError.details && {\n                    details: prismaError.details\n                }\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: prismaError.statusCode\n        });\n    }\n    // Handle generic errors\n    const response = {\n        success: false,\n        error: {\n            code: \"INTERNAL_ERROR\",\n            message: \"An error occurred processing your request\",\n            requestId,\n            ...isDevelopment && error instanceof Error && {\n                details: error.message\n            }\n        }\n    };\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n        status: 500\n    });\n}\n// Production-safe error messages\nfunction getProductionMessage(code) {\n    const messages = {\n        \"VALIDATION_ERROR\": \"Invalid input provided\",\n        \"AUTHENTICATION_ERROR\": \"Authentication required\",\n        \"AUTHORIZATION_ERROR\": \"Insufficient permissions\",\n        \"NOT_FOUND_ERROR\": \"Resource not found\",\n        \"CONFLICT_ERROR\": \"Request conflicts with current state\",\n        \"RATE_LIMIT_ERROR\": \"Too many requests\",\n        \"DATABASE_ERROR\": \"Database operation failed\",\n        \"EXTERNAL_SERVICE_ERROR\": \"External service unavailable\",\n        \"INTERNAL_ERROR\": \"An error occurred processing your request\"\n    };\n    return messages[code] || messages[\"INTERNAL_ERROR\"];\n}\n// Error response helpers\nconst ErrorResponses = {\n    unauthorized: ()=>next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: \"UNAUTHORIZED\",\n                message: \"Authentication required\"\n            }\n        }, {\n            status: 401\n        }),\n    forbidden: ()=>next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: \"FORBIDDEN\",\n                message: \"Insufficient permissions\"\n            }\n        }, {\n            status: 403\n        }),\n    notFound: (resource = \"Resource\")=>next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: \"NOT_FOUND\",\n                message: `${resource} not found`\n            }\n        }, {\n            status: 404\n        }),\n    validation: (message, details)=>next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: \"VALIDATION_ERROR\",\n                message,\n                ...details && {\n                    details\n                }\n            }\n        }, {\n            status: 400\n        }),\n    conflict: (message)=>next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: \"CONFLICT\",\n                message\n            }\n        }, {\n            status: 409\n        }),\n    rateLimit: ()=>next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: \"RATE_LIMIT\",\n                message: \"Rate limit exceeded\"\n            }\n        }, {\n            status: 429\n        }),\n    internal: (message)=>{\n        const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: {\n                code: \"INTERNAL_ERROR\",\n                message:  false ? 0 : message || \"An unexpected error occurred\",\n                requestId\n            }\n        }, {\n            status: 500\n        });\n    }\n};\n// Async error wrapper for API routes\nfunction asyncHandler(fn) {\n    return async (...args)=>{\n        try {\n            return await fn(...args);\n        } catch (error) {\n            return handleApiError(error);\n        }\n    };\n}\n// Type guard for checking if error is an AppError\nfunction isAppError(error) {\n    return error instanceof AppError;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/errors.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/logger.ts":
/*!***************************!*\
  !*** ./app/lib/logger.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogLevel: () => (/* binding */ LogLevel),\n/* harmony export */   createRequestLogger: () => (/* binding */ createRequestLogger),\n/* harmony export */   devLog: () => (/* binding */ devLog),\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\nvar LogLevel;\n(function(LogLevel) {\n    LogLevel[LogLevel[\"ERROR\"] = 0] = \"ERROR\";\n    LogLevel[LogLevel[\"WARN\"] = 1] = \"WARN\";\n    LogLevel[LogLevel[\"INFO\"] = 2] = \"INFO\";\n    LogLevel[LogLevel[\"DEBUG\"] = 3] = \"DEBUG\";\n})(LogLevel || (LogLevel = {}));\nclass Logger {\n    constructor(){\n        this.isDevelopment = \"development\" === \"development\";\n        // Reduce verbosity in development - only show warnings and errors\n        this.logLevel = this.isDevelopment ? 1 : 2;\n    }\n    shouldLog(level) {\n        return level <= this.logLevel;\n    }\n    formatMessage(entry) {\n        const { timestamp, level, message, context, error, userId, requestId } = entry;\n        const levelName = LogLevel[level];\n        let formatted = `[${timestamp}] ${levelName}: ${message}`;\n        if (userId) {\n            formatted += ` | User: ${userId}`;\n        }\n        if (requestId) {\n            formatted += ` | Request: ${requestId}`;\n        }\n        if (context && Object.keys(context).length > 0) {\n            formatted += ` | Context: ${JSON.stringify(context)}`;\n        }\n        if (error) {\n            formatted += ` | Error: ${error.message}`;\n            if (this.isDevelopment && error.stack) {\n                formatted += `\\nStack: ${error.stack}`;\n            }\n        }\n        return formatted;\n    }\n    log(level, message, context, error) {\n        if (!this.shouldLog(level)) return;\n        const entry = {\n            timestamp: new Date().toISOString(),\n            level,\n            message,\n            context,\n            error\n        };\n        const formatted = this.formatMessage(entry);\n        // In development, use console methods for better formatting\n        if (this.isDevelopment) {\n            switch(level){\n                case 0:\n                    console.error(formatted);\n                    break;\n                case 1:\n                    console.warn(formatted);\n                    break;\n                case 2:\n                    console.info(formatted);\n                    break;\n                case 3:\n                    console.debug(formatted);\n                    break;\n            }\n        } else {\n            // In production, use structured logging (JSON format)\n            console.log(JSON.stringify(entry));\n        }\n    }\n    error(message, error, context) {\n        this.log(0, message, context, error);\n    }\n    warn(message, context) {\n        this.log(1, message, context);\n    }\n    info(message, context) {\n        this.log(2, message, context);\n    }\n    debug(message, context) {\n        this.log(3, message, context);\n    }\n    // API-specific logging methods\n    apiRequest(method, path, userId, context) {\n        this.info(`API ${method} ${path}`, {\n            ...context,\n            userId,\n            type: \"api_request\"\n        });\n    }\n    apiResponse(method, path, statusCode, duration, context) {\n        this.info(`API ${method} ${path} - ${statusCode}`, {\n            ...context,\n            statusCode,\n            duration,\n            type: \"api_response\"\n        });\n    }\n    apiError(method, path, error, userId, context) {\n        this.error(`API ${method} ${path} failed`, error, {\n            ...context,\n            userId,\n            type: \"api_error\"\n        });\n    }\n    // Authentication logging\n    authSuccess(userId, method, context) {\n        this.info(`Authentication successful`, {\n            ...context,\n            userId,\n            method,\n            type: \"auth_success\"\n        });\n    }\n    authFailure(email, method, reason, context) {\n        this.warn(`Authentication failed`, {\n            ...context,\n            email,\n            method,\n            reason,\n            type: \"auth_failure\"\n        });\n    }\n    // Database logging\n    dbQuery(operation, table, duration, context) {\n        this.debug(`DB ${operation} on ${table}`, {\n            ...context,\n            operation,\n            table,\n            duration,\n            type: \"db_query\"\n        });\n    }\n    dbError(operation, table, error, context) {\n        this.error(`DB ${operation} on ${table} failed`, error, {\n            ...context,\n            operation,\n            table,\n            type: \"db_error\"\n        });\n    }\n    // Security logging\n    securityEvent(event, severity, context) {\n        const level = severity === \"high\" ? 0 : severity === \"medium\" ? 1 : 2;\n        this.log(level, `Security event: ${event}`, {\n            ...context,\n            severity,\n            type: \"security_event\"\n        });\n    }\n    // Rate limiting logging\n    rateLimitHit(identifier, limit, window, context) {\n        this.warn(`Rate limit exceeded`, {\n            ...context,\n            identifier,\n            limit,\n            window,\n            type: \"rate_limit\"\n        });\n    }\n    // Email logging\n    emailSent(to, subject, template, context) {\n        this.info(`Email sent`, {\n            ...context,\n            to,\n            subject,\n            template,\n            type: \"email_sent\"\n        });\n    }\n    emailError(to, subject, error, context) {\n        this.error(`Email failed to send`, error, {\n            ...context,\n            to,\n            subject,\n            type: \"email_error\"\n        });\n    }\n    // Performance logging\n    performance(operation, duration, context) {\n        const level = duration > 5000 ? 1 : 3;\n        this.log(level, `Performance: ${operation} took ${duration}ms`, {\n            ...context,\n            operation,\n            duration,\n            type: \"performance\"\n        });\n    }\n}\n// Create singleton instance\nconst logger = new Logger();\n// Request logging middleware helper\nfunction createRequestLogger(req) {\n    const requestId = crypto.randomUUID();\n    const startTime = Date.now();\n    return {\n        requestId,\n        log: (message, context)=>{\n            logger.info(message, {\n                ...context,\n                requestId\n            });\n        },\n        error: (message, error, context)=>{\n            logger.error(message, error, {\n                ...context,\n                requestId\n            });\n        },\n        end: (statusCode)=>{\n            const duration = Date.now() - startTime;\n            logger.apiResponse(req.method || \"UNKNOWN\", new URL(req.url || \"\").pathname, statusCode, duration, {\n                requestId\n            });\n        }\n    };\n}\n// Development-only logging helpers\nconst devLog = {\n    info: (message, data)=>{\n        if (true) {\n            console.log(`🔍 ${message}`, data ? JSON.stringify(data, null, 2) : \"\");\n        }\n    },\n    error: (message, error)=>{\n        if (true) {\n            console.error(`❌ ${message}`, error);\n        }\n    },\n    warn: (message, data)=>{\n        if (true) {\n            console.warn(`⚠️ ${message}`, data ? JSON.stringify(data, null, 2) : \"\");\n        }\n    },\n    success: (message, data)=>{\n        if (true) {\n            console.log(`✅ ${message}`, data ? JSON.stringify(data, null, 2) : \"\");\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/logger.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute&page=%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2F%5Bid%5D%2Fvariations%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();