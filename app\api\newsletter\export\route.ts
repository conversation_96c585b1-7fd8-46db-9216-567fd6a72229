import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../lib/auth';
import { prisma } from '@/app/lib/db';

// GET /api/newsletter/export - Export newsletter subscribers as CSV (Admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Check if user is admin
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const isActive = searchParams.get('active');
    const source = searchParams.get('source');
    const format = searchParams.get('format') || 'csv';

    // Build where clause
    const where: any = {};
    if (isActive !== null) {
      where.isActive = isActive === 'true';
    }
    if (source) {
      where.source = source;
    }

    // Get all subscribers matching criteria
    const subscribers = await prisma.newsletterSubscriber.findMany({
      where,
      orderBy: { subscribedAt: 'desc' },
    });

    if (format === 'csv') {
      // Generate CSV content
      const csvHeaders = ['Email', 'Name', 'Status', 'Source', 'Subscribed At', 'Unsubscribed At'];
      const csvRows = subscribers.map(subscriber => [
        subscriber.email,
        subscriber.name || '',
        subscriber.isActive ? 'Active' : 'Inactive',
        subscriber.source || '',
        subscriber.subscribedAt.toISOString(),
        subscriber.unsubscribedAt?.toISOString() || '',
      ]);

      const csvContent = [
        csvHeaders.join(','),
        ...csvRows.map(row => row.map(field => `"${field}"`).join(','))
      ].join('\n');

      // Return CSV file
      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="newsletter-subscribers-${new Date().toISOString().split('T')[0]}.csv"`,
        },
      });
    } else if (format === 'json') {
      // Return JSON format
      return NextResponse.json({
        success: true,
        data: {
          subscribers,
          exportedAt: new Date().toISOString(),
          total: subscribers.length,
        },
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Unsupported format. Use csv or json.' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error exporting newsletter subscribers:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to export newsletter subscribers' },
      { status: 500 }
    );
  }
}
