"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/homepage-settings/route";
exports.ids = ["app/api/homepage-settings/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhomepage-settings%2Froute&page=%2Fapi%2Fhomepage-settings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhomepage-settings%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhomepage-settings%2Froute&page=%2Fapi%2Fhomepage-settings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhomepage-settings%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_project_app_api_homepage_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/homepage-settings/route.ts */ \"(rsc)/./app/api/homepage-settings/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/homepage-settings/route\",\n        pathname: \"/api/homepage-settings\",\n        filename: \"route\",\n        bundlePath: \"app/api/homepage-settings/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\api\\\\homepage-settings\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_project_app_api_homepage_settings_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/homepage-settings/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhomepage-settings%2Froute&page=%2Fapi%2Fhomepage-settings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhomepage-settings%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/homepage-settings/route.ts":
/*!********************************************!*\
  !*** ./app/api/homepage-settings/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _app_lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/db */ \"(rsc)/./app/lib/db.ts\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/auth */ \"(rsc)/./app/lib/auth.ts\");\n\n\n\n\n// GET /api/homepage-settings - Get homepage settings\nasync function GET() {\n    try {\n        // Get homepage settings\n        const settings = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.homepageSetting.findMany({\n            orderBy: {\n                createdAt: \"desc\"\n            }\n        });\n        // Get featured product (Product of the Month)\n        // First check if there's a specific product set in homepage settings\n        let featuredProduct = null;\n        if (settings.length > 0 && settings[0].productOfTheMonthId) {\n            featuredProduct = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findUnique({\n                where: {\n                    id: settings[0].productOfTheMonthId,\n                    isActive: true\n                },\n                include: {\n                    images: true,\n                    category: true,\n                    _count: {\n                        select: {\n                            reviews: true\n                        }\n                    }\n                }\n            });\n        }\n        // If no specific product is set, fall back to the first featured product\n        if (!featuredProduct) {\n            featuredProduct = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findFirst({\n                where: {\n                    isFeatured: true,\n                    isActive: true\n                },\n                include: {\n                    images: true,\n                    category: true,\n                    _count: {\n                        select: {\n                            reviews: true\n                        }\n                    }\n                }\n            });\n        }\n        // Get bestsellers - either manually selected or auto-selected\n        let bestsellers = [];\n        if (settings.length > 0 && settings[0].bestsellerIds && settings[0].bestsellerIds.length > 0) {\n            // Get manually selected bestsellers\n            const bestsellerProducts = await Promise.all(settings[0].bestsellerIds.map(async (id)=>{\n                return await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findUnique({\n                    where: {\n                        id,\n                        isActive: true\n                    },\n                    include: {\n                        images: true,\n                        category: true,\n                        _count: {\n                            select: {\n                                reviews: true\n                            }\n                        }\n                    }\n                });\n            }));\n            // Filter out null results and maintain order\n            bestsellers = bestsellerProducts.filter(Boolean);\n        } else {\n            // Auto-select bestsellers (top 4 products by sales/reviews)\n            bestsellers = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.product.findMany({\n                where: {\n                    isActive: true\n                },\n                include: {\n                    images: true,\n                    category: true,\n                    _count: {\n                        select: {\n                            reviews: true\n                        }\n                    }\n                },\n                orderBy: {\n                    reviews: {\n                        _count: \"desc\"\n                    }\n                },\n                take: 4\n            });\n        }\n        // Get categories for showcase\n        const categories = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.category.findMany({\n            where: {\n                isActive: true\n            },\n            include: {\n                _count: {\n                    select: {\n                        products: {\n                            where: {\n                                isActive: true\n                            }\n                        }\n                    }\n                }\n            },\n            take: 6\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                settings: settings.length > 0 ? settings[0] : null,\n                featuredProduct,\n                bestsellers,\n                categories\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching homepage settings:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to fetch homepage settings\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/homepage-settings - Create or update homepage settings\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_2__.getServerSession)(_app_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authOptions);\n        // Check if user is admin\n        if (!session || session.user.role !== \"ADMIN\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { // Hero Section\n        heroTitle, heroSubtitle, heroCtaText, heroCtaLink, heroBackgroundColor, showHero, // Product of the Month\n        productOfTheMonthId, showProductOfMonth, // Promotional Banner\n        bannerText, bannerCtaText, bannerCtaLink, bannerBackgroundColor, showBanner, // Sections\n        showCategories, productSectionBgColor, bestsellerIds, showBestsellers, // Newsletter\n        newsletterTitle, newsletterSubtitle, showNewsletter, // Trust Badges\n        showTrustBadges, // Flash Sale Section\n        flashSaleTitle, flashSaleSubtitle, flashSaleEndDate, flashSaleBackgroundColor, flashSalePercentage, showFlashSale, // Testimonials Section\n        testimonialsTitle, testimonialsSubtitle, testimonialsBackgroundColor, showTestimonials, isActive } = body;\n        // Create or update homepage settings\n        const settings = await _app_lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.homepageSetting.upsert({\n            where: {\n                id: \"homepage-settings\"\n            },\n            update: {\n                // Hero Section\n                ...heroTitle !== undefined && {\n                    heroTitle\n                },\n                ...heroSubtitle !== undefined && {\n                    heroSubtitle\n                },\n                ...heroCtaText !== undefined && {\n                    heroCtaText\n                },\n                ...heroCtaLink !== undefined && {\n                    heroCtaLink\n                },\n                ...heroBackgroundColor !== undefined && {\n                    heroBackgroundColor\n                },\n                ...showHero !== undefined && {\n                    showHero\n                },\n                // Product of the Month\n                ...productOfTheMonthId !== undefined && {\n                    productOfTheMonthId\n                },\n                ...showProductOfMonth !== undefined && {\n                    showProductOfMonth\n                },\n                // Promotional Banner\n                ...bannerText !== undefined && {\n                    bannerText\n                },\n                ...bannerCtaText !== undefined && {\n                    bannerCtaText\n                },\n                ...bannerCtaLink !== undefined && {\n                    bannerCtaLink\n                },\n                ...bannerBackgroundColor !== undefined && {\n                    bannerBackgroundColor\n                },\n                ...showBanner !== undefined && {\n                    showBanner\n                },\n                // Sections\n                ...showCategories !== undefined && {\n                    showCategories\n                },\n                ...productSectionBgColor !== undefined && {\n                    productSectionBgColor\n                },\n                ...bestsellerIds !== undefined && {\n                    bestsellerIds\n                },\n                ...showBestsellers !== undefined && {\n                    showBestsellers\n                },\n                // Newsletter\n                ...newsletterTitle !== undefined && {\n                    newsletterTitle\n                },\n                ...newsletterSubtitle !== undefined && {\n                    newsletterSubtitle\n                },\n                ...showNewsletter !== undefined && {\n                    showNewsletter\n                },\n                // Trust Badges\n                ...showTrustBadges !== undefined && {\n                    showTrustBadges\n                },\n                // Flash Sale Section\n                ...flashSaleTitle !== undefined && {\n                    flashSaleTitle\n                },\n                ...flashSaleSubtitle !== undefined && {\n                    flashSaleSubtitle\n                },\n                ...flashSaleEndDate !== undefined && {\n                    flashSaleEndDate: flashSaleEndDate ? new Date(flashSaleEndDate) : null\n                },\n                ...flashSaleBackgroundColor !== undefined && {\n                    flashSaleBackgroundColor\n                },\n                ...flashSalePercentage !== undefined && {\n                    flashSalePercentage\n                },\n                ...showFlashSale !== undefined && {\n                    showFlashSale\n                },\n                // Testimonials Section\n                ...testimonialsTitle !== undefined && {\n                    testimonialsTitle\n                },\n                ...testimonialsSubtitle !== undefined && {\n                    testimonialsSubtitle\n                },\n                ...testimonialsBackgroundColor !== undefined && {\n                    testimonialsBackgroundColor\n                },\n                ...showTestimonials !== undefined && {\n                    showTestimonials\n                },\n                ...isActive !== undefined && {\n                    isActive\n                },\n                updatedAt: new Date()\n            },\n            create: {\n                id: \"homepage-settings\",\n                // Hero Section\n                heroTitle: heroTitle || \"Natural Skincare Essentials\",\n                heroSubtitle: heroSubtitle || \"Discover our botanical collection crafted with nature's finest ingredients for radiant, healthy skin\",\n                heroCtaText: heroCtaText || \"Shop Collection\",\n                heroCtaLink: heroCtaLink || \"/shop\",\n                heroBackgroundColor: heroBackgroundColor || \"#f0fdf4\",\n                showHero: showHero !== undefined ? showHero : true,\n                // Product of the Month\n                productOfTheMonthId,\n                showProductOfMonth: showProductOfMonth !== undefined ? showProductOfMonth : true,\n                // Promotional Banner\n                bannerText,\n                bannerCtaText,\n                bannerCtaLink,\n                bannerBackgroundColor: bannerBackgroundColor || \"#22c55e\",\n                showBanner: showBanner !== undefined ? showBanner : true,\n                // Sections\n                showCategories: showCategories !== undefined ? showCategories : true,\n                productSectionBgColor: productSectionBgColor || \"#f0fdf4\",\n                bestsellerIds: bestsellerIds || [],\n                showBestsellers: showBestsellers !== undefined ? showBestsellers : true,\n                // Newsletter\n                newsletterTitle: newsletterTitle || \"Stay Updated\",\n                newsletterSubtitle: newsletterSubtitle || \"Get the latest updates on new products and exclusive offers\",\n                showNewsletter: showNewsletter !== undefined ? showNewsletter : true,\n                // Trust Badges\n                showTrustBadges: showTrustBadges !== undefined ? showTrustBadges : true,\n                // Flash Sale Section\n                flashSaleTitle: flashSaleTitle || \"Weekend Flash Sale\",\n                flashSaleSubtitle: flashSaleSubtitle || `Get ${flashSalePercentage ?? 25}% off all natural skincare products`,\n                flashSaleEndDate: flashSaleEndDate ? new Date(flashSaleEndDate) : null,\n                flashSaleBackgroundColor: flashSaleBackgroundColor || \"#16a34a\",\n                flashSalePercentage: flashSalePercentage ?? 25,\n                showFlashSale: showFlashSale !== undefined ? showFlashSale : true,\n                // Testimonials Section\n                testimonialsTitle: testimonialsTitle || \"What Our Customers Say\",\n                testimonialsSubtitle: testimonialsSubtitle || \"Real reviews from real customers who love our natural skincare\",\n                testimonialsBackgroundColor: testimonialsBackgroundColor || \"#f0fdf4\",\n                showTestimonials: showTestimonials !== undefined ? showTestimonials : true,\n                isActive: isActive !== undefined ? isActive : true\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: settings,\n            message: \"Homepage settings updated successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error updating homepage settings:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to update homepage settings\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/homepage-settings/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/auth.ts":
/*!*************************!*\
  !*** ./app/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db */ \"(rsc)/./app/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst authOptions = {\n    // Remove PrismaAdapter when using JWT strategy\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const user = await _db__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user || !user.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2__.compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\",\n        maxAge: 7 * 24 * 60 * 60,\n        updateAge: 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 7 * 24 * 60 * 60\n    },\n    // Enable CSRF protection\n    cookies: {\n        sessionToken: {\n            name:  false ? 0 : `next-auth.session-token`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: \"development\" === \"production\"\n            }\n        },\n        callbackUrl: {\n            name:  false ? 0 : `next-auth.callback-url`,\n            options: {\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: \"development\" === \"production\"\n            }\n        },\n        csrfToken: {\n            name:  false ? 0 : `next-auth.csrf-token`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: \"development\" === \"production\"\n            }\n        }\n    },\n    callbacks: {\n        async jwt ({ token, user, account, profile }) {\n            // Handle initial sign in\n            if (account && user) {\n                // For OAuth providers, create/update user in database\n                if (account.provider === \"google\") {\n                    try {\n                        const email = user.email || profile?.email;\n                        if (!email) {\n                            throw new Error(\"No email found for Google account\");\n                        }\n                        // Check if user exists\n                        let dbUser = await _db__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n                            where: {\n                                email\n                            },\n                            select: {\n                                id: true,\n                                role: true,\n                                name: true,\n                                email: true\n                            }\n                        });\n                        // Create user if doesn't exist\n                        if (!dbUser) {\n                            dbUser = await _db__WEBPACK_IMPORTED_MODULE_3__.prisma.user.create({\n                                data: {\n                                    email,\n                                    name: user.name || profile?.name || email.split(\"@\")[0],\n                                    // Don't save Google profile picture\n                                    role: _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.CUSTOMER,\n                                    emailVerified: new Date()\n                                },\n                                select: {\n                                    id: true,\n                                    role: true,\n                                    name: true,\n                                    email: true\n                                }\n                            });\n                        }\n                        // Set token properties\n                        token.sub = dbUser.id;\n                        token.role = dbUser.role;\n                        token.email = dbUser.email;\n                        token.name = dbUser.name;\n                    } catch (error) {\n                        console.error(\"Error handling Google sign in:\", error);\n                        throw error; // This will prevent sign in\n                    }\n                } else if (user) {\n                    // For credentials provider\n                    token.sub = user.id;\n                    token.role = user.role;\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            // Populate session with user data from token\n            if (token && session.user) {\n                session.user = {\n                    ...session.user,\n                    id: token.sub,\n                    role: token.role,\n                    email: token.email,\n                    name: token.name\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Allows relative callback URLs\n            if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\n            else if (new URL(url).origin === baseUrl) return url;\n            return baseUrl;\n        }\n    },\n    events: {\n        async signIn ({ user, account, profile, isNewUser }) {\n            // Log sign in events in development only\n            if (true) {\n                console.log(\"Sign in event:\", {\n                    userId: user.id,\n                    email: user.email,\n                    provider: account?.provider,\n                    isNewUser\n                });\n            }\n        }\n    },\n    pages: {\n        signIn: \"/login\",\n        signOut: \"/\",\n        error: \"/login\"\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    // Only enable debug in development when explicitly set\n    debug:  true && process.env.NEXTAUTH_DEBUG === \"true\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGliL2F1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUN3RDtBQUNVO0FBQy9CO0FBQ0w7QUFDWTtBQVNuQyxNQUFNSyxjQUErQjtJQUMxQywrQ0FBK0M7SUFDL0NDLFdBQVc7UUFDVE4sc0VBQWNBLENBQUM7WUFDYk8sVUFBVUMsUUFBUUMsR0FBRyxDQUFDQyxnQkFBZ0I7WUFDdENDLGNBQWNILFFBQVFDLEdBQUcsQ0FBQ0csb0JBQW9CO1FBQ2hEO1FBQ0FYLDJFQUFtQkEsQ0FBQztZQUNsQlksTUFBTTtZQUNOQyxhQUFhO2dCQUNYQyxPQUFPO29CQUFFQyxPQUFPO29CQUFTQyxNQUFNO2dCQUFRO2dCQUN2Q0MsVUFBVTtvQkFBRUYsT0FBTztvQkFBWUMsTUFBTTtnQkFBVztZQUNsRDtZQUNBLE1BQU1FLFdBQVVMLFdBQVc7Z0JBQ3pCLElBQUksQ0FBQ0EsYUFBYUMsU0FBUyxDQUFDRCxhQUFhSSxVQUFVO29CQUNqRCxNQUFNLElBQUlFLE1BQU07Z0JBQ2xCO2dCQUVBLE1BQU1DLE9BQU8sTUFBTWxCLHVDQUFNQSxDQUFDa0IsSUFBSSxDQUFDQyxVQUFVLENBQUM7b0JBQ3hDQyxPQUFPO3dCQUFFUixPQUFPRCxZQUFZQyxLQUFLO29CQUFDO2dCQUNwQztnQkFFQSxJQUFJLENBQUNNLFFBQVEsQ0FBQ0EsS0FBS0gsUUFBUSxFQUFFO29CQUMzQixNQUFNLElBQUlFLE1BQU07Z0JBQ2xCO2dCQUVBLE1BQU1JLGtCQUFrQixNQUFNdEIsNkNBQWMsQ0FDMUNZLFlBQVlJLFFBQVEsRUFDcEJHLEtBQUtILFFBQVE7Z0JBR2YsSUFBSSxDQUFDTSxpQkFBaUI7b0JBQ3BCLE1BQU0sSUFBSUosTUFBTTtnQkFDbEI7Z0JBRUEsT0FBTztvQkFDTE0sSUFBSUwsS0FBS0ssRUFBRTtvQkFDWFgsT0FBT00sS0FBS04sS0FBSztvQkFDakJGLE1BQU1RLEtBQUtSLElBQUk7b0JBQ2ZjLE1BQU1OLEtBQUtNLElBQUk7Z0JBQ2pCO1lBQ0Y7UUFDRjtLQUNEO0lBQ0RDLFNBQVM7UUFDUEMsVUFBVTtRQUNWQyxRQUFRLElBQUksS0FBSyxLQUFLO1FBQ3RCQyxXQUFXLEtBQUssS0FBSztJQUN2QjtJQUNBQyxLQUFLO1FBQ0hGLFFBQVEsSUFBSSxLQUFLLEtBQUs7SUFDeEI7SUFDQSx5QkFBeUI7SUFDekJHLFNBQVM7UUFDUEMsY0FBYztZQUNackIsTUFBTUwsTUFBeUIsR0FDM0IsQ0FBa0MsR0FDbEMsQ0FBQyx1QkFBdUIsQ0FBQztZQUM3QjJCLFNBQVM7Z0JBQ1BDLFVBQVU7Z0JBQ1ZDLFVBQVU7Z0JBQ1ZDLE1BQU07Z0JBQ05DLFFBQVEvQixrQkFBeUI7WUFDbkM7UUFDRjtRQUNBZ0MsYUFBYTtZQUNYM0IsTUFBTUwsTUFBeUIsR0FDM0IsQ0FBaUMsR0FDakMsQ0FBQyxzQkFBc0IsQ0FBQztZQUM1QjJCLFNBQVM7Z0JBQ1BFLFVBQVU7Z0JBQ1ZDLE1BQU07Z0JBQ05DLFFBQVEvQixrQkFBeUI7WUFDbkM7UUFDRjtRQUNBaUMsV0FBVztZQUNUNUIsTUFBTUwsTUFBeUIsR0FDM0IsQ0FBNkIsR0FDN0IsQ0FBQyxvQkFBb0IsQ0FBQztZQUMxQjJCLFNBQVM7Z0JBQ1BDLFVBQVU7Z0JBQ1ZDLFVBQVU7Z0JBQ1ZDLE1BQU07Z0JBQ05DLFFBQVEvQixrQkFBeUI7WUFDbkM7UUFDRjtJQUNGO0lBQ0FrQyxXQUFXO1FBQ1QsTUFBTVYsS0FBSSxFQUFFVyxLQUFLLEVBQUV0QixJQUFJLEVBQUV1QixPQUFPLEVBQUVDLE9BQU8sRUFBRTtZQUN6Qyx5QkFBeUI7WUFDekIsSUFBSUQsV0FBV3ZCLE1BQU07Z0JBQ25CLHNEQUFzRDtnQkFDdEQsSUFBSXVCLFFBQVFFLFFBQVEsS0FBSyxVQUFVO29CQUNqQyxJQUFJO3dCQUNGLE1BQU0vQixRQUFRTSxLQUFLTixLQUFLLElBQUs4QixTQUFpQjlCO3dCQUM5QyxJQUFJLENBQUNBLE9BQU87NEJBQ1YsTUFBTSxJQUFJSyxNQUFNO3dCQUNsQjt3QkFFQSx1QkFBdUI7d0JBQ3ZCLElBQUkyQixTQUFTLE1BQU01Qyx1Q0FBTUEsQ0FBQ2tCLElBQUksQ0FBQ0MsVUFBVSxDQUFDOzRCQUN4Q0MsT0FBTztnQ0FBRVI7NEJBQU07NEJBQ2ZpQyxRQUFRO2dDQUFFdEIsSUFBSTtnQ0FBTUMsTUFBTTtnQ0FBTWQsTUFBTTtnQ0FBTUUsT0FBTzs0QkFBSzt3QkFDMUQ7d0JBRUEsK0JBQStCO3dCQUMvQixJQUFJLENBQUNnQyxRQUFROzRCQUNYQSxTQUFTLE1BQU01Qyx1Q0FBTUEsQ0FBQ2tCLElBQUksQ0FBQzRCLE1BQU0sQ0FBQztnQ0FDaENDLE1BQU07b0NBQ0puQztvQ0FDQUYsTUFBTVEsS0FBS1IsSUFBSSxJQUFLZ0MsU0FBaUJoQyxRQUFRRSxNQUFNb0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO29DQUNoRSxvQ0FBb0M7b0NBQ3BDeEIsTUFBTXZCLG9EQUFRQSxDQUFDZ0QsUUFBUTtvQ0FDdkJDLGVBQWUsSUFBSUM7Z0NBQ3JCO2dDQUNBTixRQUFRO29DQUFFdEIsSUFBSTtvQ0FBTUMsTUFBTTtvQ0FBTWQsTUFBTTtvQ0FBTUUsT0FBTztnQ0FBSzs0QkFDMUQ7d0JBQ0Y7d0JBRUEsdUJBQXVCO3dCQUN2QjRCLE1BQU1ZLEdBQUcsR0FBR1IsT0FBT3JCLEVBQUU7d0JBQ3JCaUIsTUFBTWhCLElBQUksR0FBR29CLE9BQU9wQixJQUFJO3dCQUN4QmdCLE1BQU01QixLQUFLLEdBQUdnQyxPQUFPaEMsS0FBSzt3QkFDMUI0QixNQUFNOUIsSUFBSSxHQUFHa0MsT0FBT2xDLElBQUk7b0JBQzFCLEVBQUUsT0FBTzJDLE9BQU87d0JBQ2RDLFFBQVFELEtBQUssQ0FBQyxrQ0FBa0NBO3dCQUNoRCxNQUFNQSxPQUFPLDRCQUE0QjtvQkFDM0M7Z0JBQ0YsT0FBTyxJQUFJbkMsTUFBTTtvQkFDZiwyQkFBMkI7b0JBQzNCc0IsTUFBTVksR0FBRyxHQUFHbEMsS0FBS0ssRUFBRTtvQkFDbkJpQixNQUFNaEIsSUFBSSxHQUFHLEtBQXVCQSxJQUFJO2dCQUMxQztZQUNGO1lBRUEsT0FBT2dCO1FBQ1Q7UUFDQSxNQUFNZixTQUFRLEVBQUVBLE9BQU8sRUFBRWUsS0FBSyxFQUFFO1lBQzlCLDZDQUE2QztZQUM3QyxJQUFJQSxTQUFTZixRQUFRUCxJQUFJLEVBQUU7Z0JBQ3pCTyxRQUFRUCxJQUFJLEdBQUc7b0JBQ2IsR0FBR08sUUFBUVAsSUFBSTtvQkFDZkssSUFBSWlCLE1BQU1ZLEdBQUc7b0JBQ2I1QixNQUFNZ0IsTUFBTWhCLElBQUk7b0JBQ2hCWixPQUFPNEIsTUFBTTVCLEtBQUs7b0JBQ2xCRixNQUFNOEIsTUFBTTlCLElBQUk7Z0JBRWxCO1lBQ0Y7WUFFQSxPQUFPZTtRQUNUO1FBQ0EsTUFBTThCLFVBQVMsRUFBRUMsR0FBRyxFQUFFQyxPQUFPLEVBQUU7WUFDN0IsZ0NBQWdDO1lBQ2hDLElBQUlELElBQUlFLFVBQVUsQ0FBQyxNQUFNLE9BQU8sQ0FBQyxFQUFFRCxRQUFRLEVBQUVELElBQUksQ0FBQztpQkFFN0MsSUFBSSxJQUFJRyxJQUFJSCxLQUFLSSxNQUFNLEtBQUtILFNBQVMsT0FBT0Q7WUFDakQsT0FBT0M7UUFDVDtJQUNGO0lBQ0FJLFFBQVE7UUFDTixNQUFNQyxRQUFPLEVBQUU1QyxJQUFJLEVBQUV1QixPQUFPLEVBQUVDLE9BQU8sRUFBRXFCLFNBQVMsRUFBRTtZQUNoRCx5Q0FBeUM7WUFDekMsSUFBSTFELElBQXlCLEVBQWU7Z0JBQzFDaUQsUUFBUVUsR0FBRyxDQUFDLGtCQUFrQjtvQkFDNUJDLFFBQVEvQyxLQUFLSyxFQUFFO29CQUNmWCxPQUFPTSxLQUFLTixLQUFLO29CQUNqQitCLFVBQVVGLFNBQVNFO29CQUNuQm9CO2dCQUNGO1lBQ0Y7UUFDRjtJQUNGO0lBQ0FHLE9BQU87UUFDTEosUUFBUTtRQUNSSyxTQUFTO1FBQ1RkLE9BQU87SUFDVDtJQUNBZSxRQUFRL0QsUUFBUUMsR0FBRyxDQUFDK0QsZUFBZTtJQUNuQyx1REFBdUQ7SUFDdkRDLE9BQU9qRSxLQUF5QixJQUFpQkEsUUFBUUMsR0FBRyxDQUFDaUUsY0FBYyxLQUFLO0FBQ2xGLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vYXBwL2xpYi9hdXRoLnRzPzZiZmMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dEF1dGhPcHRpb25zIH0gZnJvbSBcIm5leHQtYXV0aFwiO1xyXG5pbXBvcnQgR29vZ2xlUHJvdmlkZXIgZnJvbSBcIm5leHQtYXV0aC9wcm92aWRlcnMvZ29vZ2xlXCI7XHJcbmltcG9ydCBDcmVkZW50aWFsc1Byb3ZpZGVyIGZyb20gXCJuZXh0LWF1dGgvcHJvdmlkZXJzL2NyZWRlbnRpYWxzXCI7XHJcbmltcG9ydCAqIGFzIGJjcnlwdCBmcm9tIFwiYmNyeXB0anNcIjtcclxuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSBcIi4vZGJcIjtcclxuaW1wb3J0IHsgVXNlclJvbGUgfSBmcm9tIFwiQHByaXNtYS9jbGllbnRcIjtcclxuXHJcbmludGVyZmFjZSBFeHRlbmRlZFVzZXIge1xyXG4gIGlkOiBzdHJpbmc7XHJcbiAgZW1haWw6IHN0cmluZztcclxuICBuYW1lPzogc3RyaW5nIHwgbnVsbDtcclxuICByb2xlOiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBhdXRoT3B0aW9uczogTmV4dEF1dGhPcHRpb25zID0ge1xyXG4gIC8vIFJlbW92ZSBQcmlzbWFBZGFwdGVyIHdoZW4gdXNpbmcgSldUIHN0cmF0ZWd5XHJcbiAgcHJvdmlkZXJzOiBbXHJcbiAgICBHb29nbGVQcm92aWRlcih7XHJcbiAgICAgIGNsaWVudElkOiBwcm9jZXNzLmVudi5HT09HTEVfQ0xJRU5UX0lEISxcclxuICAgICAgY2xpZW50U2VjcmV0OiBwcm9jZXNzLmVudi5HT09HTEVfQ0xJRU5UX1NFQ1JFVCEsXHJcbiAgICB9KSxcclxuICAgIENyZWRlbnRpYWxzUHJvdmlkZXIoe1xyXG4gICAgICBuYW1lOiBcImNyZWRlbnRpYWxzXCIsXHJcbiAgICAgIGNyZWRlbnRpYWxzOiB7XHJcbiAgICAgICAgZW1haWw6IHsgbGFiZWw6IFwiRW1haWxcIiwgdHlwZTogXCJlbWFpbFwiIH0sXHJcbiAgICAgICAgcGFzc3dvcmQ6IHsgbGFiZWw6IFwiUGFzc3dvcmRcIiwgdHlwZTogXCJwYXNzd29yZFwiIH1cclxuICAgICAgfSxcclxuICAgICAgYXN5bmMgYXV0aG9yaXplKGNyZWRlbnRpYWxzKSB7XHJcbiAgICAgICAgaWYgKCFjcmVkZW50aWFscz8uZW1haWwgfHwgIWNyZWRlbnRpYWxzPy5wYXNzd29yZCkge1xyXG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiSW52YWxpZCBjcmVkZW50aWFsc1wiKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IHVzZXIgPSBhd2FpdCBwcmlzbWEudXNlci5maW5kVW5pcXVlKHtcclxuICAgICAgICAgIHdoZXJlOiB7IGVtYWlsOiBjcmVkZW50aWFscy5lbWFpbCB9XHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGlmICghdXNlciB8fCAhdXNlci5wYXNzd29yZCkge1xyXG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiSW52YWxpZCBjcmVkZW50aWFsc1wiKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IGlzUGFzc3dvcmRWYWxpZCA9IGF3YWl0IGJjcnlwdC5jb21wYXJlKFxyXG4gICAgICAgICAgY3JlZGVudGlhbHMucGFzc3dvcmQsXHJcbiAgICAgICAgICB1c2VyLnBhc3N3b3JkXHJcbiAgICAgICAgKTtcclxuXHJcbiAgICAgICAgaWYgKCFpc1Bhc3N3b3JkVmFsaWQpIHtcclxuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkludmFsaWQgY3JlZGVudGlhbHNcIik7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgaWQ6IHVzZXIuaWQsXHJcbiAgICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcclxuICAgICAgICAgIG5hbWU6IHVzZXIubmFtZSxcclxuICAgICAgICAgIHJvbGU6IHVzZXIucm9sZSxcclxuICAgICAgICB9IGFzIEV4dGVuZGVkVXNlcjtcclxuICAgICAgfVxyXG4gICAgfSlcclxuICBdLFxyXG4gIHNlc3Npb246IHtcclxuICAgIHN0cmF0ZWd5OiBcImp3dFwiLFxyXG4gICAgbWF4QWdlOiA3ICogMjQgKiA2MCAqIDYwLCAvLyA3IGRheXMgZGVmYXVsdFxyXG4gICAgdXBkYXRlQWdlOiAyNCAqIDYwICogNjAsIC8vIFVwZGF0ZSBzZXNzaW9uIGV2ZXJ5IDI0IGhvdXJzXHJcbiAgfSxcclxuICBqd3Q6IHtcclxuICAgIG1heEFnZTogNyAqIDI0ICogNjAgKiA2MCwgLy8gNyBkYXlzIGRlZmF1bHRcclxuICB9LFxyXG4gIC8vIEVuYWJsZSBDU1JGIHByb3RlY3Rpb25cclxuICBjb29raWVzOiB7XHJcbiAgICBzZXNzaW9uVG9rZW46IHtcclxuICAgICAgbmFtZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJ1xyXG4gICAgICAgID8gYF9fU2VjdXJlLW5leHQtYXV0aC5zZXNzaW9uLXRva2VuYFxyXG4gICAgICAgIDogYG5leHQtYXV0aC5zZXNzaW9uLXRva2VuYCxcclxuICAgICAgb3B0aW9uczoge1xyXG4gICAgICAgIGh0dHBPbmx5OiB0cnVlLFxyXG4gICAgICAgIHNhbWVTaXRlOiAnbGF4JyxcclxuICAgICAgICBwYXRoOiAnLycsXHJcbiAgICAgICAgc2VjdXJlOiBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nXHJcbiAgICAgIH1cclxuICAgIH0sXHJcbiAgICBjYWxsYmFja1VybDoge1xyXG4gICAgICBuYW1lOiBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nXHJcbiAgICAgICAgPyBgX19TZWN1cmUtbmV4dC1hdXRoLmNhbGxiYWNrLXVybGBcclxuICAgICAgICA6IGBuZXh0LWF1dGguY2FsbGJhY2stdXJsYCxcclxuICAgICAgb3B0aW9uczoge1xyXG4gICAgICAgIHNhbWVTaXRlOiAnbGF4JyxcclxuICAgICAgICBwYXRoOiAnLycsXHJcbiAgICAgICAgc2VjdXJlOiBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nXHJcbiAgICAgIH1cclxuICAgIH0sXHJcbiAgICBjc3JmVG9rZW46IHtcclxuICAgICAgbmFtZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJ1xyXG4gICAgICAgID8gYF9fSG9zdC1uZXh0LWF1dGguY3NyZi10b2tlbmBcclxuICAgICAgICA6IGBuZXh0LWF1dGguY3NyZi10b2tlbmAsXHJcbiAgICAgIG9wdGlvbnM6IHtcclxuICAgICAgICBodHRwT25seTogdHJ1ZSxcclxuICAgICAgICBzYW1lU2l0ZTogJ2xheCcsXHJcbiAgICAgICAgcGF0aDogJy8nLFxyXG4gICAgICAgIHNlY3VyZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJ1xyXG4gICAgICB9XHJcbiAgICB9LFxyXG4gIH0sXHJcbiAgY2FsbGJhY2tzOiB7XHJcbiAgICBhc3luYyBqd3QoeyB0b2tlbiwgdXNlciwgYWNjb3VudCwgcHJvZmlsZSB9KSB7XHJcbiAgICAgIC8vIEhhbmRsZSBpbml0aWFsIHNpZ24gaW5cclxuICAgICAgaWYgKGFjY291bnQgJiYgdXNlcikge1xyXG4gICAgICAgIC8vIEZvciBPQXV0aCBwcm92aWRlcnMsIGNyZWF0ZS91cGRhdGUgdXNlciBpbiBkYXRhYmFzZVxyXG4gICAgICAgIGlmIChhY2NvdW50LnByb3ZpZGVyID09PSBcImdvb2dsZVwiKSB7XHJcbiAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zdCBlbWFpbCA9IHVzZXIuZW1haWwgfHwgKHByb2ZpbGUgYXMgYW55KT8uZW1haWw7XHJcbiAgICAgICAgICAgIGlmICghZW1haWwpIHtcclxuICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJObyBlbWFpbCBmb3VuZCBmb3IgR29vZ2xlIGFjY291bnRcIik7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC8vIENoZWNrIGlmIHVzZXIgZXhpc3RzXHJcbiAgICAgICAgICAgIGxldCBkYlVzZXIgPSBhd2FpdCBwcmlzbWEudXNlci5maW5kVW5pcXVlKHtcclxuICAgICAgICAgICAgICB3aGVyZTogeyBlbWFpbCB9LFxyXG4gICAgICAgICAgICAgIHNlbGVjdDogeyBpZDogdHJ1ZSwgcm9sZTogdHJ1ZSwgbmFtZTogdHJ1ZSwgZW1haWw6IHRydWUgfVxyXG4gICAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAgIC8vIENyZWF0ZSB1c2VyIGlmIGRvZXNuJ3QgZXhpc3RcclxuICAgICAgICAgICAgaWYgKCFkYlVzZXIpIHtcclxuICAgICAgICAgICAgICBkYlVzZXIgPSBhd2FpdCBwcmlzbWEudXNlci5jcmVhdGUoe1xyXG4gICAgICAgICAgICAgICAgZGF0YToge1xyXG4gICAgICAgICAgICAgICAgICBlbWFpbCxcclxuICAgICAgICAgICAgICAgICAgbmFtZTogdXNlci5uYW1lIHx8IChwcm9maWxlIGFzIGFueSk/Lm5hbWUgfHwgZW1haWwuc3BsaXQoJ0AnKVswXSxcclxuICAgICAgICAgICAgICAgICAgLy8gRG9uJ3Qgc2F2ZSBHb29nbGUgcHJvZmlsZSBwaWN0dXJlXHJcbiAgICAgICAgICAgICAgICAgIHJvbGU6IFVzZXJSb2xlLkNVU1RPTUVSLFxyXG4gICAgICAgICAgICAgICAgICBlbWFpbFZlcmlmaWVkOiBuZXcgRGF0ZSgpLCAvLyBPQXV0aCB1c2VycyBhcmUgY29uc2lkZXJlZCB2ZXJpZmllZFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIHNlbGVjdDogeyBpZDogdHJ1ZSwgcm9sZTogdHJ1ZSwgbmFtZTogdHJ1ZSwgZW1haWw6IHRydWUgfVxyXG4gICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBTZXQgdG9rZW4gcHJvcGVydGllc1xyXG4gICAgICAgICAgICB0b2tlbi5zdWIgPSBkYlVzZXIuaWQ7XHJcbiAgICAgICAgICAgIHRva2VuLnJvbGUgPSBkYlVzZXIucm9sZTtcclxuICAgICAgICAgICAgdG9rZW4uZW1haWwgPSBkYlVzZXIuZW1haWw7XHJcbiAgICAgICAgICAgIHRva2VuLm5hbWUgPSBkYlVzZXIubmFtZTtcclxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGhhbmRsaW5nIEdvb2dsZSBzaWduIGluOicsIGVycm9yKTtcclxuICAgICAgICAgICAgdGhyb3cgZXJyb3I7IC8vIFRoaXMgd2lsbCBwcmV2ZW50IHNpZ24gaW5cclxuICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2UgaWYgKHVzZXIpIHtcclxuICAgICAgICAgIC8vIEZvciBjcmVkZW50aWFscyBwcm92aWRlclxyXG4gICAgICAgICAgdG9rZW4uc3ViID0gdXNlci5pZDtcclxuICAgICAgICAgIHRva2VuLnJvbGUgPSAodXNlciBhcyBFeHRlbmRlZFVzZXIpLnJvbGU7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICByZXR1cm4gdG9rZW47XHJcbiAgICB9LFxyXG4gICAgYXN5bmMgc2Vzc2lvbih7IHNlc3Npb24sIHRva2VuIH0pIHtcclxuICAgICAgLy8gUG9wdWxhdGUgc2Vzc2lvbiB3aXRoIHVzZXIgZGF0YSBmcm9tIHRva2VuXHJcbiAgICAgIGlmICh0b2tlbiAmJiBzZXNzaW9uLnVzZXIpIHtcclxuICAgICAgICBzZXNzaW9uLnVzZXIgPSB7XHJcbiAgICAgICAgICAuLi5zZXNzaW9uLnVzZXIsXHJcbiAgICAgICAgICBpZDogdG9rZW4uc3ViIGFzIHN0cmluZyxcclxuICAgICAgICAgIHJvbGU6IHRva2VuLnJvbGUgYXMgc3RyaW5nLFxyXG4gICAgICAgICAgZW1haWw6IHRva2VuLmVtYWlsIGFzIHN0cmluZyxcclxuICAgICAgICAgIG5hbWU6IHRva2VuLm5hbWUgYXMgc3RyaW5nIHwgbnVsbCxcclxuICAgICAgICAgIC8vIERvbid0IGluY2x1ZGUgaW1hZ2UgZnJvbSBHb29nbGVcclxuICAgICAgICB9O1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICByZXR1cm4gc2Vzc2lvbjtcclxuICAgIH0sXHJcbiAgICBhc3luYyByZWRpcmVjdCh7IHVybCwgYmFzZVVybCB9KSB7XHJcbiAgICAgIC8vIEFsbG93cyByZWxhdGl2ZSBjYWxsYmFjayBVUkxzXHJcbiAgICAgIGlmICh1cmwuc3RhcnRzV2l0aChcIi9cIikpIHJldHVybiBgJHtiYXNlVXJsfSR7dXJsfWA7XHJcbiAgICAgIC8vIEFsbG93cyBjYWxsYmFjayBVUkxzIG9uIHRoZSBzYW1lIG9yaWdpblxyXG4gICAgICBlbHNlIGlmIChuZXcgVVJMKHVybCkub3JpZ2luID09PSBiYXNlVXJsKSByZXR1cm4gdXJsO1xyXG4gICAgICByZXR1cm4gYmFzZVVybDtcclxuICAgIH0sXHJcbiAgfSxcclxuICBldmVudHM6IHtcclxuICAgIGFzeW5jIHNpZ25Jbih7IHVzZXIsIGFjY291bnQsIHByb2ZpbGUsIGlzTmV3VXNlciB9KSB7XHJcbiAgICAgIC8vIExvZyBzaWduIGluIGV2ZW50cyBpbiBkZXZlbG9wbWVudCBvbmx5XHJcbiAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50Jykge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdTaWduIGluIGV2ZW50OicsIHtcclxuICAgICAgICAgIHVzZXJJZDogdXNlci5pZCxcclxuICAgICAgICAgIGVtYWlsOiB1c2VyLmVtYWlsLFxyXG4gICAgICAgICAgcHJvdmlkZXI6IGFjY291bnQ/LnByb3ZpZGVyLFxyXG4gICAgICAgICAgaXNOZXdVc2VyXHJcbiAgICAgICAgfSk7XHJcbiAgICAgIH1cclxuICAgIH0sXHJcbiAgfSxcclxuICBwYWdlczoge1xyXG4gICAgc2lnbkluOiBcIi9sb2dpblwiLFxyXG4gICAgc2lnbk91dDogXCIvXCIsXHJcbiAgICBlcnJvcjogXCIvbG9naW5cIixcclxuICB9LFxyXG4gIHNlY3JldDogcHJvY2Vzcy5lbnYuTkVYVEFVVEhfU0VDUkVULFxyXG4gIC8vIE9ubHkgZW5hYmxlIGRlYnVnIGluIGRldmVsb3BtZW50IHdoZW4gZXhwbGljaXRseSBzZXRcclxuICBkZWJ1ZzogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcgJiYgcHJvY2Vzcy5lbnYuTkVYVEFVVEhfREVCVUcgPT09ICd0cnVlJyxcclxufTsiXSwibmFtZXMiOlsiR29vZ2xlUHJvdmlkZXIiLCJDcmVkZW50aWFsc1Byb3ZpZGVyIiwiYmNyeXB0IiwicHJpc21hIiwiVXNlclJvbGUiLCJhdXRoT3B0aW9ucyIsInByb3ZpZGVycyIsImNsaWVudElkIiwicHJvY2VzcyIsImVudiIsIkdPT0dMRV9DTElFTlRfSUQiLCJjbGllbnRTZWNyZXQiLCJHT09HTEVfQ0xJRU5UX1NFQ1JFVCIsIm5hbWUiLCJjcmVkZW50aWFscyIsImVtYWlsIiwibGFiZWwiLCJ0eXBlIiwicGFzc3dvcmQiLCJhdXRob3JpemUiLCJFcnJvciIsInVzZXIiLCJmaW5kVW5pcXVlIiwid2hlcmUiLCJpc1Bhc3N3b3JkVmFsaWQiLCJjb21wYXJlIiwiaWQiLCJyb2xlIiwic2Vzc2lvbiIsInN0cmF0ZWd5IiwibWF4QWdlIiwidXBkYXRlQWdlIiwiand0IiwiY29va2llcyIsInNlc3Npb25Ub2tlbiIsIm9wdGlvbnMiLCJodHRwT25seSIsInNhbWVTaXRlIiwicGF0aCIsInNlY3VyZSIsImNhbGxiYWNrVXJsIiwiY3NyZlRva2VuIiwiY2FsbGJhY2tzIiwidG9rZW4iLCJhY2NvdW50IiwicHJvZmlsZSIsInByb3ZpZGVyIiwiZGJVc2VyIiwic2VsZWN0IiwiY3JlYXRlIiwiZGF0YSIsInNwbGl0IiwiQ1VTVE9NRVIiLCJlbWFpbFZlcmlmaWVkIiwiRGF0ZSIsInN1YiIsImVycm9yIiwiY29uc29sZSIsInJlZGlyZWN0IiwidXJsIiwiYmFzZVVybCIsInN0YXJ0c1dpdGgiLCJVUkwiLCJvcmlnaW4iLCJldmVudHMiLCJzaWduSW4iLCJpc05ld1VzZXIiLCJsb2ciLCJ1c2VySWQiLCJwYWdlcyIsInNpZ25PdXQiLCJzZWNyZXQiLCJORVhUQVVUSF9TRUNSRVQiLCJkZWJ1ZyIsIk5FWFRBVVRIX0RFQlVHIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"error\"\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQ1hGLGdCQUFnQkUsTUFBTSxJQUN0QixJQUFJSCx3REFBWUEsQ0FBQztJQUNmSSxLQUFLO1FBQUM7S0FBUTtBQUNoQixHQUFFO0FBRUosSUFBSUMsSUFBeUIsRUFBY0osZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL2FwcC9saWIvZGIudHM/NThmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz9cbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ2Vycm9yJ10sIC8vIE9ubHkgbG9nIGVycm9ycywgbm8gcXVlcmllcyBvciB3YXJuaW5nc1xuICB9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhomepage-settings%2Froute&page=%2Fapi%2Fhomepage-settings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhomepage-settings%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();