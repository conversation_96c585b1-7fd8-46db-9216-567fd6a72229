# 📁 Media Management System

A comprehensive media management solution for your Herbalicious admin panel, integrated with Cloudflare R2 for scalable file storage and delivery.

## 🚀 Features

### ✅ **File Upload & Management**
- **Drag & Drop Upload**: Intuitive file upload interface
- **Multiple File Support**: Upload multiple files simultaneously
- **File Validation**: Automatic type and size validation (10MB max)
- **Progress Indicators**: Real-time upload progress
- **Folder Organization**: Organize files in custom folders

### ✅ **File Types Supported**
- **Images**: JPEG, PNG, GIF, WebP, SVG
- **Videos**: MP4, WebM
- **Documents**: PDF
- **Size Limit**: 10MB per file

### ✅ **User Interface**
- **Grid View**: Visual thumbnail grid for easy browsing
- **List View**: Detailed list with file metadata
- **Search**: Find files by name instantly
- **Filter**: Filter by file type or folder
- **Preview**: Full-screen preview with metadata
- **Bulk Operations**: Select and delete multiple files

### ✅ **Cloudflare R2 Integration**
- **Direct Upload**: Files uploaded directly to R2 storage
- **CDN Delivery**: Fast global content delivery
- **Secure Storage**: Files stored with proper access controls
- **URL Generation**: Automatic public URL generation

## 🔧 Setup & Configuration

### **Environment Variables**
Add these to your `.env` file:

```env
# Cloudflare R2 Storage
R2_ACCESS_KEY_ID=dced285e4c983bbdc6ba3182c433f636
R2_SECRET_ACCESS_KEY=3f32c6e871ee796ea780fc784ff7a83f27cb3c2f29a27232f353387bcefdaf0b
R2_BUCKET_NAME=herbalicious-media
R2_ENDPOINT=https://images.herbalicous.in
R2_PUBLIC_URL=https://images.herbalicous.in
```

### **Dependencies**
The following packages are installed:
- `@aws-sdk/client-s3` - S3-compatible client for R2
- `@aws-sdk/s3-request-presigner` - For generating signed URLs

### **File Structure**
```
app/
├── admin/media/
│   ├── page.tsx           # Main media management page
│   └── test-upload.tsx    # Test component for R2 connection
├── api/media/
│   ├── upload/route.ts    # File upload API endpoint
│   ├── list/route.ts      # List files API endpoint
│   ├── delete/route.ts    # Delete files API endpoint
│   └── config/route.ts    # Configuration check endpoint
├── lib/
│   └── r2.ts             # R2 utility functions
└── components/admin/
    └── MediaPicker.tsx   # Reusable media picker component
```

## 📖 Usage Guide

### **Accessing Media Management**
1. Go to Admin Panel: `http://localhost:3000/admin`
2. Click on "Media" in the sidebar
3. Or directly visit: `http://localhost:3000/admin/media`

### **Uploading Files**
1. Click "Upload Files" button
2. Drag & drop files or click to select
3. Files are automatically uploaded to R2
4. View uploaded files in the grid/list

### **Managing Files**
- **Search**: Use the search bar to find specific files
- **Filter**: Filter by folder or file type
- **View Modes**: Switch between grid and list views
- **Preview**: Click the eye icon to preview files
- **Copy URL**: Click the copy icon to copy file URLs
- **Delete**: Select files and delete individually or in bulk

### **Using in Other Components**
Import and use the MediaPicker component:

```tsx
import MediaPicker from '../components/admin/MediaPicker';

const [showMediaPicker, setShowMediaPicker] = useState(false);
const [selectedImage, setSelectedImage] = useState<string>('');

const handleMediaSelect = (file: MediaFile) => {
  setSelectedImage(file.url);
};

return (
  <>
    <button onClick={() => setShowMediaPicker(true)}>
      Select Image
    </button>
    
    <MediaPicker
      isOpen={showMediaPicker}
      onClose={() => setShowMediaPicker(false)}
      onSelect={handleMediaSelect}
      allowedTypes={['image']}
      title="Select Product Image"
    />
  </>
);
```

## 🔌 API Endpoints

### **Upload File**
```
POST /api/media/upload
Content-Type: multipart/form-data

Body:
- file: File to upload
- folder: Target folder (optional)
```

### **List Files**
```
GET /api/media/list?folder=uploads&maxKeys=100

Response:
{
  "success": true,
  "files": [...],
  "count": 10
}
```

### **Delete File**
```
DELETE /api/media/delete
Content-Type: application/json

Body:
{
  "key": "uploads/1234567890_image.jpg"
}
```

### **Check Configuration**
```
GET /api/media/config

Response:
{
  "success": true,
  "config": {...},
  "ready": true
}
```

## 🛠 Technical Details

### **R2 Configuration**
- **Endpoint**: Uses S3-compatible API
- **Authentication**: Access key and secret key
- **Bucket**: Dedicated bucket for media files
- **Public Access**: Files are publicly accessible via CDN

### **File Processing**
- **Validation**: File type and size validation
- **Naming**: Timestamp-based naming to prevent conflicts
- **Organization**: Folder-based organization system
- **Metadata**: Stores file size, type, and modification date

### **Security**
- **File Type Validation**: Only allowed file types accepted
- **Size Limits**: 10MB maximum file size
- **Secure Upload**: Direct upload to R2 with validation

## 🎯 Best Practices

### **File Organization**
- Use descriptive folder names (e.g., 'products', 'banners', 'blog')
- Keep file names descriptive and SEO-friendly
- Regularly clean up unused files

### **Performance**
- Use appropriate image formats (WebP for modern browsers)
- Optimize file sizes before upload
- Use CDN URLs for better performance

### **SEO**
- Use descriptive file names
- Include alt text when using images
- Optimize file sizes for faster loading

## 🔍 Troubleshooting

### **Upload Issues**
1. Check R2 credentials in `.env` file
2. Verify bucket permissions
3. Check file size (must be under 10MB)
4. Ensure file type is supported

### **Configuration Check**
Visit `/api/media/config` to verify:
- R2 credentials are set
- Bucket name is configured
- Endpoint URLs are correct

### **Common Errors**
- **"Upload failed"**: Check R2 credentials and bucket permissions
- **"File type not supported"**: Verify file type is in allowed list
- **"File too large"**: Reduce file size to under 10MB

## 🚀 Future Enhancements

### **Planned Features**
- **Image Resizing**: Automatic image optimization and resizing
- **Video Thumbnails**: Generate thumbnails for video files
- **Advanced Search**: Search by metadata, tags, and dates
- **File Versioning**: Keep track of file versions
- **Batch Upload**: Upload entire folders
- **Image Editor**: Basic image editing capabilities

---

**Status**: ✅ **Ready for Production**

The media management system is fully functional and ready for use with your Herbalicious admin panel. All R2 integration is configured and tested.
