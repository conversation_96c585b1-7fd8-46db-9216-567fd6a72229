const fs = require('fs');
const path = require('path');

// Product short descriptions mapping
const shortDescriptions = {
  'Anti-Pimple Pack': 'Targets acne and blemishes for clear, smooth skin.',
  'Anti Ageing Gel Creme': 'Fights signs of ageing and restores youthful glow.',
  'Baby Hair Oil': 'Gentle nourishment for soft, healthy baby hair.',
  'Beard Oil': 'Softens beard and promotes fuller, healthier growth.',
  'Body Polishing & Glow Massage Oil': 'Enhances skin radiance with deep nourishment.',
  'Bridal Face Ubtan': 'Brightens and preps skin for a radiant bridal glow.',
  'Chiksa': 'Traditional skincare blend for deep cleansing and glow.',
  'Deep Clean & Polishing Face Scrub': 'Exfoliates to remove dead skin and impurities.',
  'Eyebrow Thickening & Lash Lengthening Oil': 'Strengthens brows and lashes naturally.',
  'Face Elixir Oil': 'Revives dull skin with intense hydration and glow.',
  'Face Ubtan': 'Natural cleanser for glowing, refreshed skin.',
  'Glitter Lip Balm': 'Moisturizes lips with a subtle glittery shine.',
  'Gold Dust Skin Tonic': 'Refreshes skin with a golden glow boost.',
  'Hair Growth & Shine Mask': 'Nourishes roots for stronger, shinier hair.',
  'Kumkumadi Face Oil': 'Ayurvedic elixir for glowing, even-toned skin.',
  'Luxury Moisturizing Milk Cream': 'Rich hydration for soft, supple skin.',
  'Maharani Hair Oil': 'Royal blend for stronger, longer, healthier hair.',
  'Neem Face Wash': 'Clears acne and purifies with neem power.',
  'Papaya Face Wash': 'Gently exfoliates and brightens dull skin.',
  'Royal Facial Kit': 'Complete facial care for radiant, refreshed skin.',
  'Saffron Gel': 'Brightens complexion with the richness of saffron.',
  'Saffron Night Repair Cream': 'Repairs and nourishes skin overnight.',
  '24K Night Care': 'Luxurious night duo for glowing, youthful skin.',
  'Snow Face Cleanser': 'Refreshes and deeply cleanses skin gently.',
  'Strawberry Silk Booster': 'Boosts skin softness and fruity radiance.',
  'Supermane Hair Oil': 'Strengthens hair and controls hair fall naturally.',
  'Under-Eye Gel': 'Reduces dark circles and puffiness.',
  'Vitamin-C Serum': 'Brightens skin tone and fades dark spots.'
};

async function updateProductShortDescriptions() {
  try {
    console.log('🚀 Starting product short description update...\n');

    // Read the current products file
    const productsPath = path.join(process.cwd(), 'csvjson-updated.json');
    if (!fs.existsSync(productsPath)) {
      console.error('❌ Products file not found:', productsPath);
      process.exit(1);
    }

    const fileData = JSON.parse(fs.readFileSync(productsPath, 'utf8'));
    
    let productsData;
    if (Array.isArray(fileData)) {
      productsData = fileData;
    } else if (fileData.products && Array.isArray(fileData.products)) {
      productsData = fileData.products;
    } else {
      console.error('❌ Invalid products file format. Expected an array or { products: [...] }');
      process.exit(1);
    }

    let updatedCount = 0;
    let skippedCount = 0;

    // Update products with short descriptions
    const updatedProducts = productsData.map(product => {
      const productName = product.name?.trim();
      
      if (!productName) {
        console.warn(`⚠️  Skipping product with no name:`, product);
        skippedCount++;
        return product;
      }

      // Find matching short description
      let shortDescription = '';
      
      // Exact match first
      if (shortDescriptions[productName]) {
        shortDescription = shortDescriptions[productName];
      } else {
        // Fuzzy match - check if any key is contained in the product name
        for (const [key, value] of Object.entries(shortDescriptions)) {
          if (productName.toLowerCase().includes(key.toLowerCase()) || 
              key.toLowerCase().includes(productName.toLowerCase())) {
            shortDescription = value;
            break;
          }
        }
      }

      if (shortDescription) {
        updatedCount++;
        console.log(`✅ Updated: ${productName} -> ${shortDescription}`);
        return {
          ...product,
          shortDescription: shortDescription
        };
      } else {
        console.log(`⚠️  No match found for: ${productName}`);
        skippedCount++;
        return {
          ...product,
          shortDescription: product.shortDescription || ''
        };
      }
    });

    // Write updated products back to file
    const outputPath = path.join(process.cwd(), 'csvjson-updated.json');
    
    // Preserve original structure
    const outputData = fileData.products ? { products: updatedProducts } : updatedProducts;
    fs.writeFileSync(outputPath, JSON.stringify(outputData, null, 2), 'utf8');

    console.log('\n📊 Update Summary:');
    console.log(`✅ Products updated: ${updatedCount}`);
    console.log(`⚠️  Products skipped: ${skippedCount}`);
    console.log(`📁 File saved: ${outputPath}`);

    // Create a backup of the original
    const backupPath = path.join(process.cwd(), 'csvjson-backup.json');
    fs.writeFileSync(backupPath, JSON.stringify(fileData, null, 2), 'utf8');
    console.log(`💾 Backup created: ${backupPath}`);

  } catch (error) {
    console.error('❌ Error updating product descriptions:', error);
    process.exit(1);
  }
}

// Run the update
updateProductShortDescriptions();