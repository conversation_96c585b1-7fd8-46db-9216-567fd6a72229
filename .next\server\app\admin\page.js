/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/page";
exports.ids = ["app/admin/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/page.tsx */ \"(rsc)/./app/admin/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/layout.tsx */ \"(rsc)/./app/admin/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/admin/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/page\",\n        pathname: \"/admin\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cadmin%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cadmin%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/layout.tsx */ \"(ssr)/./app/admin/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDcHJvamVjdCU1QyU1Q2FwcCU1QyU1Q2FkbWluJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0pBQThGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8/OWMxYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcRGVza3RvcFxcXFxwcm9qZWN0XFxcXGFwcFxcXFxhZG1pblxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cadmin%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/page.tsx */ \"(ssr)/./app/admin/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDcHJvamVjdCU1QyU1Q2FwcCU1QyU1Q2FkbWluJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUE0RiIsInNvdXJjZXMiOlsid2VicGFjazovL2hlcmJhbGljaW91cy1uZXh0anMvP2ExZDIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxVc2VyXFxcXERlc2t0b3BcXFxccHJvamVjdFxcXFxhcHBcXFxcYWRtaW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CFlashSaleContext.tsx%22%2C%22ids%22%3A%5B%22FlashSaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CFlashSaleContext.tsx%22%2C%22ids%22%3A%5B%22FlashSaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/CartContext.tsx */ \"(ssr)/./app/context/CartContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/FlashSaleContext.tsx */ \"(ssr)/./app/context/FlashSaleContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/NotificationContext.tsx */ \"(ssr)/./app/context/NotificationContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/SessionProvider.tsx */ \"(ssr)/./app/context/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CCartContext.tsx%22%2C%22ids%22%3A%5B%22CartProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CFlashSaleContext.tsx%22%2C%22ids%22%3A%5B%22FlashSaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Ccontext%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDcHJvamVjdCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2FwcC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVXNlciU1QyU1Q0Rlc2t0b3AlNUMlNUNwcm9qZWN0JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVXNlciU1QyU1Q0Rlc2t0b3AlNUMlNUNwcm9qZWN0JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVXNlciU1QyU1Q0Rlc2t0b3AlNUMlNUNwcm9qZWN0JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNVc2VyJTVDJTVDRGVza3RvcCU1QyU1Q3Byb2plY3QlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNub3QtZm91bmQtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVXNlciU1QyU1Q0Rlc2t0b3AlNUMlNUNwcm9qZWN0JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQW1JO0FBQ25JO0FBQ0Esb09BQW9JO0FBQ3BJO0FBQ0EsME9BQXVJO0FBQ3ZJO0FBQ0Esd09BQXNJO0FBQ3RJO0FBQ0Esa1BBQTJJO0FBQzNJO0FBQ0Esc1FBQXFKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8/Mzk4MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcRGVza3RvcFxcXFxwcm9qZWN0XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcYXBwLXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxEZXNrdG9wXFxcXHByb2plY3RcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxEZXNrdG9wXFxcXHByb2plY3RcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxEZXNrdG9wXFxcXHByb2plY3RcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxVc2VyXFxcXERlc2t0b3BcXFxccHJvamVjdFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG5vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxEZXNrdG9wXFxcXHByb2plY3RcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDesktop%5C%5Cproject%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/admin/layout.tsx":
/*!******************************!*\
  !*** ./app/admin/layout.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Image,LayoutDashboard,Leaf,LogOut,Mail,Menu,MessageSquare,Package,Settings,ShoppingBag,ShoppingCart,Tag,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Image,LayoutDashboard,Leaf,LogOut,Mail,Menu,MessageSquare,Package,Settings,ShoppingBag,ShoppingCart,Tag,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Image,LayoutDashboard,Leaf,LogOut,Mail,Menu,MessageSquare,Package,Settings,ShoppingBag,ShoppingCart,Tag,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Image,LayoutDashboard,Leaf,LogOut,Mail,Menu,MessageSquare,Package,Settings,ShoppingBag,ShoppingCart,Tag,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Image,LayoutDashboard,Leaf,LogOut,Mail,Menu,MessageSquare,Package,Settings,ShoppingBag,ShoppingCart,Tag,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Image,LayoutDashboard,Leaf,LogOut,Mail,Menu,MessageSquare,Package,Settings,ShoppingBag,ShoppingCart,Tag,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Image,LayoutDashboard,Leaf,LogOut,Mail,Menu,MessageSquare,Package,Settings,ShoppingBag,ShoppingCart,Tag,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Image,LayoutDashboard,Leaf,LogOut,Mail,Menu,MessageSquare,Package,Settings,ShoppingBag,ShoppingCart,Tag,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Image,LayoutDashboard,Leaf,LogOut,Mail,Menu,MessageSquare,Package,Settings,ShoppingBag,ShoppingCart,Tag,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Image,LayoutDashboard,Leaf,LogOut,Mail,Menu,MessageSquare,Package,Settings,ShoppingBag,ShoppingCart,Tag,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Image,LayoutDashboard,Leaf,LogOut,Mail,Menu,MessageSquare,Package,Settings,ShoppingBag,ShoppingCart,Tag,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Image,LayoutDashboard,Leaf,LogOut,Mail,Menu,MessageSquare,Package,Settings,ShoppingBag,ShoppingCart,Tag,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Image,LayoutDashboard,Leaf,LogOut,Mail,Menu,MessageSquare,Package,Settings,ShoppingBag,ShoppingCart,Tag,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Image,LayoutDashboard,Leaf,LogOut,Mail,Menu,MessageSquare,Package,Settings,ShoppingBag,ShoppingCart,Tag,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Image,LayoutDashboard,Leaf,LogOut,Mail,Menu,MessageSquare,Package,Settings,ShoppingBag,ShoppingCart,Tag,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/leaf.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Home,Image,LayoutDashboard,Leaf,LogOut,Mail,Menu,MessageSquare,Package,Settings,ShoppingBag,ShoppingCart,Tag,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst AdminLayout = ({ children })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [sidebarOpen, setSidebarOpen] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const [isLoggingOut, setIsLoggingOut] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const handleLogout = async ()=>{\n        if (isLoggingOut) return; // Prevent double-clicks\n        try {\n            setIsLoggingOut(true);\n            // Clear any local state immediately\n            setSidebarOpen(false);\n            // Sign out without NextAuth redirect handling\n            await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signOut)({\n                redirect: false,\n                callbackUrl: \"/\"\n            });\n            // Small delay to ensure signOut completes\n            setTimeout(()=>{\n                window.location.replace(\"/\");\n            }, 100);\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n            // Force redirect even if signOut fails\n            window.location.replace(\"/\");\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/admin\",\n            label: \"Dashboard\",\n            icon: _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            href: \"/admin/products\",\n            label: \"Products\",\n            icon: _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            href: \"/admin/categories\",\n            label: \"Categories\",\n            icon: _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            href: \"/admin/orders\",\n            label: \"Orders\",\n            icon: _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            href: \"/admin/customers\",\n            label: \"Customers\",\n            icon: _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            href: \"/admin/coupons\",\n            label: \"Coupons\",\n            icon: _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            href: \"/admin/reviews\",\n            label: \"Reviews\",\n            icon: _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            href: \"/admin/notifications\",\n            label: \"Notifications\",\n            icon: _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            href: \"/admin/newsletter\",\n            label: \"Newsletter\",\n            icon: _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            href: \"/admin/media\",\n            label: \"Media\",\n            icon: _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            href: \"/admin/homepage\",\n            label: \"Homepage\",\n            icon: _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        },\n        {\n            href: \"/admin/settings\",\n            label: \"Settings\",\n            icon: _barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/admin\") {\n            return pathname === \"/admin\";\n        }\n        return pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed top-4 left-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                    className: \"p-2 bg-white rounded-lg shadow-md hover:bg-gray-50 transition-colors\",\n                    children: sidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        className: \"w-6 h-6 text-gray-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 26\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"w-6 h-6 text-gray-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 68\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: `fixed inset-y-0 left-0 z-40 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out ${sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"} lg:translate-x-0`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-16 px-6 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: \"Herbalicious\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"Admin Panel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"mt-6 px-3\",\n                        children: menuItems.map((item)=>{\n                            const Icon = item.icon;\n                            const active = isActive(item.href);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: item.href,\n                                className: `flex items-center px-3 py-2 mb-1 text-sm font-medium rounded-lg transition-colors ${active ? \"bg-green-50 text-green-700 border-r-2 border-green-600\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"}`,\n                                onClick: ()=>setSidebarOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: `w-5 h-5 mr-3 ${active ? \"text-green-600\" : \"text-gray-400\"}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    item.label\n                                ]\n                            }, item.href, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 w-full p-4 border-t border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"flex items-center justify-center w-full px-4 py-2 mb-2 text-sm text-gray-600 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\",\n                                children: \"Back to Store\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleLogout,\n                                disabled: isLoggingOut,\n                                className: \"flex items-center justify-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Home_Image_LayoutDashboard_Leaf_LogOut_Mail_Menu_MessageSquare_Package_Settings_ShoppingBag_ShoppingCart_Tag_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isLoggingOut ? \"Logging out...\" : \"Logout\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"lg:ml-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 lg:p-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, undefined),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n                lineNumber: 165,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\layout.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AdminLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/admin/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,Package,ShoppingBag,ShoppingCart,Star,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,Package,ShoppingBag,ShoppingCart,Star,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,Package,ShoppingBag,ShoppingCart,Star,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,Package,ShoppingBag,ShoppingCart,Star,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,Package,ShoppingBag,ShoppingCart,Star,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,Package,ShoppingBag,ShoppingCart,Star,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,Package,ShoppingBag,ShoppingCart,Star,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,Package,ShoppingBag,ShoppingCart,Star,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,Package,ShoppingBag,ShoppingCart,Star,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _lib_currency__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/currency */ \"(ssr)/./app/lib/currency.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst AdminDashboard = ()=>{\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchDashboardData();\n    }, []);\n    const fetchDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/dashboard/stats\");\n            const data = await response.json();\n            if (data.success) {\n                setDashboardData(data.data);\n            } else {\n                setError(\"Failed to fetch dashboard data\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching dashboard data:\", error);\n            setError(\"Failed to fetch dashboard data\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-green-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-xl p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-800\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: fetchDashboardData,\n                            className: \"mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium\",\n                            children: \"Try again\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, undefined);\n    }\n    const stats = [\n        {\n            title: \"Total Products\",\n            value: dashboardData?.overview.totalProducts || 0,\n            change: dashboardData?.growth.productsGrowth || \"+0%\",\n            trend: \"up\",\n            icon: _barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            title: \"Total Categories\",\n            value: dashboardData?.overview.totalCategories || 0,\n            change: dashboardData?.growth.categoriesGrowth || \"+0%\",\n            trend: \"up\",\n            icon: _barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            title: \"Total Users\",\n            value: dashboardData?.overview.totalUsers || 0,\n            change: dashboardData?.growth.usersGrowth || \"+0%\",\n            trend: \"up\",\n            icon: _barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            title: \"Featured Products\",\n            value: dashboardData?.overview.featuredProducts || 0,\n            change: \"+0%\",\n            trend: \"up\",\n            icon: _barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        }\n    ];\n    const recentProducts = dashboardData?.recent.products || [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: \"Welcome back! Here's what's happening with your store.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                    children: stats.map((stat, index)=>{\n                        const Icon = stat.icon;\n                        const TrendIcon = stat.trend === \"up\" ? _barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"] : _barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: stat.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stat.value\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"w-8 h-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrendIcon, {\n                                            className: `w-4 h-4 mr-1 ${stat.trend === \"up\" ? \"text-green-600\" : \"text-red-600\"}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-sm font-medium ${stat.trend === \"up\" ? \"text-green-600\" : \"text-red-600\"}`,\n                                            children: stat.change\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500 ml-1\",\n                                            children: \"vs last month\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: \"Recent Orders\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-green-600 hover:text-green-700 text-sm font-medium\",\n                                                children: \"View all\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"No orders yet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Orders will appear here once customers start purchasing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: \"Recent Products\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-green-600 hover:text-green-700 text-sm font-medium\",\n                                                children: \"View all\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: recentProducts.length > 0 ? recentProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: product.images[0]?.url || \"/images/default-product.jpg\",\n                                                                alt: product.name,\n                                                                className: \"w-12 h-12 rounded-lg object-cover mr-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium text-gray-900\",\n                                                                        children: product.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 192,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center mt-1 space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: product.category.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 194,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"Stock: \",\n                                                                                    product.quantity\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 195,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 193,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.formatPrice)(product.price)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            product.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-700 border border-green-200 mt-1\",\n                                                                children: \"Featured\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, product.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 21\n                                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_Package_ShoppingBag_ShoppingCart_Star_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                    children: \"No products yet\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Add products to see them here\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AdminDashboard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/admin/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/CartContext.tsx":
/*!*************************************!*\
  !*** ./app/context/CartContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: () => (/* binding */ CartProvider),\n/* harmony export */   useCart: () => (/* binding */ useCart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ CartProvider,useCart auto */ \n\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\n// localStorage utilities\nconst CART_STORAGE_KEY = \"herbalicious_cart\";\nconst saveCartToStorage = (state)=>{\n    try {\n        if (false) {}\n    } catch (error) {\n        console.error(\"Error saving cart to localStorage:\", error);\n    }\n};\nconst loadCartFromStorage = ()=>{\n    try {\n        if (false) {}\n    } catch (error) {\n        console.error(\"Error loading cart from localStorage:\", error);\n    }\n    return null;\n};\n// Helper function to generate unique variant key\nconst generateVariantKey = (productId, selectedVariants)=>{\n    if (!selectedVariants || selectedVariants.length === 0) {\n        return productId;\n    }\n    // Sort variants by name to ensure consistent key generation\n    const sortedVariants = [\n        ...selectedVariants\n    ].sort((a, b)=>a.name.localeCompare(b.name));\n    const variantString = sortedVariants.map((v)=>`${v.name}:${v.value}`).join(\"|\");\n    return `${productId}__${variantString}`;\n};\n// Helper function to get item identifier (with fallback for backward compatibility)\nconst getItemIdentifier = (item)=>{\n    return item.variantKey || item.product?.id || item.id;\n};\nconst getInitialCartState = ()=>{\n    const storedCart = loadCartFromStorage();\n    if (storedCart) {\n        return storedCart;\n    }\n    return {\n        items: [],\n        total: 0,\n        subtotal: 0,\n        itemCount: 0,\n        finalTotal: 0,\n        coupons: {\n            appliedCoupons: [],\n            totalDiscount: 0,\n            availableCoupons: []\n        }\n    };\n};\nconst calculateTotals = (items, appliedCoupons)=>{\n    const subtotal = items.reduce((sum, item)=>sum + item.product.price * item.quantity, 0);\n    const itemCount = items.reduce((sum, item)=>sum + item.quantity, 0);\n    const totalDiscount = appliedCoupons.reduce((sum, coupon)=>sum + coupon.discountAmount, 0);\n    const finalTotal = subtotal - totalDiscount;\n    return {\n        subtotal,\n        itemCount,\n        total: subtotal,\n        finalTotal,\n        totalDiscount\n    };\n};\nconst cartReducer = (state, action)=>{\n    let newState;\n    switch(action.type){\n        case \"ADD_ITEM\":\n            {\n                const variantKey = generateVariantKey(action.payload.id, action.selectedVariants);\n                const existingItem = state.items.find((item)=>getItemIdentifier(item) === variantKey);\n                let updatedItems;\n                if (existingItem) {\n                    // Same product with same variants - increase quantity\n                    updatedItems = state.items.map((item)=>getItemIdentifier(item) === variantKey ? {\n                            ...item,\n                            quantity: item.quantity + 1,\n                            variantKey\n                        } : item);\n                } else {\n                    // New product or different variant combination - add as new item\n                    const newCartItem = {\n                        product: action.payload,\n                        quantity: 1,\n                        selectedVariants: action.selectedVariants || [],\n                        variantKey\n                    };\n                    updatedItems = [\n                        ...state.items,\n                        newCartItem\n                    ];\n                }\n                const totals = calculateTotals(updatedItems, state.coupons.appliedCoupons);\n                newState = {\n                    ...state,\n                    items: updatedItems,\n                    ...totals,\n                    coupons: {\n                        ...state.coupons,\n                        totalDiscount: totals.totalDiscount\n                    }\n                };\n                break;\n            }\n        case \"REMOVE_ITEM\":\n            {\n                const filteredItems = state.items.filter((item)=>getItemIdentifier(item) !== action.payload);\n                const totals = calculateTotals(filteredItems, state.coupons.appliedCoupons);\n                newState = {\n                    ...state,\n                    items: filteredItems,\n                    ...totals,\n                    coupons: {\n                        ...state.coupons,\n                        totalDiscount: totals.totalDiscount\n                    }\n                };\n                break;\n            }\n        case \"UPDATE_QUANTITY\":\n            {\n                const updatedItems = state.items.map((item)=>getItemIdentifier(item) === action.payload.id ? {\n                        ...item,\n                        quantity: action.payload.quantity\n                    } : item).filter((item)=>item.quantity > 0);\n                const totals = calculateTotals(updatedItems, state.coupons.appliedCoupons);\n                newState = {\n                    ...state,\n                    items: updatedItems,\n                    ...totals,\n                    coupons: {\n                        ...state.coupons,\n                        totalDiscount: totals.totalDiscount\n                    }\n                };\n                break;\n            }\n        case \"APPLY_COUPON\":\n            {\n                // Check if coupon is already applied\n                const isAlreadyApplied = state.coupons.appliedCoupons.some((coupon)=>coupon.coupon.id === action.payload.coupon.id);\n                if (isAlreadyApplied) {\n                    return state;\n                }\n                // Check stacking rules\n                const hasNonStackableCoupon = state.coupons.appliedCoupons.some((coupon)=>!coupon.coupon.isStackable);\n                if (hasNonStackableCoupon && !action.payload.coupon.isStackable) {\n                    return state;\n                }\n                const updatedAppliedCoupons = [\n                    ...state.coupons.appliedCoupons,\n                    action.payload\n                ];\n                const totals = calculateTotals(state.items, updatedAppliedCoupons);\n                newState = {\n                    ...state,\n                    ...totals,\n                    coupons: {\n                        ...state.coupons,\n                        appliedCoupons: updatedAppliedCoupons,\n                        totalDiscount: totals.totalDiscount\n                    }\n                };\n                break;\n            }\n        case \"REMOVE_COUPON\":\n            {\n                const updatedAppliedCoupons = state.coupons.appliedCoupons.filter((coupon)=>coupon.coupon.id !== action.payload);\n                const totals = calculateTotals(state.items, updatedAppliedCoupons);\n                newState = {\n                    ...state,\n                    ...totals,\n                    coupons: {\n                        ...state.coupons,\n                        appliedCoupons: updatedAppliedCoupons,\n                        totalDiscount: totals.totalDiscount\n                    }\n                };\n                break;\n            }\n        case \"CLEAR_COUPONS\":\n            {\n                const totals = calculateTotals(state.items, []);\n                newState = {\n                    ...state,\n                    ...totals,\n                    coupons: {\n                        appliedCoupons: [],\n                        totalDiscount: 0,\n                        availableCoupons: []\n                    }\n                };\n                break;\n            }\n        case \"CLEAR_CART\":\n            {\n                newState = {\n                    items: [],\n                    total: 0,\n                    subtotal: 0,\n                    itemCount: 0,\n                    finalTotal: 0,\n                    coupons: {\n                        appliedCoupons: [],\n                        totalDiscount: 0,\n                        availableCoupons: []\n                    }\n                };\n                break;\n            }\n        default:\n            return state;\n    }\n    // Save to localStorage after state change\n    saveCartToStorage(newState);\n    return newState;\n};\nconst CartProvider = ({ children })=>{\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(cartReducer, getInitialCartState());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: {\n            state,\n            dispatch\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\CartContext.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, undefined);\n};\nconst useCart = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (!context) {\n        throw new Error(\"useCart must be used within a CartProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/context/CartContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/FlashSaleContext.tsx":
/*!******************************************!*\
  !*** ./app/context/FlashSaleContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FlashSaleProvider: () => (/* binding */ FlashSaleProvider),\n/* harmony export */   useFlashSale: () => (/* binding */ useFlashSale)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ FlashSaleProvider,useFlashSale auto */ \n\nconst FlashSaleContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction FlashSaleProvider({ children }) {\n    const [flashSaleSettings, setFlashSaleSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchFlashSaleSettings = async ()=>{\n        try {\n            const response = await fetch(\"/api/homepage-settings\");\n            const data = await response.json();\n            if (data.success && data.data.settings) {\n                const settings = data.data.settings;\n                setFlashSaleSettings({\n                    showFlashSale: settings.showFlashSale,\n                    flashSaleEndDate: settings.flashSaleEndDate,\n                    flashSalePercentage: settings.flashSalePercentage,\n                    flashSaleTitle: settings.flashSaleTitle,\n                    flashSaleSubtitle: settings.flashSaleSubtitle,\n                    flashSaleBackgroundColor: settings.flashSaleBackgroundColor\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching flash sale settings:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchFlashSaleSettings();\n    }, []);\n    const refreshSettings = async ()=>{\n        setLoading(true);\n        await fetchFlashSaleSettings();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FlashSaleContext.Provider, {\n        value: {\n            flashSaleSettings,\n            loading,\n            refreshSettings\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\FlashSaleContext.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\nfunction useFlashSale() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(FlashSaleContext);\n    if (context === undefined) {\n        throw new Error(\"useFlashSale must be used within a FlashSaleProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/context/FlashSaleContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/NotificationContext.tsx":
/*!*********************************************!*\
  !*** ./app/context/NotificationContext.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useNotifications,NotificationProvider,default auto */ \n\n\nconst NotificationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useNotifications = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationContext);\n    if (context === undefined) {\n        throw new Error(\"useNotifications must be used within a NotificationProvider\");\n    }\n    return context;\n};\nconst NotificationProvider = ({ children })=>{\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fetchNotifications = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (options = {})=>{\n        if (!session?.user?.id) return;\n        try {\n            setLoading(true);\n            setError(null);\n            const params = new URLSearchParams({\n                page: (options.page || 1).toString(),\n                limit: (options.limit || 10).toString(),\n                ...options.unreadOnly && {\n                    unreadOnly: \"true\"\n                }\n            });\n            const response = await fetch(`/api/notifications?${params}`);\n            const data = await response.json();\n            if (data.success) {\n                setNotifications(data.data.notifications);\n                setUnreadCount(data.data.unreadCount);\n            } else {\n                setError(data.error || \"Failed to fetch notifications\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching notifications:\", error);\n            setError(\"Failed to fetch notifications\");\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        session?.user?.id\n    ]);\n    const refreshUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!session?.user?.id) return;\n        try {\n            const response = await fetch(\"/api/notifications/unread-count\");\n            const data = await response.json();\n            if (data.success) {\n                setUnreadCount(data.unreadCount);\n            }\n        } catch (error) {\n            console.error(\"Error fetching unread count:\", error);\n        }\n    }, [\n        session?.user?.id\n    ]);\n    const markAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (notificationId)=>{\n        if (!session?.user?.id) return;\n        try {\n            const response = await fetch(`/api/notifications/${notificationId}/read`, {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update local state\n                setNotifications((prev)=>prev.map((notification)=>notification.id === notificationId ? {\n                            ...notification,\n                            isRead: true\n                        } : notification));\n                // Update unread count\n                setUnreadCount((prev)=>Math.max(0, prev - 1));\n            } else {\n                setError(data.error || \"Failed to mark notification as read\");\n            }\n        } catch (error) {\n            console.error(\"Error marking notification as read:\", error);\n            setError(\"Failed to mark notification as read\");\n        }\n    }, [\n        session?.user?.id\n    ]);\n    const markAllAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!session?.user?.id) return;\n        try {\n            const response = await fetch(\"/api/notifications/mark-all-read\", {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update local state\n                setNotifications((prev)=>prev.map((notification)=>({\n                            ...notification,\n                            isRead: true\n                        })));\n                setUnreadCount(0);\n            } else {\n                setError(data.error || \"Failed to mark all notifications as read\");\n            }\n        } catch (error) {\n            console.error(\"Error marking all notifications as read:\", error);\n            setError(\"Failed to mark all notifications as read\");\n        }\n    }, [\n        session?.user?.id\n    ]);\n    // Fetch notifications when user logs in\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"authenticated\" && session?.user?.id) {\n            fetchNotifications({\n                limit: 5\n            }); // Fetch recent notifications for dropdown\n            refreshUnreadCount();\n        }\n    }, [\n        status,\n        session?.user?.id,\n        fetchNotifications,\n        refreshUnreadCount\n    ]);\n    // Refresh unread count periodically (every 30 seconds)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!session?.user?.id) return;\n        const interval = setInterval(()=>{\n            refreshUnreadCount();\n        }, 30000); // 30 seconds\n        return ()=>clearInterval(interval);\n    }, [\n        session?.user?.id,\n        refreshUnreadCount\n    ]);\n    const value = {\n        notifications,\n        unreadCount,\n        loading,\n        error,\n        fetchNotifications,\n        markAsRead,\n        markAllAsRead,\n        refreshUnreadCount\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\NotificationContext.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NotificationProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/context/NotificationContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/SessionProvider.tsx":
/*!*****************************************!*\
  !*** ./app/context/SessionProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthSessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AuthSessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\context\\\\SessionProvider.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29udGV4dC9TZXNzaW9uUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVrRDtBQU9uQyxTQUFTQyxvQkFBb0IsRUFBRUMsUUFBUSxFQUFTO0lBQzdELHFCQUFPLDhEQUFDRiw0REFBZUE7a0JBQUVFOzs7Ozs7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vYXBwL2NvbnRleHQvU2Vzc2lvblByb3ZpZGVyLnRzeD82MTQxIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XHJcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmludGVyZmFjZSBQcm9wcyB7XHJcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXV0aFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFByb3BzKSB7XHJcbiAgcmV0dXJuIDxTZXNzaW9uUHJvdmlkZXI+e2NoaWxkcmVufTwvU2Vzc2lvblByb3ZpZGVyPjtcclxufSJdLCJuYW1lcyI6WyJTZXNzaW9uUHJvdmlkZXIiLCJBdXRoU2Vzc2lvblByb3ZpZGVyIiwiY2hpbGRyZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/context/SessionProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./app/lib/currency.ts":
/*!*****************************!*\
  !*** ./app/lib/currency.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   formatPriceWhole: () => (/* binding */ formatPriceWhole),\n/* harmony export */   formatRupee: () => (/* binding */ formatRupee),\n/* harmony export */   formatStock: () => (/* binding */ formatStock),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getCurrencyCode: () => (/* binding */ getCurrencyCode),\n/* harmony export */   getCurrencySymbol: () => (/* binding */ getCurrencySymbol),\n/* harmony export */   getStockColor: () => (/* binding */ getStockColor),\n/* harmony export */   getStockStatus: () => (/* binding */ getStockStatus),\n/* harmony export */   validateSlug: () => (/* binding */ validateSlug)\n/* harmony export */ });\n// Currency utility functions for Indian Rupee (₹)\n/**\n * Format a number as Indian Rupee currency\n * @param amount - The amount to format\n * @param showDecimals - Whether to show decimal places (default: true)\n * @returns Formatted currency string\n */ function formatCurrency(amount, showDecimals = true) {\n    if (isNaN(amount)) return \"₹0\";\n    const formatter = new Intl.NumberFormat(\"en-IN\", {\n        style: \"currency\",\n        currency: \"INR\",\n        minimumFractionDigits: showDecimals ? 2 : 0,\n        maximumFractionDigits: showDecimals ? 2 : 0\n    });\n    return formatter.format(amount);\n}\n/**\n * Format a number as Indian Rupee with custom symbol\n * @param amount - The amount to format\n * @param showDecimals - Whether to show decimal places (default: true)\n * @returns Formatted currency string with ₹ symbol\n */ function formatRupee(amount, showDecimals = true) {\n    if (isNaN(amount)) return \"₹0\";\n    const formatted = new Intl.NumberFormat(\"en-IN\", {\n        minimumFractionDigits: showDecimals ? 2 : 0,\n        maximumFractionDigits: showDecimals ? 2 : 0\n    }).format(amount);\n    return `₹${formatted}`;\n}\n/**\n * Format a number as Indian Rupee for display in tables/lists\n * @param amount - The amount to format\n * @returns Formatted currency string\n */ function formatPrice(amount) {\n    return formatRupee(amount, true);\n}\n/**\n * Format a number as Indian Rupee without decimals\n * @param amount - The amount to format\n * @returns Formatted currency string without decimals\n */ function formatPriceWhole(amount) {\n    return formatRupee(amount, false);\n}\n/**\n * Get the currency symbol\n * @returns The Rupee symbol\n */ function getCurrencySymbol() {\n    return \"₹\";\n}\n/**\n * Get the currency code\n * @returns The currency code for Indian Rupee\n */ function getCurrencyCode() {\n    return \"INR\";\n}\n/**\n * Format stock quantity for display\n * @param quantity - The stock quantity (-1 for unlimited)\n * @returns Formatted stock string\n */ function formatStock(quantity) {\n    if (quantity === -1) {\n        return \"Unlimited\";\n    }\n    return quantity.toString();\n}\n/**\n * Get stock status based on quantity\n * @param quantity - The stock quantity\n * @returns Stock status\n */ function getStockStatus(quantity) {\n    if (quantity === -1) return \"unlimited\";\n    if (quantity === 0) return \"out-of-stock\";\n    if (quantity <= 10) return \"low-stock\";\n    return \"in-stock\";\n}\n/**\n * Get CSS color class for stock display\n * @param quantity - The stock quantity\n * @returns CSS color class\n */ function getStockColor(quantity) {\n    const status = getStockStatus(quantity);\n    switch(status){\n        case \"unlimited\":\n            return \"text-green-600\";\n        case \"in-stock\":\n            return \"text-gray-900\";\n        case \"low-stock\":\n            return \"text-orange-600\";\n        case \"out-of-stock\":\n            return \"text-red-600\";\n        default:\n            return \"text-gray-900\";\n    }\n}\n/**\n * Generate a URL-friendly slug from text\n * @param text - The text to convert to slug\n * @returns URL-friendly slug\n */ function generateSlug(text) {\n    return text.toLowerCase().trim().replace(/[^\\w\\s-]/g, \"\") // Remove special characters except spaces and hyphens\n    .replace(/[\\s_-]+/g, \"-\") // Replace spaces, underscores, and multiple hyphens with single hyphen\n    .replace(/^-+|-+$/g, \"\"); // Remove leading and trailing hyphens\n}\n/**\n * Validate and ensure slug uniqueness (basic validation)\n * @param slug - The slug to validate\n * @param fallbackText - Fallback text if slug is empty\n * @returns Valid slug\n */ function validateSlug(slug, fallbackText) {\n    const cleanSlug = slug ? generateSlug(slug) : generateSlug(fallbackText);\n    return cleanSlug || \"product\"; // Fallback to 'product' if all else fails\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/lib/currency.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ae750f5340b2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9oZXJiYWxpY2lvdXMtbmV4dGpzLy4vYXBwL2dsb2JhbHMuY3NzPzIzMDQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJhZTc1MGY1MzQwYjJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/admin/layout.tsx":
/*!******************************!*\
  !*** ./app/admin/layout.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\admin\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/context/CartContext.tsx":
/*!*************************************!*\
  !*** ./app/context/CartContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CartProvider: () => (/* binding */ e0),
/* harmony export */   useCart: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#CartProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\CartContext.tsx#useCart`);


/***/ }),

/***/ "(rsc)/./app/context/FlashSaleContext.tsx":
/*!******************************************!*\
  !*** ./app/context/FlashSaleContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FlashSaleProvider: () => (/* binding */ e0),
/* harmony export */   useFlashSale: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\FlashSaleContext.tsx#FlashSaleProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\FlashSaleContext.tsx#useFlashSale`);


/***/ }),

/***/ "(rsc)/./app/context/NotificationContext.tsx":
/*!*********************************************!*\
  !*** ./app/context/NotificationContext.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationProvider: () => (/* binding */ e1),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useNotifications: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#useNotifications`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#NotificationProvider`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\NotificationContext.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/context/SessionProvider.tsx":
/*!*****************************************!*\
  !*** ./app/context/SessionProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\project\app\context\SessionProvider.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _context_CartContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context/CartContext */ \"(rsc)/./app/context/CartContext.tsx\");\n/* harmony import */ var _context_SessionProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./context/SessionProvider */ \"(rsc)/./app/context/SessionProvider.tsx\");\n/* harmony import */ var _context_NotificationContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./context/NotificationContext */ \"(rsc)/./app/context/NotificationContext.tsx\");\n/* harmony import */ var _context_FlashSaleContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./context/FlashSaleContext */ \"(rsc)/./app/context/FlashSaleContext.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Herbalicious - Natural Skincare\",\n    description: \"Natural skincare products for radiant, healthy skin. Discover our botanical collection crafted with nature's finest ingredients.\"\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1.0,\n    themeColor: \"#16a34a\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_SessionProvider__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_NotificationContext__WEBPACK_IMPORTED_MODULE_4__.NotificationProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_FlashSaleContext__WEBPACK_IMPORTED_MODULE_5__.FlashSaleProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_CartContext__WEBPACK_IMPORTED_MODULE_2__.CartProvider, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();