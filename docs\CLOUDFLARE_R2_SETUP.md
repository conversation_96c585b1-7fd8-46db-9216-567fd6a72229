# 🚀 Cloudflare R2 Setup for Herbalicious Media Management

## ❗ Current Status
Your R2 integration is **partially configured** but needs the following to work:

1. **Replace placeholder Account ID** in `.env` file
2. **Create the R2 bucket** in Cloudflare dashboard
3. **Configure public access** for the bucket
4. **Set up custom domain** (optional but recommended)

## 🔧 Step-by-Step Setup

### Step 1: Get Your Cloudflare Account ID

1. **Log into Cloudflare Dashboard**: https://dash.cloudflare.com
2. **Find Account ID**: Look in the right sidebar - it's a 32-character string like `a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6`
3. **Copy the Account ID** - you'll need this for the next step

### Step 2: Update Environment Variables

**Open your `.env` file** and replace this line:
```env
R2_ACCOUNT_ID=your_cloudflare_account_id_here
```

**With your actual Account ID:**
```env
R2_ACCOUNT_ID=a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
```

### Step 3: Create R2 Bucket

1. **Go to R2 in Cloudflare Dashboard**: https://dash.cloudflare.com/?to=/:account/r2
2. **Click "Create bucket"**
3. **Bucket name**: `herbalicious-media` (exactly as shown)
4. **Location**: Choose closest to your users (e.g., Eastern North America)
5. **Click "Create bucket"**

### Step 4: Configure Bucket Settings

1. **Open your new bucket**
2. **Go to "Settings" tab**
3. **Find "Public access" section**
4. **Enable "Allow public read access"** ✅
5. **Save changes**

### Step 5: Set Up Custom Domain (Recommended)

#### Option A: Custom Domain (Better for branding)
1. **In bucket settings**, scroll to "Custom Domains"
2. **Click "Connect Domain"**
3. **Enter domain**: `images.herbalicous.in`
4. **Add CNAME record** to your DNS:
   - **Type**: CNAME
   - **Name**: images
   - **Target**: `herbalicious-media.YOUR_ACCOUNT_ID.r2.cloudflarestorage.com`
5. **Wait for DNS propagation** (5-30 minutes)

#### Option B: Direct R2 URL (Simpler)
1. **In bucket settings**, find "Public bucket URL"
2. **Copy the URL** (looks like `https://pub-xxxxx.r2.dev`)
3. **Update `.env` file**:
   ```env
   R2_PUBLIC_URL=https://pub-xxxxx.r2.dev
   ```

### Step 6: Verify Setup

1. **Restart your development server**:
   ```bash
   npm run dev
   ```

2. **Go to Media Management**: http://localhost:3000/admin/media

3. **Check configuration status** - should show all green checkmarks

4. **Test upload** - click "Test R2 Upload" button

## 🔍 Troubleshooting

### Issue: "Account ID not found"
- **Solution**: Double-check your Account ID from Cloudflare dashboard
- **Make sure**: No extra spaces or characters in `.env` file

### Issue: "Bucket not found"
- **Solution**: Create bucket named exactly `herbalicious-media`
- **Check**: Bucket is in the same account as your Account ID

### Issue: "Access denied"
- **Solution**: Enable "Allow public read access" in bucket settings
- **Check**: Your R2 API token has correct permissions

### Issue: "SSL/TLS errors"
- **Solution**: This usually resolves after proper Account ID setup
- **Try**: Restart your development server after updating `.env`

### Issue: Custom domain not working
- **Solution**: 
  - Verify CNAME record is correct
  - Wait for DNS propagation (up to 24 hours)
  - Use direct R2 URL as fallback

## 📋 Quick Checklist

- [ ] ✅ Account ID copied from Cloudflare dashboard
- [ ] ✅ `.env` file updated with real Account ID
- [ ] ✅ Bucket `herbalicious-media` created
- [ ] ✅ Public read access enabled on bucket
- [ ] ✅ Custom domain configured OR direct R2 URL set
- [ ] ✅ Development server restarted
- [ ] ✅ Configuration test passes
- [ ] ✅ Test upload works

## 🎯 Expected Results

After completing setup:

1. **Configuration check** should show:
   ```json
   {
     "ready": true,
     "connectionTest": {
       "success": true,
       "targetBucketExists": true
     }
   }
   ```

2. **Test upload** should succeed and show file URL

3. **File URLs** should be accessible in browser

## 📞 Need Help?

1. **Check the setup wizard** in the admin panel
2. **Use the configuration checker** at `/api/media/config`
3. **Review Cloudflare R2 documentation**: https://developers.cloudflare.com/r2/

## 🔐 Security Notes

- **API tokens**: Keep your R2 credentials secure
- **Public access**: Only enable for files you want publicly accessible
- **Domain**: Use HTTPS for all file URLs
- **Monitoring**: Check R2 usage in Cloudflare dashboard

---

**Once setup is complete**, your media management system will be fully functional with:
- ✅ Direct upload to Cloudflare R2
- ✅ Fast global CDN delivery
- ✅ Scalable storage
- ✅ Professional file management interface
