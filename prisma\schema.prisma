generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               String          @id @default(cuid())
  email            String          @unique
  name             String?
  phone            String?
  avatar           String?
  password         String?
  role             UserRole        @default(CUSTOMER)
  emailVerified    DateTime?
  resetToken       String?
  resetTokenExpiry DateTime?
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt
  addresses        Address[]
  couponUsages     CouponUsage[]
  notifications    Notification[]
  orders           Order[]
  reviews          Review[]
  preferences      UserPreference?
  wishlist         WishlistItem[]

  @@map("users")
}

model UserPreference {
  id                    String   @id @default(cuid())
  userId                String   @unique
  language              String   @default("en-US")
  theme                 String   @default("light")
  orderUpdates          <PERSON>olean  @default(true)
  promotions            Boolean  @default(false)
  newsletter            Boolean  @default(true)
  smsNotifications      Boolean  @default(false)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  adminMessages         <PERSON>olean  @default(true)
  broadcastMessages     <PERSON>olean  @default(true)
  emailNotifications    Boolean  @default(true)
  inAppNotifications    Boolean  @default(true)
  orderNotifications    Boolean  @default(true)
  priceDropAlerts       Boolean  @default(false)
  reviewNotifications   Boolean  @default(true)
  wishlistNotifications Boolean  @default(true)
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_preferences")
}

model Category {
  id                String            @id @default(cuid())
  name              String            @unique
  slug              String            @unique
  description       String?
  parentId          String?
  isActive          Boolean           @default(true)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  parent            Category?         @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children          Category[]        @relation("CategoryHierarchy")
  productCategories ProductCategory[]
  products          Product[]

  @@map("categories")
}

model Product {
  id                String            @id @default(cuid())
  name              String
  slug              String            @unique
  description       String?
  shortDescription  String?
  comparePrice      Float?
  costPrice         Float?
  weight            Float?
  dimensions        String?
  isActive          Boolean           @default(true)
  isFeatured        Boolean           @default(false)
  metaTitle         String?
  metaDescription   String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  categoryId        String?
  price             Float?
  homepageSettings  HomepageSetting[] @relation("ProductOfTheMonth")
  orderItems        OrderItem[]
  productCategories ProductCategory[]
  faqs              ProductFAQ[]
  images            ProductImage[]
  variants          ProductVariant[]
  category          Category?         @relation(fields: [categoryId], references: [id])
  reviews           Review[]
  wishlist          WishlistItem[]

  @@map("products")
}

model ProductImage {
  id        String  @id @default(cuid())
  url       String
  alt       String?
  position  Int     @default(0)
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductVariant {
  id          String      @id @default(cuid())
  name        String
  value       String
  price       Float?
  productId   String
  pricingMode PricingMode @default(INCREMENT)
  product     Product     @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_variants")
}

model ProductFAQ {
  id        String   @id @default(cuid())
  question  String
  answer    String
  position  Int      @default(0)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  productId String
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_faqs")
}

model Order {
  id                String        @id @default(cuid())
  orderNumber       String        @unique
  status            OrderStatus   @default(PENDING)
  paymentStatus     PaymentStatus @default(PENDING)
  paymentMethod     String?
  paymentId         String?
  subtotal          Float
  tax               Float         @default(0)
  shipping          Float         @default(0)
  discount          Float         @default(0)
  total             Float
  currency          String        @default("INR")
  notes             String?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  userId            String
  couponCodes       String[]
  couponDiscount    Float         @default(0)
  estimatedDelivery String?
  trackingNumber    String?
  couponUsages      CouponUsage[]
  address           OrderAddress?
  items             OrderItem[]
  user              User          @relation(fields: [userId], references: [id])

  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  quantity  Int
  price     Float
  total     Float
  orderId   String
  productId String
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product   Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

model OrderAddress {
  id         String  @id @default(cuid())
  firstName  String
  lastName   String
  company    String?
  address1   String
  address2   String?
  city       String
  state      String
  postalCode String
  country    String  @default("India")
  phone      String?
  orderId    String  @unique
  order      Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("order_addresses")
}

model Address {
  id         String  @id @default(cuid())
  firstName  String
  lastName   String
  company    String?
  address1   String
  address2   String?
  city       String
  state      String
  postalCode String
  country    String  @default("India")
  phone      String?
  isDefault  Boolean @default(false)
  userId     String
  user       User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("addresses")
}

model Review {
  id         String       @id @default(cuid())
  rating     Int
  title      String?
  content    String?
  isVerified Boolean      @default(false)
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt
  userId     String
  productId  String
  status     ReviewStatus @default(PENDING)
  product    Product      @relation(fields: [productId], references: [id], onDelete: Cascade)
  user       User         @relation(fields: [userId], references: [id])

  @@unique([userId, productId])
  @@map("reviews")
}

model Notification {
  id        String           @id @default(cuid())
  type      NotificationType
  title     String
  message   String
  isRead    Boolean          @default(false)
  expiresAt DateTime?
  createdAt DateTime         @default(now())
  userId    String
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model Coupon {
  id               String        @id @default(cuid())
  code             String        @unique
  description      String?
  discountType     DiscountType
  discountValue    Float
  minimumPurchase  Float?
  maxDiscount      Float?
  usageLimit       Int?
  usageCount       Int           @default(0)
  userLimit        Int?
  validFrom        DateTime
  validUntil       DateTime?
  isActive         Boolean       @default(true)
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt
  couponUsages     CouponUsage[]

  @@map("coupons")
}

model CouponUsage {
  id        String   @id @default(cuid())
  couponId  String
  userId    String
  orderId   String
  createdAt DateTime @default(now())
  coupon    Coupon   @relation(fields: [couponId], references: [id])
  user      User
