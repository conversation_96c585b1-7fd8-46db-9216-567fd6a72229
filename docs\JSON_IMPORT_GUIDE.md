# JSON Import Guide for Products

## Overview

The product import system now supports both CSV and JSON file formats. JSON import provides a more structured and flexible way to import products with complex data like variations, multiple categories, and detailed product information.

## JSON Import Features

### Supported File Formats
- **CSV files** (`.csv`) - Traditional comma-separated values
- **JSON files** (`.json`) - Structured JSON format

### JSON Format Options

The system accepts two JSON formats:

#### 1. Wrapped Format (Recommended)
```json
{
  "products": [
    {
      "name": "Product Name",
      "description": "Product description",
      // ... other fields
    }
  ]
}
```

#### 2. Direct Array Format
```json
[
  {
    "name": "Product Name",
    "description": "Product description",
    // ... other fields
  }
]
```

## JSON Schema

### Required Fields
- `name` (string) - Product name
- `category` (string) OR `categoryNames` (array) - Product categories

### Optional Fields
- `slug` (string) - URL-friendly identifier (auto-generated if not provided)
- `description` (string) - Detailed product description
- `shortDescription` (string) - Brief product summary
- `price` (number) - Product price (required if no variations with prices)
- `comparePrice` (number) - Original/compare price for discounts
- `isFeatured` (boolean) - Whether product is featured (default: false)
- `isActive` (boolean) - Whether product is active (default: true)
- `variations` (array) - Product variations with pricing

### Variations Format
```json
"variations": [
  {
    "name": "Size",        // Variation type (can be empty for simple variations)
    "value": "Medium",     // Variation value
    "price": 29.99         // Price for this variation
  }
]
```

## Complete Example

```json
{
  "products": [
    {
      "name": "Premium Face Cream",
      "slug": "premium-face-cream",
      "description": "A luxurious face cream with natural ingredients for all skin types.",
      "shortDescription": "Premium moisturizing face cream",
      "price": 45.99,
      "comparePrice": 59.99,
      "category": "Skincare",
      "categoryNames": ["Skincare", "Face Care"],
      "isFeatured": true,
      "isActive": true,
      "variations": [
        {
          "name": "Size",
          "value": "50ml",
          "price": 45.99
        },
        {
          "name": "Size", 
          "value": "100ml",
          "price": 79.99
        }
      ]
    },
    {
      "name": "Hair Growth Oil",
      "description": "Natural hair oil for promoting hair growth",
      "shortDescription": "Hair growth treatment oil",
      "category": "Hair Care",
      "isFeatured": false,
      "isActive": true,
      "variations": [
        {
          "name": "",
          "value": "100ml",
          "price": 25.99
        },
        {
          "name": "",
          "value": "200ml",
          "price": 45.99
        }
      ]
    }
  ]
}
```

## Categories

### Available Categories
- Skincare
- Hair Care  
- Body Care

### Category Handling
- Use `category` for single category assignment
- Use `categoryNames` for multiple categories
- Categories are case-insensitive with fuzzy matching
- "Skincare" matches "Skin Care", "skin", etc.

## Pricing Rules

1. **Direct Price**: Set `price` field for simple products
2. **Variation Pricing**: If no `price` field, system extracts highest price from variations
3. **Required**: Either `price` OR variations with prices must be provided

## Validation

The system validates:
- ✅ Product name is required
- ✅ At least one valid category
- ✅ Valid pricing (direct price OR variation prices)
- ✅ Unique product slugs
- ✅ JSON format validity

## Error Handling

Import results show:
- Number of successful imports
- Number of failed imports  
- Detailed error messages for failures
- Products are skipped (not imported) if validation fails

## How to Use

### Via Admin Interface
1. Go to Admin → Products
2. Click the "JSON" template button to download a sample
3. Modify the template with your product data
4. Click the Upload button and select your JSON file
5. Review import results

### Via API
```bash
curl -X POST http://localhost:3000/api/products/import \
  -H "Content-Type: application/json" \
  -d @your-products.json
```

## Tips for Success

1. **Start Small**: Test with 1-2 products first
2. **Use Templates**: Download and modify the JSON template
3. **Validate JSON**: Ensure your JSON is valid before importing
4. **Check Categories**: Use exact category names from the available list
5. **Price Variations**: For size-based products, use variations instead of base price
6. **Backup**: Export existing products before large imports

## Troubleshooting

### Common Issues
- **"Invalid JSON format"**: Check JSON syntax with a validator
- **"Missing required name field"**: Ensure all products have names
- **"No valid categories"**: Check category names match available options
- **"Missing required price field"**: Add price OR variations with prices

### Best Practices
- Use consistent naming for variations
- Include both `category` and `categoryNames` for flexibility
- Set realistic compare prices for discount display
- Use descriptive slugs for SEO