# Herbalicious Admin Panel

A comprehensive admin interface for managing your Herbalicious skincare e-commerce store. The admin panel matches your site's green theme and provides full CRUD functionality for products, categories, orders, and customers.

## 🚀 Features

### ✅ **Dashboard**
- Overview statistics (revenue, orders, customers, products)
- Recent orders with status tracking
- Top-performing products with ratings
- Trend indicators and growth metrics

### ✅ **Product Management**
- View all products in a clean table format
- Search and filter products
- Edit product details (name, price, description, category)
- Manage product status (active, featured, draft)
- Product images and ratings display
- Bulk actions support

### ✅ **Category Management**
- Visual category cards with icons
- Add new categories with custom icons
- Edit category details and descriptions
- Product count per category
- Category status management

### ✅ **Order Management**
- Complete order tracking system
- Order status updates (pending, processing, shipped, delivered)
- Customer information and contact details
- Payment status tracking
- Order item details and totals
- Search and filter orders

### ✅ **Customer Management**
- Customer profiles with purchase history
- Contact information and addresses
- Customer tags (VIP, Frequent Buyer, etc.)
- Total orders and spending analytics
- Customer status management

### ✅ **Media Management**
- **Cloudflare R2 Integration**: Direct upload to your R2 storage
- **File Upload**: Drag & drop or click to upload (images, videos, PDFs)
- **File Organization**: Folder-based organization system
- **Search & Filter**: Find files by name, type, or folder
- **Grid/List Views**: Switch between visual grid and detailed list
- **File Preview**: Preview images and videos with metadata
- **URL Management**: Copy file URLs for use in products/content
- **Bulk Operations**: Select and delete multiple files
- **File Validation**: Automatic file type and size validation (10MB max)

### ✅ **Settings**
- Store configuration (name, description, contact info)
- Email/SMTP settings
- SEO settings (title, description, keywords)
- Security settings (2FA, session timeout)
- Payment and notification preferences

## 🎨 Design Features

### **Theme Consistency**
- **Primary Color**: Green (#16a34a) - matches your Herbalicious brand
- **Typography**: Inter font family (same as main site)
- **Icons**: Lucide React icons for consistency
- **Layout**: Clean, modern design with proper spacing

### **Responsive Design**
- **Desktop**: Full sidebar navigation with detailed views
- **Mobile**: Collapsible hamburger menu
- **Tablet**: Optimized layouts for medium screens
- **Touch-friendly**: Large buttons and touch targets

### **User Experience**
- **Intuitive Navigation**: Clear menu structure
- **Visual Feedback**: Hover states and transitions
- **Status Indicators**: Color-coded status badges
- **Search & Filter**: Easy content discovery
- **Bulk Actions**: Efficient management tools

## 🔐 Access

### **Current Access Method**
1. Go to your main website: `http://localhost:3000`
2. Navigate to Profile page
3. Look for the "Admin Panel" button (green button with shield icon)
4. Click to access the admin dashboard

### **Direct Access**
- Admin Dashboard: `http://localhost:3000/admin`
- Products: `http://localhost:3000/admin/products`
- Categories: `http://localhost:3000/admin/categories`
- Orders: `http://localhost:3000/admin/orders`
- Customers: `http://localhost:3000/admin/customers`
- Media: `http://localhost:3000/admin/media`
- Settings: `http://localhost:3000/admin/settings`

## 📱 Mobile Experience

The admin panel is fully responsive and works great on mobile devices:

- **Collapsible Sidebar**: Hamburger menu for navigation
- **Touch-Optimized**: Large buttons and touch targets
- **Responsive Tables**: Horizontal scrolling for data tables
- **Mobile-First**: Designed with mobile users in mind

## 🛠 Technical Details

### **Built With**
- **Next.js 14**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling
- **Lucide React**: Beautiful icons
- **Responsive Design**: Mobile-first approach

### **File Structure**
```
app/admin/
├── layout.tsx          # Admin layout with sidebar
├── page.tsx           # Dashboard
├── products/page.tsx  # Product management
├── categories/page.tsx # Category management
├── orders/page.tsx    # Order management
├── customers/page.tsx # Customer management
├── media/page.tsx     # Media management
└── settings/page.tsx  # Settings

app/api/media/
├── upload/route.ts    # File upload API
├── list/route.ts      # List files API
└── delete/route.ts    # Delete files API

app/lib/
└── r2.ts             # Cloudflare R2 utilities

app/components/admin/
└── MediaPicker.tsx   # Reusable media picker component
```

### **Data Integration**
- Uses existing product data from `app/data/products.ts`
- Mock data for orders and customers (ready for API integration)
- Type-safe interfaces in `app/types/index.ts`

## 🔄 Future Enhancements

### **Ready for API Integration**
- All components are designed to work with real APIs
- Mock data can be easily replaced with API calls
- Error handling and loading states ready

### **Potential Additions**
- **Analytics Dashboard**: Detailed sales and customer analytics
- **Inventory Management**: Stock tracking and alerts
- **Marketing Tools**: Discount codes and promotions
- **Content Management**: Blog posts and pages
- **User Roles**: Different admin permission levels

## 🎯 Usage Tips

1. **Navigation**: Use the sidebar to switch between different sections
2. **Search**: Use search bars to quickly find specific items
3. **Bulk Actions**: Select multiple items for batch operations
4. **Status Updates**: Click status badges to change order/product status
5. **Mobile**: Use the hamburger menu on mobile devices

## 🔧 Customization

The admin panel is built with your brand in mind:
- **Colors**: Easy to customize via Tailwind classes
- **Layout**: Modular components for easy modification
- **Features**: Add new sections by following existing patterns
- **Branding**: Logo and colors already match your site

## 📁 Media Management

### **Cloudflare R2 Integration**
The media management system is fully integrated with Cloudflare R2 for scalable, fast file storage:

- **Endpoint**: `https://images.herbalicous.in`
- **Automatic Upload**: Files are uploaded directly to R2
- **CDN Delivery**: Fast global content delivery
- **Secure Storage**: Files are stored securely with access controls

### **Supported File Types**
- **Images**: JPEG, PNG, GIF, WebP, SVG
- **Videos**: MP4, WebM
- **Documents**: PDF
- **Size Limit**: 10MB per file

### **Features**
1. **Upload Files**
   - Drag & drop interface
   - Multiple file selection
   - Progress indicators
   - File validation

2. **File Organization**
   - Folder-based structure
   - Automatic folder creation
   - Filter by folder

3. **Search & Filter**
   - Search by filename
   - Filter by file type
   - Filter by folder

4. **View Modes**
   - Grid view with thumbnails
   - List view with details
   - File preview modal

5. **File Management**
   - Copy URLs to clipboard
   - Bulk delete operations
   - File metadata display

### **Environment Variables**
Make sure these are set in your `.env` file:
```env
R2_ACCESS_KEY_ID=your_access_key
R2_SECRET_ACCESS_KEY=your_secret_key
R2_BUCKET_NAME=herbalicious-media
R2_ENDPOINT=https://images.herbalicous.in
R2_PUBLIC_URL=https://images.herbalicous.in
```

### **API Endpoints**
- `POST /api/media/upload` - Upload files
- `GET /api/media/list` - List files
- `DELETE /api/media/delete` - Delete files

### **Usage in Other Components**
Use the `MediaPicker` component to select media in other admin pages:

```tsx
import MediaPicker from '../components/admin/MediaPicker';

const [showMediaPicker, setShowMediaPicker] = useState(false);

const handleMediaSelect = (file: MediaFile) => {
  console.log('Selected file:', file.url);
};

<MediaPicker
  isOpen={showMediaPicker}
  onClose={() => setShowMediaPicker(false)}
  onSelect={handleMediaSelect}
  allowedTypes={['image']}
  title="Select Product Image"
/>
```

---

**Note**: This admin panel is currently set up for development/demo purposes. For production use, implement proper authentication, authorization, and API integration.
