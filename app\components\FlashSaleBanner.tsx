'use client';

import React, { useEffect, useState } from 'react';
import { Clock, Zap } from 'lucide-react';
import { useFlashSale } from '../context/FlashSaleContext';
import { isFlashSaleActive } from '../lib/flash-sale';

export default function FlashSaleBanner() {
  const { flashSaleSettings } = useFlashSale();
  const [timeLeft, setTimeLeft] = useState<string>('');

  useEffect(() => {
    if (!flashSaleSettings || !isFlashSaleActive(flashSaleSettings)) {
      return;
    }

    const calculateTimeLeft = () => {
      if (!flashSaleSettings.flashSaleEndDate) {
        return '';
      }

      const now = new Date().getTime();
      const endDate = new Date(flashSaleSettings.flashSaleEndDate).getTime();
      const difference = endDate - now;

      if (difference <= 0) {
        return 'Ended';
      }

      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      if (days > 0) {
        return `${days}d ${hours}h ${minutes}m`;
      } else if (hours > 0) {
        return `${hours}h ${minutes}m ${seconds}s`;
      } else {
        return `${minutes}m ${seconds}s`;
      }
    };

    // Update immediately
    setTimeLeft(calculateTimeLeft());

    // Update every second
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, [flashSaleSettings]);

  if (!flashSaleSettings || !isFlashSaleActive(flashSaleSettings)) {
    return null;
  }

  return (
    <div 
      className="relative overflow-hidden rounded-lg p-4 mb-6"
      style={{ backgroundColor: flashSaleSettings.flashSaleBackgroundColor || '#16a34a' }}
    >
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white rounded-full"></div>
        <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-white rounded-full"></div>
      </div>

      <div className="relative z-10">
        <div className="flex items-center justify-between flex-wrap gap-4">
          <div className="flex items-center space-x-3">
            <div className="bg-white bg-opacity-20 p-2 rounded-full">
              <Zap className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-white font-bold text-lg">
                {flashSaleSettings.flashSaleTitle || 'Flash Sale'}
              </h3>
              <p className="text-white text-opacity-90 text-sm">
                {flashSaleSettings.flashSaleSubtitle || `Get ${flashSaleSettings.flashSalePercentage}% off all products`}
              </p>
            </div>
          </div>

          {timeLeft && flashSaleSettings.flashSaleEndDate && (
            <div className="flex items-center space-x-2 bg-white bg-opacity-20 px-4 py-2 rounded-full">
              <Clock className="w-4 h-4 text-white" />
              <span className="text-white font-semibold text-sm">
                {timeLeft === 'Ended' ? 'Sale Ended' : `Ends in: ${timeLeft}`}
              </span>
            </div>
          )}
        </div>

        {flashSaleSettings.flashSalePercentage && (
          <div className="mt-3 inline-flex items-center bg-white bg-opacity-20 px-3 py-1 rounded-full">
            <span className="text-white font-bold text-2xl">
              {flashSaleSettings.flashSalePercentage}% OFF
            </span>
            <span className="text-white text-opacity-90 ml-2">
              Store-wide Discount
            </span>
          </div>
        )}
      </div>
    </div>
  );
}