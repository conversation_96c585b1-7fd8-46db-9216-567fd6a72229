'use client';

import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface ProductFAQ {
  id: string;
  question: string;
  answer: string;
  position: number;
  isActive: boolean;
}

interface ProductFAQsProps {
  productId: string;
}

const ProductFAQs: React.FC<ProductFAQsProps> = ({ productId }) => {
  const [faqs, setFaqs] = useState<ProductFAQ[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);

  useEffect(() => {
    fetchFAQs();
  }, [productId]);

  const fetchFAQs = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/products/${productId}/faqs`);
      const result = await response.json();
      
      if (result.success) {
        setFaqs(result.data);
      }
    } catch (error) {
      console.error('Error fetching FAQs:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleFAQ = (faqId: string) => {
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 bg-gray-100 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  if (faqs.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-500">No frequently asked questions available for this product.</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold text-gray-900 mb-6">Frequently Asked Questions</h3>
      
      <div className="space-y-3">
        {faqs.map((faq) => (
          <div
            key={faq.id}
            className="border border-gray-200 rounded-lg overflow-hidden"
          >
            <button
              onClick={() => toggleFAQ(faq.id)}
              className="w-full px-6 py-4 text-left bg-white hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-inset"
            >
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-gray-900 pr-4">{faq.question}</h4>
                <div className="flex-shrink-0">
                  {expandedFAQ === faq.id ? (
                    <ChevronUp className="w-5 h-5 text-gray-500" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-500" />
                  )}
                </div>
              </div>
            </button>
            
            {expandedFAQ === faq.id && (
              <div className="px-6 pb-4 bg-gray-50">
                <div className="pt-2 text-gray-700 whitespace-pre-wrap leading-relaxed">
                  {faq.answer}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <p className="text-sm text-blue-800">
          <strong>Have a question that's not answered here?</strong> Feel free to contact our customer support team for more information about this product.
        </p>
      </div>
    </div>
  );
};

export default ProductFAQs;
