'use client';

import React, { useState, useEffect } from 'react';
import { Clock, Zap } from 'lucide-react';

interface CountdownBannerProps {
  endDate?: string;
  title?: string;
  subtitle?: string;
  backgroundColor?: string;
  discountPercentage?: number;
}

const CountdownBanner: React.FC<CountdownBannerProps> = ({
  endDate,
  title = "Limited Time Offer",
  subtitle,
  backgroundColor = "#16a34a",
  discountPercentage = 25
}) => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  // Use dynamic subtitle if provided, otherwise generate from discount percentage
  const dynamicSubtitle = subtitle || `Get ${discountPercentage}% off all natural skincare products`;

  useEffect(() => {
    // Set default end date to 7 days from now if not provided
    const targetDate = endDate ? new Date(endDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

    const timer = setInterval(() => {
      const now = new Date().getTime();
      const distance = targetDate.getTime() - now;

      if (distance > 0) {
        setTimeLeft({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000)
        });
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [endDate]);

  const isExpired = timeLeft.days === 0 && timeLeft.hours === 0 && timeLeft.minutes === 0 && timeLeft.seconds === 0;

  if (isExpired) {
    return null; // Don't show expired countdown
  }

  return (
    <div 
      className="mx-4 mt-4 rounded-2xl p-6 text-white shadow-lg lg:mx-8 lg:mt-6 lg:p-8"
      style={{ backgroundColor }}
    >
      <div className="flex items-center justify-center mb-4">
        <Zap className="w-6 h-6 mr-2 animate-pulse" />
        <span className="text-sm font-semibold uppercase tracking-wide lg:text-base">
          Flash Sale
        </span>
      </div>

      <div className="text-center mb-6">
        <h3 className="text-xl font-bold mb-2 lg:text-2xl">{title}</h3>
        <p className="text-sm opacity-90 lg:text-base">{dynamicSubtitle}</p>
      </div>

      <div className="flex items-center justify-center space-x-4 mb-6">
        <Clock className="w-5 h-5 opacity-80" />
        <span className="text-sm font-medium lg:text-base">Ends in:</span>
      </div>

      <div className="grid grid-cols-4 gap-3 max-w-sm mx-auto">
        <div className="bg-white bg-opacity-20 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold lg:text-3xl">{timeLeft.days}</div>
          <div className="text-xs opacity-80 lg:text-sm">Days</div>
        </div>
        <div className="bg-white bg-opacity-20 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold lg:text-3xl">{timeLeft.hours}</div>
          <div className="text-xs opacity-80 lg:text-sm">Hours</div>
        </div>
        <div className="bg-white bg-opacity-20 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold lg:text-3xl">{timeLeft.minutes}</div>
          <div className="text-xs opacity-80 lg:text-sm">Min</div>
        </div>
        <div className="bg-white bg-opacity-20 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold lg:text-3xl">{timeLeft.seconds}</div>
          <div className="text-xs opacity-80 lg:text-sm">Sec</div>
        </div>
      </div>

      <div className="text-center mt-6">
        <button className="bg-white text-gray-900 px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-200 shadow-lg">
          Shop Now & Save {discountPercentage}%
        </button>
      </div>
    </div>
  );
};

export default CountdownBanner;
