import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../../lib/auth';
import { notificationService } from '../../../../lib/notifications';
import { logger } from '../../../../lib/logger';
import { z } from 'zod';

const broadcastSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
  type: z.enum(['BROADCAST', 'PROMOTIONAL', 'SYSTEM']).default('BROADCAST'),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']).default('NORMAL'),
  sendEmail: z.boolean().default(true),
  sendInApp: z.boolean().default(true),
});

/**
 * POST /api/admin/notifications/broadcast
 * Send broadcast notification to all users
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Only allow admin users
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = broadcastSchema.parse(body);

    logger.info('Admin sending broadcast notification', {
      adminId: session.user.id,
      type: validatedData.type,
      priority: validatedData.priority,
      title: validatedData.title,
    });

    // Send broadcast notification
    const result = await notificationService.sendBroadcast({
      type: validatedData.type as any,
      title: validatedData.title,
      message: validatedData.content,
      priority: validatedData.priority as any,
      sendEmail: validatedData.sendEmail,
      data: {
        sentBy: session.user.id,
        sentAt: new Date().toISOString(),
      },
    });

    logger.info('Broadcast notification sent successfully', {
      adminId: session.user.id,
      userCount: result.length,
    });

    return NextResponse.json({
      success: true,
      message: `Broadcast notification sent to ${result.length} users`,
      userCount: result.length,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation error',
        details: error.issues,
      }, { status: 400 });
    }

    logger.error('Failed to send broadcast notification', error as Error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
