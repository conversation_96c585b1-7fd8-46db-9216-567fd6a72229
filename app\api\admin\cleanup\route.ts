import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../lib/auth';
import { prisma } from '../../../lib/db';

/**
 * POST /api/admin/cleanup
 * Clean up database by removing notifications, reviews, and orders
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is admin
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get counts before deletion
    const notificationCount = await prisma.notification.count();
    const reviewCount = await prisma.review.count();
    const orderCount = await prisma.order.count();
    const orderItemCount = await prisma.orderItem.count();
    const orderAddressCount = await prisma.orderAddress.count();
    const couponUsageCount = await prisma.couponUsage.count();

    // Delete in correct order to handle foreign key constraints
    
    // 1. Delete order-related data
    await prisma.orderAddress.deleteMany({});
    await prisma.orderItem.deleteMany({});
    await prisma.couponUsage.deleteMany({});
    await prisma.order.deleteMany({});
    
    // 2. Delete reviews
    await prisma.review.deleteMany({});
    
    // 3. Delete notifications
    await prisma.notification.deleteMany({});

    return NextResponse.json({
      success: true,
      message: 'Database cleanup completed successfully',
      deletedCounts: {
        notifications: notificationCount,
        reviews: reviewCount,
        orders: orderCount,
        orderItems: orderItemCount,
        orderAddresses: orderAddressCount,
        couponUsages: couponUsageCount,
      }
    });

  } catch (error) {
    console.error('Error during database cleanup:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to cleanup database' },
      { status: 500 }
    );
  }
}