"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/wishlist/route";
exports.ids = ["app/api/wishlist/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwishlist%2Froute&page=%2Fapi%2Fwishlist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwishlist%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwishlist%2Froute&page=%2Fapi%2Fwishlist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwishlist%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_project_app_api_wishlist_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/wishlist/route.ts */ \"(rsc)/./app/api/wishlist/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/wishlist/route\",\n        pathname: \"/api/wishlist\",\n        filename: \"route\",\n        bundlePath: \"app/api/wishlist/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project\\\\app\\\\api\\\\wishlist\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_project_app_api_wishlist_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/wishlist/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwishlist%2Froute&page=%2Fapi%2Fwishlist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwishlist%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/wishlist/route.ts":
/*!***********************************!*\
  !*** ./app/api/wishlist/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/auth */ \"(rsc)/./app/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/db */ \"(rsc)/./app/lib/db.ts\");\n/* harmony import */ var _lib_notification_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/notification-helpers */ \"(rsc)/./app/lib/notification-helpers.ts\");\n\n\n\n\n\n// GET /api/wishlist - Get user's wishlist items\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const wishlistItems = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.wishlistItem.findMany({\n            where: {\n                userId: session.user.id\n            },\n            include: {\n                product: {\n                    include: {\n                        images: {\n                            orderBy: {\n                                id: \"asc\"\n                            },\n                            take: 1\n                        },\n                        productCategories: {\n                            include: {\n                                category: true\n                            }\n                        }\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            }\n        });\n        // Transform the data to match the expected format\n        const transformedItems = wishlistItems.map((item)=>({\n                id: item.product.id,\n                name: item.product.name,\n                slug: item.product.slug,\n                price: item.product.price,\n                shortDescription: item.product.shortDescription,\n                image: item.product.images[0]?.url || \"/placeholder-product.jpg\",\n                rating: 4.5,\n                reviews: 0,\n                wishlistItemId: item.id\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            items: transformedItems\n        });\n    } catch (error) {\n        console.error(\"Error fetching wishlist:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/wishlist - Add item to wishlist\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { productId } = await request.json();\n        if (!productId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Product ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if product exists\n        const product = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.product.findUnique({\n            where: {\n                id: productId\n            }\n        });\n        if (!product) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Product not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check if item is already in wishlist\n        const existingItem = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.wishlistItem.findUnique({\n            where: {\n                userId_productId: {\n                    userId: session.user.id,\n                    productId: productId\n                }\n            }\n        });\n        if (existingItem) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Item already in wishlist\"\n            }, {\n                status: 400\n            });\n        }\n        // Add to wishlist\n        const wishlistItem = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.wishlistItem.create({\n            data: {\n                userId: session.user.id,\n                productId: productId\n            }\n        });\n        // Send wishlist added notification\n        try {\n            await _lib_notification_helpers__WEBPACK_IMPORTED_MODULE_4__.wishlistNotifications.itemAdded(session.user.id, {\n                productId: product.id,\n                productName: product.name,\n                price: product.price || undefined,\n                currency: \"INR\"\n            });\n        } catch (notificationError) {\n            console.error(\"Failed to send wishlist added notification:\", notificationError);\n        // Don't fail the wishlist addition if notification fails\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            item: wishlistItem\n        });\n    } catch (error) {\n        console.error(\"Error adding to wishlist:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/wishlist - Remove item from wishlist\nasync function DELETE(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const productId = searchParams.get(\"productId\");\n        if (!productId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Product ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Get product details before removing\n        const product = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.product.findUnique({\n            where: {\n                id: productId\n            }\n        });\n        // Remove from wishlist\n        const deletedItem = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.wishlistItem.deleteMany({\n            where: {\n                userId: session.user.id,\n                productId: productId\n            }\n        });\n        if (deletedItem.count === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Item not found in wishlist\"\n            }, {\n                status: 404\n            });\n        }\n        // Send wishlist removed notification\n        try {\n            if (product) {\n                await _lib_notification_helpers__WEBPACK_IMPORTED_MODULE_4__.wishlistNotifications.itemRemoved(session.user.id, {\n                    productId: product.id,\n                    productName: product.name\n                });\n            }\n        } catch (notificationError) {\n            console.error(\"Failed to send wishlist removed notification:\", notificationError);\n        // Don't fail the wishlist removal if notification fails\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"Error removing from wishlist:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/wishlist/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/auth.ts":
/*!*************************!*\
  !*** ./app/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db */ \"(rsc)/./app/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst authOptions = {\n    // Remove PrismaAdapter when using JWT strategy\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const user = await _db__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user || !user.password) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2__.compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    throw new Error(\"Invalid credentials\");\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\",\n        maxAge: 7 * 24 * 60 * 60,\n        updateAge: 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 7 * 24 * 60 * 60\n    },\n    // Enable CSRF protection\n    cookies: {\n        sessionToken: {\n            name:  false ? 0 : `next-auth.session-token`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: \"development\" === \"production\"\n            }\n        },\n        callbackUrl: {\n            name:  false ? 0 : `next-auth.callback-url`,\n            options: {\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: \"development\" === \"production\"\n            }\n        },\n        csrfToken: {\n            name:  false ? 0 : `next-auth.csrf-token`,\n            options: {\n                httpOnly: true,\n                sameSite: \"lax\",\n                path: \"/\",\n                secure: \"development\" === \"production\"\n            }\n        }\n    },\n    callbacks: {\n        async jwt ({ token, user, account, profile }) {\n            // Handle initial sign in\n            if (account && user) {\n                // For OAuth providers, create/update user in database\n                if (account.provider === \"google\") {\n                    try {\n                        const email = user.email || profile?.email;\n                        if (!email) {\n                            throw new Error(\"No email found for Google account\");\n                        }\n                        // Check if user exists\n                        let dbUser = await _db__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n                            where: {\n                                email\n                            },\n                            select: {\n                                id: true,\n                                role: true,\n                                name: true,\n                                email: true\n                            }\n                        });\n                        // Create user if doesn't exist\n                        if (!dbUser) {\n                            dbUser = await _db__WEBPACK_IMPORTED_MODULE_3__.prisma.user.create({\n                                data: {\n                                    email,\n                                    name: user.name || profile?.name || email.split(\"@\")[0],\n                                    // Don't save Google profile picture\n                                    role: _prisma_client__WEBPACK_IMPORTED_MODULE_4__.UserRole.CUSTOMER,\n                                    emailVerified: new Date()\n                                },\n                                select: {\n                                    id: true,\n                                    role: true,\n                                    name: true,\n                                    email: true\n                                }\n                            });\n                        }\n                        // Set token properties\n                        token.sub = dbUser.id;\n                        token.role = dbUser.role;\n                        token.email = dbUser.email;\n                        token.name = dbUser.name;\n                    } catch (error) {\n                        console.error(\"Error handling Google sign in:\", error);\n                        throw error; // This will prevent sign in\n                    }\n                } else if (user) {\n                    // For credentials provider\n                    token.sub = user.id;\n                    token.role = user.role;\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            // Populate session with user data from token\n            if (token && session.user) {\n                session.user = {\n                    ...session.user,\n                    id: token.sub,\n                    role: token.role,\n                    email: token.email,\n                    name: token.name\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Allows relative callback URLs\n            if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\n            else if (new URL(url).origin === baseUrl) return url;\n            return baseUrl;\n        }\n    },\n    events: {\n        async signIn ({ user, account, profile, isNewUser }) {\n            // Log sign in events in development only\n            if (true) {\n                console.log(\"Sign in event:\", {\n                    userId: user.id,\n                    email: user.email,\n                    provider: account?.provider,\n                    isNewUser\n                });\n            }\n        }\n    },\n    pages: {\n        signIn: \"/login\",\n        signOut: \"/\",\n        error: \"/login\"\n    },\n    secret: process.env.NEXTAUTH_SECRET,\n    // Only enable debug in development when explicitly set\n    debug:  true && process.env.NEXTAUTH_DEBUG === \"true\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/db.ts":
/*!***********************!*\
  !*** ./app/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"error\"\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQ1hGLGdCQUFnQkUsTUFBTSxJQUN0QixJQUFJSCx3REFBWUEsQ0FBQztJQUNmSSxLQUFLO1FBQUM7S0FBUTtBQUNoQixHQUFFO0FBRUosSUFBSUMsSUFBeUIsRUFBY0osZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL2FwcC9saWIvZGIudHM/NThmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz9cbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ2Vycm9yJ10sIC8vIE9ubHkgbG9nIGVycm9ycywgbm8gcXVlcmllcyBvciB3YXJuaW5nc1xuICB9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/email.ts":
/*!**************************!*\
  !*** ./app/lib/email.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sendEmail: () => (/* binding */ sendEmail),\n/* harmony export */   sendOrderConfirmationEmail: () => (/* binding */ sendOrderConfirmationEmail),\n/* harmony export */   sendOrderStatusUpdateEmail: () => (/* binding */ sendOrderStatusUpdateEmail),\n/* harmony export */   sendPasswordResetEmail: () => (/* binding */ sendPasswordResetEmail),\n/* harmony export */   sendTestEmail: () => (/* binding */ sendTestEmail),\n/* harmony export */   sendWelcomeEmail: () => (/* binding */ sendWelcomeEmail)\n/* harmony export */ });\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nodemailer */ \"(rsc)/./node_modules/nodemailer/lib/nodemailer.js\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./logger */ \"(rsc)/./app/lib/logger.ts\");\n\n\n// Brevo API configuration\nconst BREVO_API_URL = \"https://api.brevo.com/v3/smtp/email\";\n// Send email using Brevo API\nasync function sendWithBrevoAPI(options) {\n    const brevoApiKey = process.env.BREVO_API_KEY;\n    if (!brevoApiKey) {\n        throw new Error(\"BREVO_API_KEY is not configured\");\n    }\n    const payload = {\n        sender: {\n            name: process.env.FROM_NAME || \"Herbalicious\",\n            email: process.env.FROM_EMAIL || \"<EMAIL>\"\n        },\n        to: [\n            {\n                email: options.to\n            }\n        ],\n        subject: options.subject,\n        htmlContent: options.html\n    };\n    const response = await fetch(BREVO_API_URL, {\n        method: \"POST\",\n        headers: {\n            \"Accept\": \"application/json\",\n            \"Content-Type\": \"application/json\",\n            \"api-key\": brevoApiKey\n        },\n        body: JSON.stringify(payload)\n    });\n    if (!response.ok) {\n        const errorData = await response.text();\n        _logger__WEBPACK_IMPORTED_MODULE_1__.logger.emailError(options.to, options.subject, new Error(`Brevo API error: ${response.status} - ${errorData}`));\n        throw new Error(`Failed to send email via Brevo API: ${response.status}`);\n    }\n    _logger__WEBPACK_IMPORTED_MODULE_1__.logger.emailSent(options.to, options.subject, \"brevo-api\");\n}\n// Create SMTP transporter (fallback)\nconst createTransporter = ()=>{\n    // Try Brevo SMTP first\n    if (process.env.SMTP_HOST) {\n        return nodemailer__WEBPACK_IMPORTED_MODULE_0__.createTransport({\n            host: process.env.SMTP_HOST,\n            port: parseInt(process.env.SMTP_PORT || \"587\"),\n            secure: false,\n            auth: {\n                user: process.env.SMTP_USER,\n                pass: process.env.SMTP_PASS\n            }\n        });\n    }\n    // Development fallback\n    if (true) {\n        return nodemailer__WEBPACK_IMPORTED_MODULE_0__.createTransport({\n            host: \"smtp.ethereal.email\",\n            port: 587,\n            secure: false,\n            auth: {\n                user: \"<EMAIL>\",\n                pass: \"ethereal.pass\"\n            }\n        });\n    }\n    throw new Error(\"No email configuration found\");\n};\n// Send email using SMTP (fallback)\nasync function sendWithSMTP(options) {\n    const transporter = createTransporter();\n    const mailOptions = {\n        from: options.from || process.env.FROM_EMAIL || \"<EMAIL>\",\n        to: options.to,\n        subject: options.subject,\n        html: options.html\n    };\n    await transporter.sendMail(mailOptions);\n    _logger__WEBPACK_IMPORTED_MODULE_1__.logger.emailSent(options.to, options.subject, \"smtp\");\n}\n// Main email sending function\nasync function sendEmail(options) {\n    try {\n        // Try Brevo API first if configured\n        if (process.env.BREVO_API_KEY) {\n            await sendWithBrevoAPI(options);\n            return;\n        }\n        // Fallback to SMTP\n        await sendWithSMTP(options);\n    } catch (error) {\n        _logger__WEBPACK_IMPORTED_MODULE_1__.logger.emailError(options.to, options.subject, error);\n        throw error;\n    }\n}\nasync function sendPasswordResetEmail(email, resetToken) {\n    const resetUrl = `${process.env.NEXTAUTH_URL}/reset-password?token=${resetToken}`;\n    const html = `\r\n    <div style=\"max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;\">\r\n      <h2 style=\"color: #2d5a27;\">Password Reset Request</h2>\r\n      <p>You have requested to reset your password for your Herbalicious account.</p>\r\n      <p>Click the button below to reset your password:</p>\r\n      <div style=\"text-align: center; margin: 30px 0;\">\r\n        <a href=\"${resetUrl}\" \r\n           style=\"background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;\">\r\n          Reset Password\r\n        </a>\r\n      </div>\r\n      <p>If the button doesn't work, copy and paste this link into your browser:</p>\r\n      <p style=\"word-break: break-all; color: #666;\">${resetUrl}</p>\r\n      <p style=\"color: #666; font-size: 14px; margin-top: 30px;\">\r\n        This link will expire in 1 hour. If you didn't request this password reset, please ignore this email.\r\n      </p>\r\n      <hr style=\"border: none; border-top: 1px solid #eee; margin: 30px 0;\">\r\n      <p style=\"color: #999; font-size: 12px;\">\r\n        This email was sent from Herbalicious. Please do not reply to this email.\r\n      </p>\r\n    </div>\r\n  `;\n    await sendEmail({\n        to: email,\n        subject: \"Password Reset Request - Herbalicious\",\n        html\n    });\n}\nasync function sendWelcomeEmail(email, name) {\n    const html = `\r\n    <div style=\"max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;\">\r\n      <h2 style=\"color: #2d5a27;\">Welcome to Herbalicious, ${name}!</h2>\r\n      <p>Thank you for joining our community of natural health enthusiasts.</p>\r\n      <p>We're excited to have you on board and look forward to helping you discover the best herbal products for your wellness journey.</p>\r\n      <div style=\"text-align: center; margin: 30px 0;\">\r\n        <a href=\"${process.env.NEXTAUTH_URL}/shop\" \r\n           style=\"background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;\">\r\n          Start Shopping\r\n        </a>\r\n      </div>\r\n      <p>If you have any questions, feel free to contact our support team.</p>\r\n      <hr style=\"border: none; border-top: 1px solid #eee; margin: 30px 0;\">\r\n      <p style=\"color: #999; font-size: 12px;\">\r\n        This email was sent from Herbalicious. Please do not reply to this email.\r\n      </p>\r\n    </div>\r\n  `;\n    await sendEmail({\n        to: email,\n        subject: \"Welcome to Herbalicious!\",\n        html\n    });\n}\nasync function sendOrderConfirmationEmail(email, orderDetails) {\n    const itemsHtml = orderDetails.items.map((item)=>`\r\n    <tr>\r\n      <td style=\"padding: 8px; border-bottom: 1px solid #eee;\">${item.name}</td>\r\n      <td style=\"padding: 8px; border-bottom: 1px solid #eee; text-align: center;\">${item.quantity}</td>\r\n      <td style=\"padding: 8px; border-bottom: 1px solid #eee; text-align: right;\">₹${item.price.toFixed(2)}</td>\r\n    </tr>\r\n  `).join(\"\");\n    const html = `\r\n    <div style=\"max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;\">\r\n      <h2 style=\"color: #2d5a27;\">Order Confirmation</h2>\r\n      <p>Thank you for your order! Here are the details:</p>\r\n      \r\n      <div style=\"background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;\">\r\n        <h3 style=\"margin: 0 0 10px 0;\">Order #${orderDetails.orderId}</h3>\r\n      </div>\r\n      \r\n      <table style=\"width: 100%; border-collapse: collapse; margin: 20px 0;\">\r\n        <thead>\r\n          <tr style=\"background-color: #f5f5f5;\">\r\n            <th style=\"padding: 12px; text-align: left; border-bottom: 2px solid #ddd;\">Item</th>\r\n            <th style=\"padding: 12px; text-align: center; border-bottom: 2px solid #ddd;\">Qty</th>\r\n            <th style=\"padding: 12px; text-align: right; border-bottom: 2px solid #ddd;\">Price</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          ${itemsHtml}\r\n        </tbody>\r\n        <tfoot>\r\n          <tr>\r\n            <td colspan=\"2\" style=\"padding: 12px; font-weight: bold; border-top: 2px solid #ddd;\">Total:</td>\r\n            <td style=\"padding: 12px; font-weight: bold; text-align: right; border-top: 2px solid #ddd;\">₹${orderDetails.total.toFixed(2)}</td>\r\n          </tr>\r\n        </tfoot>\r\n      </table>\r\n      \r\n      <div style=\"margin: 20px 0;\">\r\n        <h4>Shipping Address:</h4>\r\n        <p style=\"background-color: #f9f9f9; padding: 10px; border-radius: 5px;\">${orderDetails.shippingAddress}</p>\r\n      </div>\r\n      \r\n      <p>We'll send you another email when your order ships.</p>\r\n      \r\n      <hr style=\"border: none; border-top: 1px solid #eee; margin: 30px 0;\">\r\n      <p style=\"color: #999; font-size: 12px;\">\r\n        This email was sent from Herbalicious. Please do not reply to this email.\r\n      </p>\r\n    </div>\r\n  `;\n    await sendEmail({\n        to: email,\n        subject: `Order Confirmation - ${orderDetails.orderId}`,\n        html\n    });\n}\nasync function sendOrderStatusUpdateEmail(email, orderData) {\n    const statusMessages = {\n        \"CONFIRMED\": \"Your order has been confirmed and is being prepared.\",\n        \"PROCESSING\": \"Your order is currently being processed.\",\n        \"SHIPPED\": \"Your order has been shipped and is on its way to you.\",\n        \"DELIVERED\": \"Your order has been delivered successfully.\",\n        \"CANCELLED\": \"Your order has been cancelled.\",\n        \"REFUNDED\": \"Your order has been refunded.\"\n    };\n    const statusMessage = statusMessages[orderData.status] || \"Your order status has been updated.\";\n    const html = `\r\n    <div style=\"max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;\">\r\n      <h2 style=\"color: #2d5a27;\">Order Status Update</h2>\r\n      <p>Your order <strong>#${orderData.orderNumber}</strong> status has been updated.</p>\r\n      \r\n      <div style=\"background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;\">\r\n        <h3 style=\"margin: 0 0 10px 0; color: #2d5a27;\">Current Status: ${orderData.status}</h3>\r\n        <p style=\"margin: 0;\">${statusMessage}</p>\r\n      </div>\r\n      \r\n      \r\n      <p>Thank you for shopping with Herbalicious!</p>\r\n      \r\n      <hr style=\"border: none; border-top: 1px solid #eee; margin: 30px 0;\">\r\n      <p style=\"color: #999; font-size: 12px;\">\r\n        This email was sent from Herbalicious. Please do not reply to this email.\r\n      </p>\r\n    </div>\r\n  `;\n    await sendEmail({\n        to: email,\n        subject: `Order Update - ${orderData.orderNumber}`,\n        html\n    });\n}\n// Test email function for development\nasync function sendTestEmail(email) {\n    const html = `\r\n    <div style=\"max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;\">\r\n      <h2 style=\"color: #2d5a27;\">Email Configuration Test</h2>\r\n      <p>This is a test email to verify that your email configuration is working correctly.</p>\r\n      <p>If you received this email, your Brevo integration is working properly!</p>\r\n      <p style=\"color: #666; font-size: 14px; margin-top: 30px;\">\r\n        Sent at: ${new Date().toISOString()}\r\n      </p>\r\n      <hr style=\"border: none; border-top: 1px solid #eee; margin: 30px 0;\">\r\n      <p style=\"color: #999; font-size: 12px;\">\r\n        This email was sent from Herbalicious for testing purposes.\r\n      </p>\r\n    </div>\r\n  `;\n    await sendEmail({\n        to: email,\n        subject: \"Email Configuration Test - Herbalicious\",\n        html\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGliL2VtYWlsLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQW9DO0FBQ0Y7QUFFbEMsMEJBQTBCO0FBQzFCLE1BQU1FLGdCQUFnQjtBQVN0Qiw2QkFBNkI7QUFDN0IsZUFBZUMsaUJBQWlCQyxPQUFxQjtJQUNuRCxNQUFNQyxjQUFjQyxRQUFRQyxHQUFHLENBQUNDLGFBQWE7SUFFN0MsSUFBSSxDQUFDSCxhQUFhO1FBQ2hCLE1BQU0sSUFBSUksTUFBTTtJQUNsQjtJQUVBLE1BQU1DLFVBQVU7UUFDZEMsUUFBUTtZQUNOQyxNQUFNTixRQUFRQyxHQUFHLENBQUNNLFNBQVMsSUFBSTtZQUMvQkMsT0FBT1IsUUFBUUMsR0FBRyxDQUFDUSxVQUFVLElBQUk7UUFDbkM7UUFDQUMsSUFBSTtZQUNGO2dCQUNFRixPQUFPVixRQUFRWSxFQUFFO1lBQ25CO1NBQ0Q7UUFDREMsU0FBU2IsUUFBUWEsT0FBTztRQUN4QkMsYUFBYWQsUUFBUWUsSUFBSTtJQUMzQjtJQUVBLE1BQU1DLFdBQVcsTUFBTUMsTUFBTW5CLGVBQWU7UUFDMUNvQixRQUFRO1FBQ1JDLFNBQVM7WUFDUCxVQUFVO1lBQ1YsZ0JBQWdCO1lBQ2hCLFdBQVdsQjtRQUNiO1FBQ0FtQixNQUFNQyxLQUFLQyxTQUFTLENBQUNoQjtJQUN2QjtJQUVBLElBQUksQ0FBQ1UsU0FBU08sRUFBRSxFQUFFO1FBQ2hCLE1BQU1DLFlBQVksTUFBTVIsU0FBU1MsSUFBSTtRQUNyQzVCLDJDQUFNQSxDQUFDNkIsVUFBVSxDQUFDMUIsUUFBUVksRUFBRSxFQUFFWixRQUFRYSxPQUFPLEVBQUUsSUFBSVIsTUFBTSxDQUFDLGlCQUFpQixFQUFFVyxTQUFTVyxNQUFNLENBQUMsR0FBRyxFQUFFSCxVQUFVLENBQUM7UUFDN0csTUFBTSxJQUFJbkIsTUFBTSxDQUFDLG9DQUFvQyxFQUFFVyxTQUFTVyxNQUFNLENBQUMsQ0FBQztJQUMxRTtJQUVBOUIsMkNBQU1BLENBQUMrQixTQUFTLENBQUM1QixRQUFRWSxFQUFFLEVBQUVaLFFBQVFhLE9BQU8sRUFBRTtBQUNoRDtBQUVBLHFDQUFxQztBQUNyQyxNQUFNZ0Isb0JBQW9CO0lBQ3hCLHVCQUF1QjtJQUN2QixJQUFJM0IsUUFBUUMsR0FBRyxDQUFDMkIsU0FBUyxFQUFFO1FBQ3pCLE9BQU9sQyx1REFBMEIsQ0FBQztZQUNoQ29DLE1BQU05QixRQUFRQyxHQUFHLENBQUMyQixTQUFTO1lBQzNCRyxNQUFNQyxTQUFTaEMsUUFBUUMsR0FBRyxDQUFDZ0MsU0FBUyxJQUFJO1lBQ3hDQyxRQUFRO1lBQ1JDLE1BQU07Z0JBQ0pDLE1BQU1wQyxRQUFRQyxHQUFHLENBQUNvQyxTQUFTO2dCQUMzQkMsTUFBTXRDLFFBQVFDLEdBQUcsQ0FBQ3NDLFNBQVM7WUFDN0I7UUFDRjtJQUNGO0lBRUEsdUJBQXVCO0lBQ3ZCLElBQUl2QyxJQUF5QixFQUFlO1FBQzFDLE9BQU9OLHVEQUEwQixDQUFDO1lBQ2hDb0MsTUFBTTtZQUNOQyxNQUFNO1lBQ05HLFFBQVE7WUFDUkMsTUFBTTtnQkFDSkMsTUFBTTtnQkFDTkUsTUFBTTtZQUNSO1FBQ0Y7SUFDRjtJQUVBLE1BQU0sSUFBSW5DLE1BQU07QUFDbEI7QUFFQSxtQ0FBbUM7QUFDbkMsZUFBZXFDLGFBQWExQyxPQUFxQjtJQUMvQyxNQUFNMkMsY0FBY2Q7SUFFcEIsTUFBTWUsY0FBYztRQUNsQkMsTUFBTTdDLFFBQVE2QyxJQUFJLElBQUkzQyxRQUFRQyxHQUFHLENBQUNRLFVBQVUsSUFBSTtRQUNoREMsSUFBSVosUUFBUVksRUFBRTtRQUNkQyxTQUFTYixRQUFRYSxPQUFPO1FBQ3hCRSxNQUFNZixRQUFRZSxJQUFJO0lBQ3BCO0lBRUEsTUFBTTRCLFlBQVlHLFFBQVEsQ0FBQ0Y7SUFDM0IvQywyQ0FBTUEsQ0FBQytCLFNBQVMsQ0FBQzVCLFFBQVFZLEVBQUUsRUFBRVosUUFBUWEsT0FBTyxFQUFFO0FBQ2hEO0FBRUEsOEJBQThCO0FBQ3ZCLGVBQWVrQyxVQUFVL0MsT0FBcUI7SUFDbkQsSUFBSTtRQUNGLG9DQUFvQztRQUNwQyxJQUFJRSxRQUFRQyxHQUFHLENBQUNDLGFBQWEsRUFBRTtZQUM3QixNQUFNTCxpQkFBaUJDO1lBQ3ZCO1FBQ0Y7UUFFQSxtQkFBbUI7UUFDbkIsTUFBTTBDLGFBQWExQztJQUNyQixFQUFFLE9BQU9nRCxPQUFPO1FBQ2RuRCwyQ0FBTUEsQ0FBQzZCLFVBQVUsQ0FBQzFCLFFBQVFZLEVBQUUsRUFBRVosUUFBUWEsT0FBTyxFQUFFbUM7UUFDL0MsTUFBTUE7SUFDUjtBQUNGO0FBRU8sZUFBZUMsdUJBQXVCdkMsS0FBYSxFQUFFd0MsVUFBa0I7SUFDNUUsTUFBTUMsV0FBVyxDQUFDLEVBQUVqRCxRQUFRQyxHQUFHLENBQUNpRCxZQUFZLENBQUMsc0JBQXNCLEVBQUVGLFdBQVcsQ0FBQztJQUVqRixNQUFNbkMsT0FBTyxDQUFDOzs7Ozs7aUJBTUMsRUFBRW9DLFNBQVM7Ozs7OztxREFNeUIsRUFBRUEsU0FBUzs7Ozs7Ozs7O0VBUzlELENBQUM7SUFFRCxNQUFNSixVQUFVO1FBQ2RuQyxJQUFJRjtRQUNKRyxTQUFTO1FBQ1RFO0lBQ0Y7QUFDRjtBQUVPLGVBQWVzQyxpQkFBaUIzQyxLQUFhLEVBQUVGLElBQVk7SUFDaEUsTUFBTU8sT0FBTyxDQUFDOzsyREFFMkMsRUFBRVAsS0FBSzs7OztpQkFJakQsRUFBRU4sUUFBUUMsR0FBRyxDQUFDaUQsWUFBWSxDQUFDOzs7Ozs7Ozs7OztFQVcxQyxDQUFDO0lBRUQsTUFBTUwsVUFBVTtRQUNkbkMsSUFBSUY7UUFDSkcsU0FBUztRQUNURTtJQUNGO0FBQ0Y7QUFFTyxlQUFldUMsMkJBQ3BCNUMsS0FBYSxFQUNiNkMsWUFLQztJQUVELE1BQU1DLFlBQVlELGFBQWFFLEtBQUssQ0FBQ0MsR0FBRyxDQUFDQyxDQUFBQSxPQUFRLENBQUM7OytEQUVXLEVBQUVBLEtBQUtuRCxJQUFJLENBQUM7bUZBQ1EsRUFBRW1ELEtBQUtDLFFBQVEsQ0FBQzttRkFDaEIsRUFBRUQsS0FBS0UsS0FBSyxDQUFDQyxPQUFPLENBQUMsR0FBRzs7RUFFekcsQ0FBQyxFQUFFQyxJQUFJLENBQUM7SUFFUixNQUFNaEQsT0FBTyxDQUFDOzs7Ozs7K0NBTStCLEVBQUV3QyxhQUFhUyxPQUFPLENBQUM7Ozs7Ozs7Ozs7OztVQVk1RCxFQUFFUixVQUFVOzs7OzswR0FLb0YsRUFBRUQsYUFBYVUsS0FBSyxDQUFDSCxPQUFPLENBQUMsR0FBRzs7Ozs7OztpRkFPekQsRUFBRVAsYUFBYVcsZUFBZSxDQUFDOzs7Ozs7Ozs7O0VBVTlHLENBQUM7SUFFRCxNQUFNbkIsVUFBVTtRQUNkbkMsSUFBSUY7UUFDSkcsU0FBUyxDQUFDLHFCQUFxQixFQUFFMEMsYUFBYVMsT0FBTyxDQUFDLENBQUM7UUFDdkRqRDtJQUNGO0FBQ0Y7QUFFTyxlQUFlb0QsMkJBQ3BCekQsS0FBYSxFQUNiMEQsU0FHQztJQUVELE1BQU1DLGlCQUF5QztRQUM3QyxhQUFhO1FBQ2IsY0FBYztRQUNkLFdBQVc7UUFDWCxhQUFhO1FBQ2IsYUFBYTtRQUNiLFlBQVk7SUFDZDtJQUVBLE1BQU1DLGdCQUFnQkQsY0FBYyxDQUFDRCxVQUFVekMsTUFBTSxDQUFDLElBQUk7SUFFMUQsTUFBTVosT0FBTyxDQUFDOzs7NkJBR2EsRUFBRXFELFVBQVVHLFdBQVcsQ0FBQzs7O3dFQUdtQixFQUFFSCxVQUFVekMsTUFBTSxDQUFDOzhCQUM3RCxFQUFFMkMsY0FBYzs7Ozs7Ozs7Ozs7RUFXNUMsQ0FBQztJQUVELE1BQU12QixVQUFVO1FBQ2RuQyxJQUFJRjtRQUNKRyxTQUFTLENBQUMsZUFBZSxFQUFFdUQsVUFBVUcsV0FBVyxDQUFDLENBQUM7UUFDbER4RDtJQUNGO0FBQ0Y7QUFFQSxzQ0FBc0M7QUFDL0IsZUFBZXlELGNBQWM5RCxLQUFhO0lBQy9DLE1BQU1LLE9BQU8sQ0FBQzs7Ozs7O2lCQU1DLEVBQUUsSUFBSTBELE9BQU9DLFdBQVcsR0FBRzs7Ozs7OztFQU8xQyxDQUFDO0lBRUQsTUFBTTNCLFVBQVU7UUFDZG5DLElBQUlGO1FBQ0pHLFNBQVM7UUFDVEU7SUFDRjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVyYmFsaWNpb3VzLW5leHRqcy8uL2FwcC9saWIvZW1haWwudHM/NzJkZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbm9kZW1haWxlciBmcm9tICdub2RlbWFpbGVyJztcclxuaW1wb3J0IHsgbG9nZ2VyIH0gZnJvbSAnLi9sb2dnZXInO1xyXG5cclxuLy8gQnJldm8gQVBJIGNvbmZpZ3VyYXRpb25cclxuY29uc3QgQlJFVk9fQVBJX1VSTCA9ICdodHRwczovL2FwaS5icmV2by5jb20vdjMvc210cC9lbWFpbCc7XHJcblxyXG5pbnRlcmZhY2UgRW1haWxPcHRpb25zIHtcclxuICB0bzogc3RyaW5nO1xyXG4gIHN1YmplY3Q6IHN0cmluZztcclxuICBodG1sOiBzdHJpbmc7XHJcbiAgZnJvbT86IHN0cmluZztcclxufVxyXG5cclxuLy8gU2VuZCBlbWFpbCB1c2luZyBCcmV2byBBUElcclxuYXN5bmMgZnVuY3Rpb24gc2VuZFdpdGhCcmV2b0FQSShvcHRpb25zOiBFbWFpbE9wdGlvbnMpOiBQcm9taXNlPHZvaWQ+IHtcclxuICBjb25zdCBicmV2b0FwaUtleSA9IHByb2Nlc3MuZW52LkJSRVZPX0FQSV9LRVk7XHJcbiAgXHJcbiAgaWYgKCFicmV2b0FwaUtleSkge1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKCdCUkVWT19BUElfS0VZIGlzIG5vdCBjb25maWd1cmVkJyk7XHJcbiAgfVxyXG5cclxuICBjb25zdCBwYXlsb2FkID0ge1xyXG4gICAgc2VuZGVyOiB7XHJcbiAgICAgIG5hbWU6IHByb2Nlc3MuZW52LkZST01fTkFNRSB8fCAnSGVyYmFsaWNpb3VzJyxcclxuICAgICAgZW1haWw6IHByb2Nlc3MuZW52LkZST01fRU1BSUwgfHwgJ25vcmVwbHlAaGVyYmFsaWNpb3VzLmNvbSdcclxuICAgIH0sXHJcbiAgICB0bzogW1xyXG4gICAgICB7XHJcbiAgICAgICAgZW1haWw6IG9wdGlvbnMudG9cclxuICAgICAgfVxyXG4gICAgXSxcclxuICAgIHN1YmplY3Q6IG9wdGlvbnMuc3ViamVjdCxcclxuICAgIGh0bWxDb250ZW50OiBvcHRpb25zLmh0bWxcclxuICB9O1xyXG5cclxuICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKEJSRVZPX0FQSV9VUkwsIHtcclxuICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgaGVhZGVyczoge1xyXG4gICAgICAnQWNjZXB0JzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAnYXBpLWtleSc6IGJyZXZvQXBpS2V5XHJcbiAgICB9LFxyXG4gICAgYm9keTogSlNPTi5zdHJpbmdpZnkocGF5bG9hZClcclxuICB9KTtcclxuXHJcbiAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xyXG4gICAgbG9nZ2VyLmVtYWlsRXJyb3Iob3B0aW9ucy50bywgb3B0aW9ucy5zdWJqZWN0LCBuZXcgRXJyb3IoYEJyZXZvIEFQSSBlcnJvcjogJHtyZXNwb25zZS5zdGF0dXN9IC0gJHtlcnJvckRhdGF9YCkpO1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gc2VuZCBlbWFpbCB2aWEgQnJldm8gQVBJOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcclxuICB9XHJcblxyXG4gIGxvZ2dlci5lbWFpbFNlbnQob3B0aW9ucy50bywgb3B0aW9ucy5zdWJqZWN0LCAnYnJldm8tYXBpJyk7XHJcbn1cclxuXHJcbi8vIENyZWF0ZSBTTVRQIHRyYW5zcG9ydGVyIChmYWxsYmFjaylcclxuY29uc3QgY3JlYXRlVHJhbnNwb3J0ZXIgPSAoKSA9PiB7XHJcbiAgLy8gVHJ5IEJyZXZvIFNNVFAgZmlyc3RcclxuICBpZiAocHJvY2Vzcy5lbnYuU01UUF9IT1NUKSB7XHJcbiAgICByZXR1cm4gbm9kZW1haWxlci5jcmVhdGVUcmFuc3BvcnQoe1xyXG4gICAgICBob3N0OiBwcm9jZXNzLmVudi5TTVRQX0hPU1QsXHJcbiAgICAgIHBvcnQ6IHBhcnNlSW50KHByb2Nlc3MuZW52LlNNVFBfUE9SVCB8fCAnNTg3JyksXHJcbiAgICAgIHNlY3VyZTogZmFsc2UsIC8vIHRydWUgZm9yIDQ2NSwgZmFsc2UgZm9yIG90aGVyIHBvcnRzXHJcbiAgICAgIGF1dGg6IHtcclxuICAgICAgICB1c2VyOiBwcm9jZXNzLmVudi5TTVRQX1VTRVIsXHJcbiAgICAgICAgcGFzczogcHJvY2Vzcy5lbnYuU01UUF9QQVNTLFxyXG4gICAgICB9LFxyXG4gICAgfSk7XHJcbiAgfVxyXG4gIFxyXG4gIC8vIERldmVsb3BtZW50IGZhbGxiYWNrXHJcbiAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7XHJcbiAgICByZXR1cm4gbm9kZW1haWxlci5jcmVhdGVUcmFuc3BvcnQoe1xyXG4gICAgICBob3N0OiAnc210cC5ldGhlcmVhbC5lbWFpbCcsXHJcbiAgICAgIHBvcnQ6IDU4NyxcclxuICAgICAgc2VjdXJlOiBmYWxzZSxcclxuICAgICAgYXV0aDoge1xyXG4gICAgICAgIHVzZXI6ICdldGhlcmVhbC51c2VyQGV0aGVyZWFsLmVtYWlsJyxcclxuICAgICAgICBwYXNzOiAnZXRoZXJlYWwucGFzcycsXHJcbiAgICAgIH0sXHJcbiAgICB9KTtcclxuICB9XHJcblxyXG4gIHRocm93IG5ldyBFcnJvcignTm8gZW1haWwgY29uZmlndXJhdGlvbiBmb3VuZCcpO1xyXG59O1xyXG5cclxuLy8gU2VuZCBlbWFpbCB1c2luZyBTTVRQIChmYWxsYmFjaylcclxuYXN5bmMgZnVuY3Rpb24gc2VuZFdpdGhTTVRQKG9wdGlvbnM6IEVtYWlsT3B0aW9ucyk6IFByb21pc2U8dm9pZD4ge1xyXG4gIGNvbnN0IHRyYW5zcG9ydGVyID0gY3JlYXRlVHJhbnNwb3J0ZXIoKTtcclxuICBcclxuICBjb25zdCBtYWlsT3B0aW9ucyA9IHtcclxuICAgIGZyb206IG9wdGlvbnMuZnJvbSB8fCBwcm9jZXNzLmVudi5GUk9NX0VNQUlMIHx8ICdub3JlcGx5QGhlcmJhbGljaW91cy5jb20nLFxyXG4gICAgdG86IG9wdGlvbnMudG8sXHJcbiAgICBzdWJqZWN0OiBvcHRpb25zLnN1YmplY3QsXHJcbiAgICBodG1sOiBvcHRpb25zLmh0bWwsXHJcbiAgfTtcclxuXHJcbiAgYXdhaXQgdHJhbnNwb3J0ZXIuc2VuZE1haWwobWFpbE9wdGlvbnMpO1xyXG4gIGxvZ2dlci5lbWFpbFNlbnQob3B0aW9ucy50bywgb3B0aW9ucy5zdWJqZWN0LCAnc210cCcpO1xyXG59XHJcblxyXG4vLyBNYWluIGVtYWlsIHNlbmRpbmcgZnVuY3Rpb25cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHNlbmRFbWFpbChvcHRpb25zOiBFbWFpbE9wdGlvbnMpOiBQcm9taXNlPHZvaWQ+IHtcclxuICB0cnkge1xyXG4gICAgLy8gVHJ5IEJyZXZvIEFQSSBmaXJzdCBpZiBjb25maWd1cmVkXHJcbiAgICBpZiAocHJvY2Vzcy5lbnYuQlJFVk9fQVBJX0tFWSkge1xyXG4gICAgICBhd2FpdCBzZW5kV2l0aEJyZXZvQVBJKG9wdGlvbnMpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgLy8gRmFsbGJhY2sgdG8gU01UUFxyXG4gICAgYXdhaXQgc2VuZFdpdGhTTVRQKG9wdGlvbnMpO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBsb2dnZXIuZW1haWxFcnJvcihvcHRpb25zLnRvLCBvcHRpb25zLnN1YmplY3QsIGVycm9yIGFzIEVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHNlbmRQYXNzd29yZFJlc2V0RW1haWwoZW1haWw6IHN0cmluZywgcmVzZXRUb2tlbjogc3RyaW5nKSB7XHJcbiAgY29uc3QgcmVzZXRVcmwgPSBgJHtwcm9jZXNzLmVudi5ORVhUQVVUSF9VUkx9L3Jlc2V0LXBhc3N3b3JkP3Rva2VuPSR7cmVzZXRUb2tlbn1gO1xyXG4gIFxyXG4gIGNvbnN0IGh0bWwgPSBgXHJcbiAgICA8ZGl2IHN0eWxlPVwibWF4LXdpZHRoOiA2MDBweDsgbWFyZ2luOiAwIGF1dG87IHBhZGRpbmc6IDIwcHg7IGZvbnQtZmFtaWx5OiBBcmlhbCwgc2Fucy1zZXJpZjtcIj5cclxuICAgICAgPGgyIHN0eWxlPVwiY29sb3I6ICMyZDVhMjc7XCI+UGFzc3dvcmQgUmVzZXQgUmVxdWVzdDwvaDI+XHJcbiAgICAgIDxwPllvdSBoYXZlIHJlcXVlc3RlZCB0byByZXNldCB5b3VyIHBhc3N3b3JkIGZvciB5b3VyIEhlcmJhbGljaW91cyBhY2NvdW50LjwvcD5cclxuICAgICAgPHA+Q2xpY2sgdGhlIGJ1dHRvbiBiZWxvdyB0byByZXNldCB5b3VyIHBhc3N3b3JkOjwvcD5cclxuICAgICAgPGRpdiBzdHlsZT1cInRleHQtYWxpZ246IGNlbnRlcjsgbWFyZ2luOiAzMHB4IDA7XCI+XHJcbiAgICAgICAgPGEgaHJlZj1cIiR7cmVzZXRVcmx9XCIgXHJcbiAgICAgICAgICAgc3R5bGU9XCJiYWNrZ3JvdW5kLWNvbG9yOiAjMmQ1YTI3OyBjb2xvcjogd2hpdGU7IHBhZGRpbmc6IDEycHggMjRweDsgdGV4dC1kZWNvcmF0aW9uOiBub25lOyBib3JkZXItcmFkaXVzOiA1cHg7IGRpc3BsYXk6IGlubGluZS1ibG9jaztcIj5cclxuICAgICAgICAgIFJlc2V0IFBhc3N3b3JkXHJcbiAgICAgICAgPC9hPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPHA+SWYgdGhlIGJ1dHRvbiBkb2Vzbid0IHdvcmssIGNvcHkgYW5kIHBhc3RlIHRoaXMgbGluayBpbnRvIHlvdXIgYnJvd3Nlcjo8L3A+XHJcbiAgICAgIDxwIHN0eWxlPVwid29yZC1icmVhazogYnJlYWstYWxsOyBjb2xvcjogIzY2NjtcIj4ke3Jlc2V0VXJsfTwvcD5cclxuICAgICAgPHAgc3R5bGU9XCJjb2xvcjogIzY2NjsgZm9udC1zaXplOiAxNHB4OyBtYXJnaW4tdG9wOiAzMHB4O1wiPlxyXG4gICAgICAgIFRoaXMgbGluayB3aWxsIGV4cGlyZSBpbiAxIGhvdXIuIElmIHlvdSBkaWRuJ3QgcmVxdWVzdCB0aGlzIHBhc3N3b3JkIHJlc2V0LCBwbGVhc2UgaWdub3JlIHRoaXMgZW1haWwuXHJcbiAgICAgIDwvcD5cclxuICAgICAgPGhyIHN0eWxlPVwiYm9yZGVyOiBub25lOyBib3JkZXItdG9wOiAxcHggc29saWQgI2VlZTsgbWFyZ2luOiAzMHB4IDA7XCI+XHJcbiAgICAgIDxwIHN0eWxlPVwiY29sb3I6ICM5OTk7IGZvbnQtc2l6ZTogMTJweDtcIj5cclxuICAgICAgICBUaGlzIGVtYWlsIHdhcyBzZW50IGZyb20gSGVyYmFsaWNpb3VzLiBQbGVhc2UgZG8gbm90IHJlcGx5IHRvIHRoaXMgZW1haWwuXHJcbiAgICAgIDwvcD5cclxuICAgIDwvZGl2PlxyXG4gIGA7XHJcblxyXG4gIGF3YWl0IHNlbmRFbWFpbCh7XHJcbiAgICB0bzogZW1haWwsXHJcbiAgICBzdWJqZWN0OiAnUGFzc3dvcmQgUmVzZXQgUmVxdWVzdCAtIEhlcmJhbGljaW91cycsXHJcbiAgICBodG1sXHJcbiAgfSk7XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzZW5kV2VsY29tZUVtYWlsKGVtYWlsOiBzdHJpbmcsIG5hbWU6IHN0cmluZykge1xyXG4gIGNvbnN0IGh0bWwgPSBgXHJcbiAgICA8ZGl2IHN0eWxlPVwibWF4LXdpZHRoOiA2MDBweDsgbWFyZ2luOiAwIGF1dG87IHBhZGRpbmc6IDIwcHg7IGZvbnQtZmFtaWx5OiBBcmlhbCwgc2Fucy1zZXJpZjtcIj5cclxuICAgICAgPGgyIHN0eWxlPVwiY29sb3I6ICMyZDVhMjc7XCI+V2VsY29tZSB0byBIZXJiYWxpY2lvdXMsICR7bmFtZX0hPC9oMj5cclxuICAgICAgPHA+VGhhbmsgeW91IGZvciBqb2luaW5nIG91ciBjb21tdW5pdHkgb2YgbmF0dXJhbCBoZWFsdGggZW50aHVzaWFzdHMuPC9wPlxyXG4gICAgICA8cD5XZSdyZSBleGNpdGVkIHRvIGhhdmUgeW91IG9uIGJvYXJkIGFuZCBsb29rIGZvcndhcmQgdG8gaGVscGluZyB5b3UgZGlzY292ZXIgdGhlIGJlc3QgaGVyYmFsIHByb2R1Y3RzIGZvciB5b3VyIHdlbGxuZXNzIGpvdXJuZXkuPC9wPlxyXG4gICAgICA8ZGl2IHN0eWxlPVwidGV4dC1hbGlnbjogY2VudGVyOyBtYXJnaW46IDMwcHggMDtcIj5cclxuICAgICAgICA8YSBocmVmPVwiJHtwcm9jZXNzLmVudi5ORVhUQVVUSF9VUkx9L3Nob3BcIiBcclxuICAgICAgICAgICBzdHlsZT1cImJhY2tncm91bmQtY29sb3I6ICMyZDVhMjc7IGNvbG9yOiB3aGl0ZTsgcGFkZGluZzogMTJweCAyNHB4OyB0ZXh0LWRlY29yYXRpb246IG5vbmU7IGJvcmRlci1yYWRpdXM6IDVweDsgZGlzcGxheTogaW5saW5lLWJsb2NrO1wiPlxyXG4gICAgICAgICAgU3RhcnQgU2hvcHBpbmdcclxuICAgICAgICA8L2E+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgICA8cD5JZiB5b3UgaGF2ZSBhbnkgcXVlc3Rpb25zLCBmZWVsIGZyZWUgdG8gY29udGFjdCBvdXIgc3VwcG9ydCB0ZWFtLjwvcD5cclxuICAgICAgPGhyIHN0eWxlPVwiYm9yZGVyOiBub25lOyBib3JkZXItdG9wOiAxcHggc29saWQgI2VlZTsgbWFyZ2luOiAzMHB4IDA7XCI+XHJcbiAgICAgIDxwIHN0eWxlPVwiY29sb3I6ICM5OTk7IGZvbnQtc2l6ZTogMTJweDtcIj5cclxuICAgICAgICBUaGlzIGVtYWlsIHdhcyBzZW50IGZyb20gSGVyYmFsaWNpb3VzLiBQbGVhc2UgZG8gbm90IHJlcGx5IHRvIHRoaXMgZW1haWwuXHJcbiAgICAgIDwvcD5cclxuICAgIDwvZGl2PlxyXG4gIGA7XHJcblxyXG4gIGF3YWl0IHNlbmRFbWFpbCh7XHJcbiAgICB0bzogZW1haWwsXHJcbiAgICBzdWJqZWN0OiAnV2VsY29tZSB0byBIZXJiYWxpY2lvdXMhJyxcclxuICAgIGh0bWxcclxuICB9KTtcclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHNlbmRPcmRlckNvbmZpcm1hdGlvbkVtYWlsKFxyXG4gIGVtYWlsOiBzdHJpbmcsIFxyXG4gIG9yZGVyRGV0YWlsczoge1xyXG4gICAgb3JkZXJJZDogc3RyaW5nO1xyXG4gICAgaXRlbXM6IEFycmF5PHsgbmFtZTogc3RyaW5nOyBxdWFudGl0eTogbnVtYmVyOyBwcmljZTogbnVtYmVyIH0+O1xyXG4gICAgdG90YWw6IG51bWJlcjtcclxuICAgIHNoaXBwaW5nQWRkcmVzczogc3RyaW5nO1xyXG4gIH1cclxuKSB7XHJcbiAgY29uc3QgaXRlbXNIdG1sID0gb3JkZXJEZXRhaWxzLml0ZW1zLm1hcChpdGVtID0+IGBcclxuICAgIDx0cj5cclxuICAgICAgPHRkIHN0eWxlPVwicGFkZGluZzogOHB4OyBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2VlZTtcIj4ke2l0ZW0ubmFtZX08L3RkPlxyXG4gICAgICA8dGQgc3R5bGU9XCJwYWRkaW5nOiA4cHg7IGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWVlOyB0ZXh0LWFsaWduOiBjZW50ZXI7XCI+JHtpdGVtLnF1YW50aXR5fTwvdGQ+XHJcbiAgICAgIDx0ZCBzdHlsZT1cInBhZGRpbmc6IDhweDsgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlZWU7IHRleHQtYWxpZ246IHJpZ2h0O1wiPuKCuSR7aXRlbS5wcmljZS50b0ZpeGVkKDIpfTwvdGQ+XHJcbiAgICA8L3RyPlxyXG4gIGApLmpvaW4oJycpO1xyXG4gIFxyXG4gIGNvbnN0IGh0bWwgPSBgXHJcbiAgICA8ZGl2IHN0eWxlPVwibWF4LXdpZHRoOiA2MDBweDsgbWFyZ2luOiAwIGF1dG87IHBhZGRpbmc6IDIwcHg7IGZvbnQtZmFtaWx5OiBBcmlhbCwgc2Fucy1zZXJpZjtcIj5cclxuICAgICAgPGgyIHN0eWxlPVwiY29sb3I6ICMyZDVhMjc7XCI+T3JkZXIgQ29uZmlybWF0aW9uPC9oMj5cclxuICAgICAgPHA+VGhhbmsgeW91IGZvciB5b3VyIG9yZGVyISBIZXJlIGFyZSB0aGUgZGV0YWlsczo8L3A+XHJcbiAgICAgIFxyXG4gICAgICA8ZGl2IHN0eWxlPVwiYmFja2dyb3VuZC1jb2xvcjogI2Y5ZjlmOTsgcGFkZGluZzogMTVweDsgYm9yZGVyLXJhZGl1czogNXB4OyBtYXJnaW46IDIwcHggMDtcIj5cclxuICAgICAgICA8aDMgc3R5bGU9XCJtYXJnaW46IDAgMCAxMHB4IDA7XCI+T3JkZXIgIyR7b3JkZXJEZXRhaWxzLm9yZGVySWR9PC9oMz5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIFxyXG4gICAgICA8dGFibGUgc3R5bGU9XCJ3aWR0aDogMTAwJTsgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsgbWFyZ2luOiAyMHB4IDA7XCI+XHJcbiAgICAgICAgPHRoZWFkPlxyXG4gICAgICAgICAgPHRyIHN0eWxlPVwiYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTtcIj5cclxuICAgICAgICAgICAgPHRoIHN0eWxlPVwicGFkZGluZzogMTJweDsgdGV4dC1hbGlnbjogbGVmdDsgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICNkZGQ7XCI+SXRlbTwvdGg+XHJcbiAgICAgICAgICAgIDx0aCBzdHlsZT1cInBhZGRpbmc6IDEycHg7IHRleHQtYWxpZ246IGNlbnRlcjsgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICNkZGQ7XCI+UXR5PC90aD5cclxuICAgICAgICAgICAgPHRoIHN0eWxlPVwicGFkZGluZzogMTJweDsgdGV4dC1hbGlnbjogcmlnaHQ7IGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjZGRkO1wiPlByaWNlPC90aD5cclxuICAgICAgICAgIDwvdHI+XHJcbiAgICAgICAgPC90aGVhZD5cclxuICAgICAgICA8dGJvZHk+XHJcbiAgICAgICAgICAke2l0ZW1zSHRtbH1cclxuICAgICAgICA8L3Rib2R5PlxyXG4gICAgICAgIDx0Zm9vdD5cclxuICAgICAgICAgIDx0cj5cclxuICAgICAgICAgICAgPHRkIGNvbHNwYW49XCIyXCIgc3R5bGU9XCJwYWRkaW5nOiAxMnB4OyBmb250LXdlaWdodDogYm9sZDsgYm9yZGVyLXRvcDogMnB4IHNvbGlkICNkZGQ7XCI+VG90YWw6PC90ZD5cclxuICAgICAgICAgICAgPHRkIHN0eWxlPVwicGFkZGluZzogMTJweDsgZm9udC13ZWlnaHQ6IGJvbGQ7IHRleHQtYWxpZ246IHJpZ2h0OyBib3JkZXItdG9wOiAycHggc29saWQgI2RkZDtcIj7igrkke29yZGVyRGV0YWlscy50b3RhbC50b0ZpeGVkKDIpfTwvdGQ+XHJcbiAgICAgICAgICA8L3RyPlxyXG4gICAgICAgIDwvdGZvb3Q+XHJcbiAgICAgIDwvdGFibGU+XHJcbiAgICAgIFxyXG4gICAgICA8ZGl2IHN0eWxlPVwibWFyZ2luOiAyMHB4IDA7XCI+XHJcbiAgICAgICAgPGg0PlNoaXBwaW5nIEFkZHJlc3M6PC9oND5cclxuICAgICAgICA8cCBzdHlsZT1cImJhY2tncm91bmQtY29sb3I6ICNmOWY5Zjk7IHBhZGRpbmc6IDEwcHg7IGJvcmRlci1yYWRpdXM6IDVweDtcIj4ke29yZGVyRGV0YWlscy5zaGlwcGluZ0FkZHJlc3N9PC9wPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgXHJcbiAgICAgIDxwPldlJ2xsIHNlbmQgeW91IGFub3RoZXIgZW1haWwgd2hlbiB5b3VyIG9yZGVyIHNoaXBzLjwvcD5cclxuICAgICAgXHJcbiAgICAgIDxociBzdHlsZT1cImJvcmRlcjogbm9uZTsgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlZWU7IG1hcmdpbjogMzBweCAwO1wiPlxyXG4gICAgICA8cCBzdHlsZT1cImNvbG9yOiAjOTk5OyBmb250LXNpemU6IDEycHg7XCI+XHJcbiAgICAgICAgVGhpcyBlbWFpbCB3YXMgc2VudCBmcm9tIEhlcmJhbGljaW91cy4gUGxlYXNlIGRvIG5vdCByZXBseSB0byB0aGlzIGVtYWlsLlxyXG4gICAgICA8L3A+XHJcbiAgICA8L2Rpdj5cclxuICBgO1xyXG5cclxuICBhd2FpdCBzZW5kRW1haWwoe1xyXG4gICAgdG86IGVtYWlsLFxyXG4gICAgc3ViamVjdDogYE9yZGVyIENvbmZpcm1hdGlvbiAtICR7b3JkZXJEZXRhaWxzLm9yZGVySWR9YCxcclxuICAgIGh0bWxcclxuICB9KTtcclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHNlbmRPcmRlclN0YXR1c1VwZGF0ZUVtYWlsKFxyXG4gIGVtYWlsOiBzdHJpbmcsXHJcbiAgb3JkZXJEYXRhOiB7XHJcbiAgICBvcmRlck51bWJlcjogc3RyaW5nO1xyXG4gICAgc3RhdHVzOiBzdHJpbmc7XHJcbiAgfVxyXG4pIHtcclxuICBjb25zdCBzdGF0dXNNZXNzYWdlczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcclxuICAgICdDT05GSVJNRUQnOiAnWW91ciBvcmRlciBoYXMgYmVlbiBjb25maXJtZWQgYW5kIGlzIGJlaW5nIHByZXBhcmVkLicsXHJcbiAgICAnUFJPQ0VTU0lORyc6ICdZb3VyIG9yZGVyIGlzIGN1cnJlbnRseSBiZWluZyBwcm9jZXNzZWQuJyxcclxuICAgICdTSElQUEVEJzogJ1lvdXIgb3JkZXIgaGFzIGJlZW4gc2hpcHBlZCBhbmQgaXMgb24gaXRzIHdheSB0byB5b3UuJyxcclxuICAgICdERUxJVkVSRUQnOiAnWW91ciBvcmRlciBoYXMgYmVlbiBkZWxpdmVyZWQgc3VjY2Vzc2Z1bGx5LicsXHJcbiAgICAnQ0FOQ0VMTEVEJzogJ1lvdXIgb3JkZXIgaGFzIGJlZW4gY2FuY2VsbGVkLicsXHJcbiAgICAnUkVGVU5ERUQnOiAnWW91ciBvcmRlciBoYXMgYmVlbiByZWZ1bmRlZC4nXHJcbiAgfTtcclxuXHJcbiAgY29uc3Qgc3RhdHVzTWVzc2FnZSA9IHN0YXR1c01lc3NhZ2VzW29yZGVyRGF0YS5zdGF0dXNdIHx8ICdZb3VyIG9yZGVyIHN0YXR1cyBoYXMgYmVlbiB1cGRhdGVkLic7XHJcbiAgXHJcbiAgY29uc3QgaHRtbCA9IGBcclxuICAgIDxkaXYgc3R5bGU9XCJtYXgtd2lkdGg6IDYwMHB4OyBtYXJnaW46IDAgYXV0bzsgcGFkZGluZzogMjBweDsgZm9udC1mYW1pbHk6IEFyaWFsLCBzYW5zLXNlcmlmO1wiPlxyXG4gICAgICA8aDIgc3R5bGU9XCJjb2xvcjogIzJkNWEyNztcIj5PcmRlciBTdGF0dXMgVXBkYXRlPC9oMj5cclxuICAgICAgPHA+WW91ciBvcmRlciA8c3Ryb25nPiMke29yZGVyRGF0YS5vcmRlck51bWJlcn08L3N0cm9uZz4gc3RhdHVzIGhhcyBiZWVuIHVwZGF0ZWQuPC9wPlxyXG4gICAgICBcclxuICAgICAgPGRpdiBzdHlsZT1cImJhY2tncm91bmQtY29sb3I6ICNmOWY5Zjk7IHBhZGRpbmc6IDE1cHg7IGJvcmRlci1yYWRpdXM6IDVweDsgbWFyZ2luOiAyMHB4IDA7XCI+XHJcbiAgICAgICAgPGgzIHN0eWxlPVwibWFyZ2luOiAwIDAgMTBweCAwOyBjb2xvcjogIzJkNWEyNztcIj5DdXJyZW50IFN0YXR1czogJHtvcmRlckRhdGEuc3RhdHVzfTwvaDM+XHJcbiAgICAgICAgPHAgc3R5bGU9XCJtYXJnaW46IDA7XCI+JHtzdGF0dXNNZXNzYWdlfTwvcD5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIFxyXG4gICAgICBcclxuICAgICAgPHA+VGhhbmsgeW91IGZvciBzaG9wcGluZyB3aXRoIEhlcmJhbGljaW91cyE8L3A+XHJcbiAgICAgIFxyXG4gICAgICA8aHIgc3R5bGU9XCJib3JkZXI6IG5vbmU7IGJvcmRlci10b3A6IDFweCBzb2xpZCAjZWVlOyBtYXJnaW46IDMwcHggMDtcIj5cclxuICAgICAgPHAgc3R5bGU9XCJjb2xvcjogIzk5OTsgZm9udC1zaXplOiAxMnB4O1wiPlxyXG4gICAgICAgIFRoaXMgZW1haWwgd2FzIHNlbnQgZnJvbSBIZXJiYWxpY2lvdXMuIFBsZWFzZSBkbyBub3QgcmVwbHkgdG8gdGhpcyBlbWFpbC5cclxuICAgICAgPC9wPlxyXG4gICAgPC9kaXY+XHJcbiAgYDtcclxuXHJcbiAgYXdhaXQgc2VuZEVtYWlsKHtcclxuICAgIHRvOiBlbWFpbCxcclxuICAgIHN1YmplY3Q6IGBPcmRlciBVcGRhdGUgLSAke29yZGVyRGF0YS5vcmRlck51bWJlcn1gLFxyXG4gICAgaHRtbFxyXG4gIH0pO1xyXG59XHJcblxyXG4vLyBUZXN0IGVtYWlsIGZ1bmN0aW9uIGZvciBkZXZlbG9wbWVudFxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc2VuZFRlc3RFbWFpbChlbWFpbDogc3RyaW5nKSB7XHJcbiAgY29uc3QgaHRtbCA9IGBcclxuICAgIDxkaXYgc3R5bGU9XCJtYXgtd2lkdGg6IDYwMHB4OyBtYXJnaW46IDAgYXV0bzsgcGFkZGluZzogMjBweDsgZm9udC1mYW1pbHk6IEFyaWFsLCBzYW5zLXNlcmlmO1wiPlxyXG4gICAgICA8aDIgc3R5bGU9XCJjb2xvcjogIzJkNWEyNztcIj5FbWFpbCBDb25maWd1cmF0aW9uIFRlc3Q8L2gyPlxyXG4gICAgICA8cD5UaGlzIGlzIGEgdGVzdCBlbWFpbCB0byB2ZXJpZnkgdGhhdCB5b3VyIGVtYWlsIGNvbmZpZ3VyYXRpb24gaXMgd29ya2luZyBjb3JyZWN0bHkuPC9wPlxyXG4gICAgICA8cD5JZiB5b3UgcmVjZWl2ZWQgdGhpcyBlbWFpbCwgeW91ciBCcmV2byBpbnRlZ3JhdGlvbiBpcyB3b3JraW5nIHByb3Blcmx5ITwvcD5cclxuICAgICAgPHAgc3R5bGU9XCJjb2xvcjogIzY2NjsgZm9udC1zaXplOiAxNHB4OyBtYXJnaW4tdG9wOiAzMHB4O1wiPlxyXG4gICAgICAgIFNlbnQgYXQ6ICR7bmV3IERhdGUoKS50b0lTT1N0cmluZygpfVxyXG4gICAgICA8L3A+XHJcbiAgICAgIDxociBzdHlsZT1cImJvcmRlcjogbm9uZTsgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlZWU7IG1hcmdpbjogMzBweCAwO1wiPlxyXG4gICAgICA8cCBzdHlsZT1cImNvbG9yOiAjOTk5OyBmb250LXNpemU6IDEycHg7XCI+XHJcbiAgICAgICAgVGhpcyBlbWFpbCB3YXMgc2VudCBmcm9tIEhlcmJhbGljaW91cyBmb3IgdGVzdGluZyBwdXJwb3Nlcy5cclxuICAgICAgPC9wPlxyXG4gICAgPC9kaXY+XHJcbiAgYDtcclxuXHJcbiAgYXdhaXQgc2VuZEVtYWlsKHtcclxuICAgIHRvOiBlbWFpbCxcclxuICAgIHN1YmplY3Q6ICdFbWFpbCBDb25maWd1cmF0aW9uIFRlc3QgLSBIZXJiYWxpY2lvdXMnLFxyXG4gICAgaHRtbFxyXG4gIH0pO1xyXG59Il0sIm5hbWVzIjpbIm5vZGVtYWlsZXIiLCJsb2dnZXIiLCJCUkVWT19BUElfVVJMIiwic2VuZFdpdGhCcmV2b0FQSSIsIm9wdGlvbnMiLCJicmV2b0FwaUtleSIsInByb2Nlc3MiLCJlbnYiLCJCUkVWT19BUElfS0VZIiwiRXJyb3IiLCJwYXlsb2FkIiwic2VuZGVyIiwibmFtZSIsIkZST01fTkFNRSIsImVtYWlsIiwiRlJPTV9FTUFJTCIsInRvIiwic3ViamVjdCIsImh0bWxDb250ZW50IiwiaHRtbCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJvayIsImVycm9yRGF0YSIsInRleHQiLCJlbWFpbEVycm9yIiwic3RhdHVzIiwiZW1haWxTZW50IiwiY3JlYXRlVHJhbnNwb3J0ZXIiLCJTTVRQX0hPU1QiLCJjcmVhdGVUcmFuc3BvcnQiLCJob3N0IiwicG9ydCIsInBhcnNlSW50IiwiU01UUF9QT1JUIiwic2VjdXJlIiwiYXV0aCIsInVzZXIiLCJTTVRQX1VTRVIiLCJwYXNzIiwiU01UUF9QQVNTIiwic2VuZFdpdGhTTVRQIiwidHJhbnNwb3J0ZXIiLCJtYWlsT3B0aW9ucyIsImZyb20iLCJzZW5kTWFpbCIsInNlbmRFbWFpbCIsImVycm9yIiwic2VuZFBhc3N3b3JkUmVzZXRFbWFpbCIsInJlc2V0VG9rZW4iLCJyZXNldFVybCIsIk5FWFRBVVRIX1VSTCIsInNlbmRXZWxjb21lRW1haWwiLCJzZW5kT3JkZXJDb25maXJtYXRpb25FbWFpbCIsIm9yZGVyRGV0YWlscyIsIml0ZW1zSHRtbCIsIml0ZW1zIiwibWFwIiwiaXRlbSIsInF1YW50aXR5IiwicHJpY2UiLCJ0b0ZpeGVkIiwiam9pbiIsIm9yZGVySWQiLCJ0b3RhbCIsInNoaXBwaW5nQWRkcmVzcyIsInNlbmRPcmRlclN0YXR1c1VwZGF0ZUVtYWlsIiwib3JkZXJEYXRhIiwic3RhdHVzTWVzc2FnZXMiLCJzdGF0dXNNZXNzYWdlIiwib3JkZXJOdW1iZXIiLCJzZW5kVGVzdEVtYWlsIiwiRGF0ZSIsInRvSVNPU3RyaW5nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/email.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/logger.ts":
/*!***************************!*\
  !*** ./app/lib/logger.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogLevel: () => (/* binding */ LogLevel),\n/* harmony export */   createRequestLogger: () => (/* binding */ createRequestLogger),\n/* harmony export */   devLog: () => (/* binding */ devLog),\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\nvar LogLevel;\n(function(LogLevel) {\n    LogLevel[LogLevel[\"ERROR\"] = 0] = \"ERROR\";\n    LogLevel[LogLevel[\"WARN\"] = 1] = \"WARN\";\n    LogLevel[LogLevel[\"INFO\"] = 2] = \"INFO\";\n    LogLevel[LogLevel[\"DEBUG\"] = 3] = \"DEBUG\";\n})(LogLevel || (LogLevel = {}));\nclass Logger {\n    constructor(){\n        this.isDevelopment = \"development\" === \"development\";\n        // Reduce verbosity in development - only show warnings and errors\n        this.logLevel = this.isDevelopment ? 1 : 2;\n    }\n    shouldLog(level) {\n        return level <= this.logLevel;\n    }\n    formatMessage(entry) {\n        const { timestamp, level, message, context, error, userId, requestId } = entry;\n        const levelName = LogLevel[level];\n        let formatted = `[${timestamp}] ${levelName}: ${message}`;\n        if (userId) {\n            formatted += ` | User: ${userId}`;\n        }\n        if (requestId) {\n            formatted += ` | Request: ${requestId}`;\n        }\n        if (context && Object.keys(context).length > 0) {\n            formatted += ` | Context: ${JSON.stringify(context)}`;\n        }\n        if (error) {\n            formatted += ` | Error: ${error.message}`;\n            if (this.isDevelopment && error.stack) {\n                formatted += `\\nStack: ${error.stack}`;\n            }\n        }\n        return formatted;\n    }\n    log(level, message, context, error) {\n        if (!this.shouldLog(level)) return;\n        const entry = {\n            timestamp: new Date().toISOString(),\n            level,\n            message,\n            context,\n            error\n        };\n        const formatted = this.formatMessage(entry);\n        // In development, use console methods for better formatting\n        if (this.isDevelopment) {\n            switch(level){\n                case 0:\n                    console.error(formatted);\n                    break;\n                case 1:\n                    console.warn(formatted);\n                    break;\n                case 2:\n                    console.info(formatted);\n                    break;\n                case 3:\n                    console.debug(formatted);\n                    break;\n            }\n        } else {\n            // In production, use structured logging (JSON format)\n            console.log(JSON.stringify(entry));\n        }\n    }\n    error(message, error, context) {\n        this.log(0, message, context, error);\n    }\n    warn(message, context) {\n        this.log(1, message, context);\n    }\n    info(message, context) {\n        this.log(2, message, context);\n    }\n    debug(message, context) {\n        this.log(3, message, context);\n    }\n    // API-specific logging methods\n    apiRequest(method, path, userId, context) {\n        this.info(`API ${method} ${path}`, {\n            ...context,\n            userId,\n            type: \"api_request\"\n        });\n    }\n    apiResponse(method, path, statusCode, duration, context) {\n        this.info(`API ${method} ${path} - ${statusCode}`, {\n            ...context,\n            statusCode,\n            duration,\n            type: \"api_response\"\n        });\n    }\n    apiError(method, path, error, userId, context) {\n        this.error(`API ${method} ${path} failed`, error, {\n            ...context,\n            userId,\n            type: \"api_error\"\n        });\n    }\n    // Authentication logging\n    authSuccess(userId, method, context) {\n        this.info(`Authentication successful`, {\n            ...context,\n            userId,\n            method,\n            type: \"auth_success\"\n        });\n    }\n    authFailure(email, method, reason, context) {\n        this.warn(`Authentication failed`, {\n            ...context,\n            email,\n            method,\n            reason,\n            type: \"auth_failure\"\n        });\n    }\n    // Database logging\n    dbQuery(operation, table, duration, context) {\n        this.debug(`DB ${operation} on ${table}`, {\n            ...context,\n            operation,\n            table,\n            duration,\n            type: \"db_query\"\n        });\n    }\n    dbError(operation, table, error, context) {\n        this.error(`DB ${operation} on ${table} failed`, error, {\n            ...context,\n            operation,\n            table,\n            type: \"db_error\"\n        });\n    }\n    // Security logging\n    securityEvent(event, severity, context) {\n        const level = severity === \"high\" ? 0 : severity === \"medium\" ? 1 : 2;\n        this.log(level, `Security event: ${event}`, {\n            ...context,\n            severity,\n            type: \"security_event\"\n        });\n    }\n    // Rate limiting logging\n    rateLimitHit(identifier, limit, window, context) {\n        this.warn(`Rate limit exceeded`, {\n            ...context,\n            identifier,\n            limit,\n            window,\n            type: \"rate_limit\"\n        });\n    }\n    // Email logging\n    emailSent(to, subject, template, context) {\n        this.info(`Email sent`, {\n            ...context,\n            to,\n            subject,\n            template,\n            type: \"email_sent\"\n        });\n    }\n    emailError(to, subject, error, context) {\n        this.error(`Email failed to send`, error, {\n            ...context,\n            to,\n            subject,\n            type: \"email_error\"\n        });\n    }\n    // Performance logging\n    performance(operation, duration, context) {\n        const level = duration > 5000 ? 1 : 3;\n        this.log(level, `Performance: ${operation} took ${duration}ms`, {\n            ...context,\n            operation,\n            duration,\n            type: \"performance\"\n        });\n    }\n}\n// Create singleton instance\nconst logger = new Logger();\n// Request logging middleware helper\nfunction createRequestLogger(req) {\n    const requestId = crypto.randomUUID();\n    const startTime = Date.now();\n    return {\n        requestId,\n        log: (message, context)=>{\n            logger.info(message, {\n                ...context,\n                requestId\n            });\n        },\n        error: (message, error, context)=>{\n            logger.error(message, error, {\n                ...context,\n                requestId\n            });\n        },\n        end: (statusCode)=>{\n            const duration = Date.now() - startTime;\n            logger.apiResponse(req.method || \"UNKNOWN\", new URL(req.url || \"\").pathname, statusCode, duration, {\n                requestId\n            });\n        }\n    };\n}\n// Development-only logging helpers\nconst devLog = {\n    info: (message, data)=>{\n        if (true) {\n            console.log(`🔍 ${message}`, data ? JSON.stringify(data, null, 2) : \"\");\n        }\n    },\n    error: (message, error)=>{\n        if (true) {\n            console.error(`❌ ${message}`, error);\n        }\n    },\n    warn: (message, data)=>{\n        if (true) {\n            console.warn(`⚠️ ${message}`, data ? JSON.stringify(data, null, 2) : \"\");\n        }\n    },\n    success: (message, data)=>{\n        if (true) {\n            console.log(`✅ ${message}`, data ? JSON.stringify(data, null, 2) : \"\");\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/logger.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/notification-helpers.ts":
/*!*****************************************!*\
  !*** ./app/lib/notification-helpers.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminNotifications: () => (/* binding */ adminNotifications),\n/* harmony export */   orderNotifications: () => (/* binding */ orderNotifications),\n/* harmony export */   reviewNotifications: () => (/* binding */ reviewNotifications),\n/* harmony export */   systemNotifications: () => (/* binding */ systemNotifications),\n/* harmony export */   wishlistNotifications: () => (/* binding */ wishlistNotifications)\n/* harmony export */ });\n/* harmony import */ var _notifications__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./notifications */ \"(rsc)/./app/lib/notifications.ts\");\n\n/**\n * Order notification helpers\n */ const orderNotifications = {\n    /**\n   * Send order placed notification\n   */ async orderPlaced (userId, orderData) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.createNotification({\n            userId,\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.ORDER_PLACED,\n            title: \"Order Placed Successfully\",\n            message: `Your order #${orderData.orderNumber} has been placed successfully. We'll send you updates as your order progresses.`,\n            data: {\n                orderId: orderData.orderId,\n                orderNumber: orderData.orderNumber,\n                amount: orderData.total,\n                currency: orderData.currency,\n                itemCount: orderData.itemCount\n            },\n            sendEmail: true\n        });\n    },\n    /**\n   * Send order confirmed notification\n   */ async orderConfirmed (userId, orderData) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.createNotification({\n            userId,\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.ORDER_CONFIRMED,\n            title: \"Order Confirmed\",\n            message: `Your order #${orderData.orderNumber} has been confirmed and is being prepared for shipment.${orderData.estimatedDelivery ? ` Estimated delivery: ${orderData.estimatedDelivery}` : \"\"}`,\n            data: {\n                orderId: orderData.orderId,\n                orderNumber: orderData.orderNumber,\n                estimatedDelivery: orderData.estimatedDelivery\n            },\n            sendEmail: true\n        });\n    },\n    /**\n   * Send order processing notification\n   */ async orderProcessing (userId, orderData) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.createNotification({\n            userId,\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.ORDER_PROCESSING,\n            title: \"Order Being Processed\",\n            message: `Your order #${orderData.orderNumber} is currently being processed. We'll notify you once it's shipped.`,\n            data: {\n                orderId: orderData.orderId,\n                orderNumber: orderData.orderNumber\n            },\n            sendEmail: true\n        });\n    },\n    /**\n   * Send order shipped notification\n   */ async orderShipped (userId, orderData) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.createNotification({\n            userId,\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.ORDER_SHIPPED,\n            title: \"Order Shipped\",\n            message: `Great news! Your order #${orderData.orderNumber} has been shipped.${orderData.estimatedDelivery ? ` Estimated delivery: ${orderData.estimatedDelivery}` : \"\"}`,\n            data: {\n                orderId: orderData.orderId,\n                orderNumber: orderData.orderNumber,\n                estimatedDelivery: orderData.estimatedDelivery\n            },\n            sendEmail: true\n        });\n    },\n    /**\n   * Send order delivered notification\n   */ async orderDelivered (userId, orderData) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.createNotification({\n            userId,\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.ORDER_DELIVERED,\n            title: \"Order Delivered\",\n            message: `Your order #${orderData.orderNumber} has been delivered successfully! We hope you love your natural skincare products.`,\n            data: {\n                orderId: orderData.orderId,\n                orderNumber: orderData.orderNumber,\n                deliveredAt: orderData.deliveredAt\n            },\n            sendEmail: true\n        });\n    },\n    /**\n   * Send order cancelled notification\n   */ async orderCancelled (userId, orderData) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.createNotification({\n            userId,\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.ORDER_CANCELLED,\n            title: \"Order Cancelled\",\n            message: `Your order #${orderData.orderNumber} has been cancelled.${orderData.reason ? ` Reason: ${orderData.reason}` : \"\"}${orderData.refundAmount ? ` A refund of ${orderData.currency} ${orderData.refundAmount} will be processed within 3-5 business days.` : \"\"}`,\n            data: {\n                orderId: orderData.orderId,\n                orderNumber: orderData.orderNumber,\n                reason: orderData.reason,\n                refundAmount: orderData.refundAmount,\n                currency: orderData.currency\n            },\n            sendEmail: true\n        });\n    }\n};\n/**\n * Wishlist notification helpers\n */ const wishlistNotifications = {\n    /**\n   * Send wishlist item added notification\n   */ async itemAdded (userId, productData) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.createNotification({\n            userId,\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.WISHLIST_ADDED,\n            title: \"Item Added to Wishlist\",\n            message: `${productData.productName} has been added to your wishlist. We'll notify you of any price changes!`,\n            data: {\n                productId: productData.productId,\n                productName: productData.productName,\n                price: productData.price,\n                currency: productData.currency\n            },\n            sendEmail: false\n        });\n    },\n    /**\n   * Send wishlist item removed notification\n   */ async itemRemoved (userId, productData) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.createNotification({\n            userId,\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.WISHLIST_REMOVED,\n            title: \"Item Removed from Wishlist\",\n            message: `${productData.productName} has been removed from your wishlist.`,\n            data: {\n                productId: productData.productId,\n                productName: productData.productName\n            },\n            sendEmail: false\n        });\n    },\n    /**\n   * Send price drop alert\n   */ async priceDropAlert (userId, productData) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.createNotification({\n            userId,\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.PRICE_DROP_ALERT,\n            title: \"Price Drop Alert!\",\n            message: `Great news! ${productData.productName} is now ${productData.discountPercentage}% off! Price dropped from ${productData.currency} ${productData.oldPrice} to ${productData.currency} ${productData.newPrice}.`,\n            data: {\n                productId: productData.productId,\n                productName: productData.productName,\n                oldPrice: productData.oldPrice,\n                newPrice: productData.newPrice,\n                currency: productData.currency,\n                discountPercentage: productData.discountPercentage\n            },\n            sendEmail: true,\n            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)\n        });\n    }\n};\n/**\n * Review notification helpers\n */ const reviewNotifications = {\n    /**\n   * Send review request notification\n   */ async reviewRequest (userId, orderData) {\n        const productList = orderData.productNames.join(\", \");\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.createNotification({\n            userId,\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.REVIEW_REQUEST,\n            title: \"How was your experience?\",\n            message: `We'd love to hear about your experience with ${productList}. Your review helps other customers make informed decisions!`,\n            data: {\n                orderId: orderData.orderId,\n                orderNumber: orderData.orderNumber,\n                productIds: orderData.productIds,\n                productNames: orderData.productNames\n            },\n            sendEmail: true,\n            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)\n        });\n    },\n    /**\n   * Send review submitted confirmation\n   */ async reviewSubmitted (userId, reviewData) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.createNotification({\n            userId,\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.REVIEW_SUBMITTED,\n            title: \"Review Submitted\",\n            message: `Thank you for your ${reviewData.rating}-star review of ${reviewData.productName}! Your feedback is valuable to us and other customers.`,\n            data: {\n                productId: reviewData.productId,\n                productName: reviewData.productName,\n                rating: reviewData.rating\n            },\n            sendEmail: false\n        });\n    }\n};\n/**\n * System notification helpers\n */ const systemNotifications = {\n    /**\n   * Send system notification\n   */ async sendSystemNotification (userId, messageData) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.createNotification({\n            userId,\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.SYSTEM,\n            title: messageData.title,\n            message: messageData.message,\n            data: messageData.data,\n            sendEmail: false\n        });\n    }\n};\n/**\n * Admin notification helpers\n */ const adminNotifications = {\n    /**\n   * Send admin message to user\n   */ async adminMessage (userId, data) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.createNotification({\n            userId,\n            type: data.type || _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.ADMIN_MESSAGE,\n            title: data.title,\n            message: data.content,\n            data: {\n                sentByAdmin: true,\n                sendEmail: data.sendEmail !== false,\n                sendInApp: data.sendInApp !== false\n            },\n            sendEmail: data.sendEmail !== false\n        });\n    },\n    /**\n   * Send system alert\n   */ async systemAlert (userId, data) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.createNotification({\n            userId,\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.SYSTEM,\n            title: data.title,\n            message: data.message,\n            data: {\n                severity: data.severity\n            },\n            sendEmail: data.severity === \"critical\" || data.severity === \"high\"\n        });\n    },\n    /**\n   * Send maintenance notice\n   */ async maintenanceNotice (userId, data) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.createNotification({\n            userId,\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.SYSTEM,\n            title: \"Scheduled Maintenance\",\n            message: `Our system will be under maintenance from ${data.startTime} to ${data.endTime}. ${data.description || \"We apologize for any inconvenience.\"}`,\n            data: {\n                maintenanceStart: data.startTime,\n                maintenanceEnd: data.endTime\n            },\n            sendEmail: true\n        });\n    },\n    /**\n   * Send admin message to specific user\n   */ async sendMessage (userId, messageData) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.createNotification({\n            userId,\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.ADMIN_MESSAGE,\n            title: messageData.title,\n            message: messageData.message,\n            sendEmail: messageData.sendEmail || false\n        });\n    },\n    /**\n   * Send broadcast message to all users\n   */ async sendBroadcast (messageData) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.sendBroadcast({\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.BROADCAST,\n            title: messageData.title,\n            message: messageData.message,\n            sendEmail: messageData.sendEmail || false,\n            userIds: messageData.userIds\n        });\n    },\n    /**\n   * Send promotional notification\n   */ async sendPromotion (messageData) {\n        return await _notifications__WEBPACK_IMPORTED_MODULE_0__.notificationService.sendBroadcast({\n            type: _notifications__WEBPACK_IMPORTED_MODULE_0__.NotificationType.PROMOTIONAL,\n            title: messageData.title,\n            message: messageData.message,\n            data: messageData.data,\n            expiresAt: messageData.expiresAt,\n            sendEmail: messageData.sendEmail || false,\n            userIds: messageData.userIds\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/notification-helpers.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/notifications.ts":
/*!**********************************!*\
  !*** ./app/lib/notifications.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationType: () => (/* binding */ NotificationType),\n/* harmony export */   notificationService: () => (/* binding */ notificationService)\n/* harmony export */ });\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./db */ \"(rsc)/./app/lib/db.ts\");\n/* harmony import */ var _email__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./email */ \"(rsc)/./app/lib/email.ts\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./logger */ \"(rsc)/./app/lib/logger.ts\");\n\n\n\nvar NotificationType;\n(function(NotificationType) {\n    NotificationType[\"ORDER_PLACED\"] = \"ORDER_PLACED\";\n    NotificationType[\"ORDER_CONFIRMED\"] = \"ORDER_CONFIRMED\";\n    NotificationType[\"ORDER_PROCESSING\"] = \"ORDER_PROCESSING\";\n    NotificationType[\"ORDER_SHIPPED\"] = \"ORDER_SHIPPED\";\n    NotificationType[\"ORDER_DELIVERED\"] = \"ORDER_DELIVERED\";\n    NotificationType[\"ORDER_CANCELLED\"] = \"ORDER_CANCELLED\";\n    NotificationType[\"WISHLIST_ADDED\"] = \"WISHLIST_ADDED\";\n    NotificationType[\"WISHLIST_REMOVED\"] = \"WISHLIST_REMOVED\";\n    NotificationType[\"PRICE_DROP_ALERT\"] = \"PRICE_DROP_ALERT\";\n    NotificationType[\"REVIEW_REQUEST\"] = \"REVIEW_REQUEST\";\n    NotificationType[\"REVIEW_SUBMITTED\"] = \"REVIEW_SUBMITTED\";\n    NotificationType[\"ADMIN_MESSAGE\"] = \"ADMIN_MESSAGE\";\n    NotificationType[\"BROADCAST\"] = \"BROADCAST\";\n    NotificationType[\"PROMOTIONAL\"] = \"PROMOTIONAL\";\n    NotificationType[\"SYSTEM\"] = \"SYSTEM\";\n})(NotificationType || (NotificationType = {}));\nclass NotificationService {\n    async createNotification(options) {\n        try {\n            const userPreferences = await _db__WEBPACK_IMPORTED_MODULE_0__.prisma.userPreference.findUnique({\n                where: {\n                    userId: options.userId\n                }\n            });\n            if (!this.shouldSendNotification(options.type, userPreferences)) {\n                _logger__WEBPACK_IMPORTED_MODULE_2__.logger.info(`Notification skipped for user ${options.userId} due to preferences`);\n                return null;\n            }\n            const notification = await _db__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.create({\n                data: {\n                    userId: options.userId,\n                    type: options.type,\n                    title: options.title,\n                    message: options.message,\n                    data: options.data || {},\n                    expiresAt: options.expiresAt,\n                    templateId: options.templateId\n                },\n                include: {\n                    user: true,\n                    template: true\n                }\n            });\n            if (options.sendEmail && userPreferences?.emailNotifications !== false) {\n                await this.sendEmailNotification(notification);\n            }\n            _logger__WEBPACK_IMPORTED_MODULE_2__.logger.info(`Notification created: ${notification.id} for user ${options.userId}`);\n            return notification;\n        } catch (error) {\n            _logger__WEBPACK_IMPORTED_MODULE_2__.logger.error(\"Error creating notification:\", error);\n            throw error;\n        }\n    }\n    async sendBroadcast(options) {\n        try {\n            let targetUsers;\n            if (options.userIds && options.userIds.length > 0) {\n                targetUsers = await _db__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findMany({\n                    where: {\n                        id: {\n                            in: options.userIds\n                        }\n                    },\n                    include: {\n                        preferences: true\n                    }\n                });\n            } else {\n                targetUsers = await _db__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findMany({\n                    include: {\n                        preferences: true\n                    },\n                    where: {\n                        OR: [\n                            {\n                                preferences: {\n                                    broadcastMessages: true\n                                }\n                            },\n                            {\n                                preferences: null\n                            }\n                        ]\n                    }\n                });\n            }\n            const notifications = [];\n            for (const user of targetUsers){\n                if (this.shouldSendNotification(options.type, user.preferences)) {\n                    const notification = await _db__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.create({\n                        data: {\n                            userId: user.id,\n                            type: options.type,\n                            title: options.title,\n                            message: options.message,\n                            data: options.data || {},\n                            expiresAt: options.expiresAt,\n                            templateId: options.templateId\n                        }\n                    });\n                    notifications.push(notification);\n                    if (options.sendEmail && user.preferences?.emailNotifications !== false) {\n                        await this.sendEmailNotification({\n                            ...notification,\n                            user,\n                            template: null\n                        });\n                    }\n                }\n            }\n            _logger__WEBPACK_IMPORTED_MODULE_2__.logger.info(`Broadcast sent to ${notifications.length} users`);\n            return notifications;\n        } catch (error) {\n            _logger__WEBPACK_IMPORTED_MODULE_2__.logger.error(\"Error sending broadcast:\", error);\n            throw error;\n        }\n    }\n    async markAsRead(notificationId, userId) {\n        try {\n            const notification = await _db__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.update({\n                where: {\n                    id: notificationId,\n                    userId: userId\n                },\n                data: {\n                    isRead: true\n                }\n            });\n            return notification;\n        } catch (error) {\n            _logger__WEBPACK_IMPORTED_MODULE_2__.logger.error(\"Error marking notification as read:\", error);\n            throw error;\n        }\n    }\n    async markAllAsRead(userId) {\n        try {\n            const result = await _db__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.updateMany({\n                where: {\n                    userId: userId,\n                    isRead: false\n                },\n                data: {\n                    isRead: true\n                }\n            });\n            return result;\n        } catch (error) {\n            _logger__WEBPACK_IMPORTED_MODULE_2__.logger.error(\"Error marking all notifications as read:\", error);\n            throw error;\n        }\n    }\n    async getUserNotifications(userId, options = {}) {\n        try {\n            const { page = 1, limit = 20, unreadOnly = false, type } = options;\n            const skip = (page - 1) * limit;\n            const where = {\n                userId: userId,\n                OR: [\n                    {\n                        expiresAt: null\n                    },\n                    {\n                        expiresAt: {\n                            gt: new Date()\n                        }\n                    }\n                ]\n            };\n            if (unreadOnly) {\n                where.isRead = false;\n            }\n            if (type) {\n                where.type = type;\n            }\n            const [notifications, total] = await Promise.all([\n                _db__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.findMany({\n                    where,\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    skip,\n                    take: limit,\n                    include: {\n                        template: true\n                    }\n                }),\n                _db__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.count({\n                    where\n                })\n            ]);\n            return {\n                notifications,\n                total,\n                page,\n                limit,\n                totalPages: Math.ceil(total / limit)\n            };\n        } catch (error) {\n            _logger__WEBPACK_IMPORTED_MODULE_2__.logger.error(\"Error getting user notifications:\", error);\n            throw error;\n        }\n    }\n    async getUnreadCount(userId) {\n        try {\n            const count = await _db__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.count({\n                where: {\n                    userId: userId,\n                    isRead: false,\n                    OR: [\n                        {\n                            expiresAt: null\n                        },\n                        {\n                            expiresAt: {\n                                gt: new Date()\n                            }\n                        }\n                    ]\n                }\n            });\n            return count;\n        } catch (error) {\n            _logger__WEBPACK_IMPORTED_MODULE_2__.logger.error(\"Error getting unread count:\", error);\n            throw error;\n        }\n    }\n    async cleanupOldNotifications(daysOld = 30) {\n        try {\n            const cutoffDate = new Date();\n            cutoffDate.setDate(cutoffDate.getDate() - daysOld);\n            const result = await _db__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.deleteMany({\n                where: {\n                    createdAt: {\n                        lt: cutoffDate\n                    },\n                    isRead: true\n                }\n            });\n            _logger__WEBPACK_IMPORTED_MODULE_2__.logger.info(`Cleaned up ${result.count} old notifications`);\n            return result;\n        } catch (error) {\n            _logger__WEBPACK_IMPORTED_MODULE_2__.logger.error(\"Error cleaning up notifications:\", error);\n            throw error;\n        }\n    }\n    async sendEmailNotification(notification) {\n        try {\n            const user = notification.user;\n            if (!user?.email) {\n                throw new Error(\"User email not found\");\n            }\n            const emailSubject = notification.template?.emailSubject || notification.title;\n            const emailContent = this.generateEmailContent(notification);\n            await (0,_email__WEBPACK_IMPORTED_MODULE_1__.sendEmail)({\n                to: user.email,\n                subject: emailSubject,\n                html: emailContent\n            });\n            await _db__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.update({\n                where: {\n                    id: notification.id\n                },\n                data: {\n                    emailSent: true,\n                    emailSentAt: new Date()\n                }\n            });\n            _logger__WEBPACK_IMPORTED_MODULE_2__.logger.info(`Email notification sent to ${user.email}`);\n        } catch (error) {\n            await _db__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.update({\n                where: {\n                    id: notification.id\n                },\n                data: {\n                    emailError: error instanceof Error ? error.message : \"Unknown error\"\n                }\n            });\n            _logger__WEBPACK_IMPORTED_MODULE_2__.logger.error(\"Error sending email notification:\", error);\n        }\n    }\n    shouldSendNotification(type, preferences) {\n        if (!preferences) return true;\n        if (!preferences.inAppNotifications) return false;\n        switch(type){\n            case \"ORDER_PLACED\":\n            case \"ORDER_CONFIRMED\":\n            case \"ORDER_PROCESSING\":\n            case \"ORDER_SHIPPED\":\n            case \"ORDER_DELIVERED\":\n            case \"ORDER_CANCELLED\":\n                return preferences.orderNotifications;\n            case \"WISHLIST_ADDED\":\n            case \"WISHLIST_REMOVED\":\n                return preferences.wishlistNotifications;\n            case \"PRICE_DROP_ALERT\":\n                return preferences.priceDropAlerts;\n            case \"REVIEW_REQUEST\":\n            case \"REVIEW_SUBMITTED\":\n                return preferences.reviewNotifications;\n            case \"ADMIN_MESSAGE\":\n                return preferences.adminMessages;\n            case \"BROADCAST\":\n            case \"PROMOTIONAL\":\n                return preferences.broadcastMessages;\n            default:\n                return true;\n        }\n    }\n    generateEmailContent(notification) {\n        const baseUrl = process.env.NEXTAUTH_URL || \"http://localhost:3000\";\n        return `\n      <div style=\"max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;\">\n        <div style=\"text-align: center; margin-bottom: 30px;\">\n          <h1 style=\"color: #2d5a27; margin: 0;\">Herbalicious</h1>\n          <p style=\"color: #666; margin: 5px 0 0 0;\">Natural Skincare Essentials</p>\n        </div>\n        \n        <div style=\"background: #f9f9f9; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n          <h2 style=\"color: #333; margin: 0 0 15px 0;\">${notification.title}</h2>\n          <p style=\"color: #666; line-height: 1.6; margin: 0;\">${notification.message}</p>\n        </div>\n        \n        ${notification.data && Object.keys(notification.data).length > 0 ? `\n          <div style=\"margin-bottom: 20px;\">\n            <h3 style=\"color: #333; margin: 0 0 10px 0;\">Details:</h3>\n            ${this.formatNotificationData(notification.data)}\n          </div>\n        ` : \"\"}\n        \n        <div style=\"text-align: center; margin: 30px 0;\">\n          <a href=\"${baseUrl}\" \n             style=\"background-color: #2d5a27; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;\">\n            Visit Herbalicious\n          </a>\n        </div>\n        \n        <div style=\"text-align: center; color: #999; font-size: 12px; margin-top: 30px;\">\n          <p>You received this notification because you have an account with Herbalicious.</p>\n          <p>You can manage your notification preferences in your account settings.</p>\n        </div>\n      </div>\n    `;\n    }\n    formatNotificationData(data) {\n        let html = '<ul style=\"color: #666; line-height: 1.6;\">';\n        if (data.orderNumber) {\n            html += `<li><strong>Order Number:</strong> ${data.orderNumber}</li>`;\n        }\n        if (data.productName) {\n            html += `<li><strong>Product:</strong> ${data.productName}</li>`;\n        }\n        if (data.amount && data.currency) {\n            html += `<li><strong>Amount:</strong> ${data.currency} ${data.amount}</li>`;\n        }\n        html += \"</ul>\";\n        return html;\n    }\n}\nconst notificationService = new NotificationService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/notifications.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/nodemailer"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwishlist%2Froute&page=%2Fapi%2Fwishlist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwishlist%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();