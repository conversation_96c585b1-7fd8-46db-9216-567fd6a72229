import { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import * as bcrypt from "bcryptjs";
import { prisma } from "./db";
import { UserRole } from "@prisma/client";

interface ExtendedUser {
  id: string;
  email: string;
  name?: string | null;
  role: string;
}

export const authOptions: NextAuthOptions = {
  // Remove PrismaAdapter when using JWT strategy
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Invalid credentials");
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email }
        });

        if (!user || !user.password) {
          throw new Error("Invalid credentials");
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        );

        if (!isPasswordValid) {
          throw new Error("Invalid credentials");
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        } as ExtendedUser;
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 7 * 24 * 60 * 60, // 7 days default
    updateAge: 24 * 60 * 60, // Update session every 24 hours
  },
  jwt: {
    maxAge: 7 * 24 * 60 * 60, // 7 days default
  },
  // Enable CSRF protection
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === 'production'
        ? `__Secure-next-auth.session-token`
        : `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production'
      }
    },
    callbackUrl: {
      name: process.env.NODE_ENV === 'production'
        ? `__Secure-next-auth.callback-url`
        : `next-auth.callback-url`,
      options: {
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production'
      }
    },
    csrfToken: {
      name: process.env.NODE_ENV === 'production'
        ? `__Host-next-auth.csrf-token`
        : `next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production'
      }
    },
  },
  callbacks: {
    async jwt({ token, user, account, profile }) {
      // Handle initial sign in
      if (account && user) {
        // For OAuth providers, create/update user in database
        if (account.provider === "google") {
          try {
            const email = user.email || (profile as any)?.email;
            if (!email) {
              throw new Error("No email found for Google account");
            }

            // Check if user exists
            let dbUser = await prisma.user.findUnique({
              where: { email },
              select: { id: true, role: true, name: true, email: true }
            });

            // Create user if doesn't exist
            if (!dbUser) {
              dbUser = await prisma.user.create({
                data: {
                  email,
                  name: user.name || (profile as any)?.name || email.split('@')[0],
                  // Don't save Google profile picture
                  role: UserRole.CUSTOMER,
                  emailVerified: new Date(), // OAuth users are considered verified
                },
                select: { id: true, role: true, name: true, email: true }
              });
            }

            // Set token properties
            token.sub = dbUser.id;
            token.role = dbUser.role;
            token.email = dbUser.email;
            token.name = dbUser.name;
          } catch (error) {
            console.error('Error handling Google sign in:', error);
            throw error; // This will prevent sign in
          }
        } else if (user) {
          // For credentials provider
          token.sub = user.id;
          token.role = (user as ExtendedUser).role;
        }
      }
      
      return token;
    },
    async session({ session, token }) {
      // Populate session with user data from token
      if (token && session.user) {
        session.user = {
          ...session.user,
          id: token.sub as string,
          role: token.role as string,
          email: token.email as string,
          name: token.name as string | null,
          // Don't include image from Google
        };
      }
      
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
  },
  events: {
    async signIn({ user, account, profile, isNewUser }) {
      // Log sign in events in development only
      if (process.env.NODE_ENV === 'development') {
        console.log('Sign in event:', {
          userId: user.id,
          email: user.email,
          provider: account?.provider,
          isNewUser
        });
      }
    },
  },
  pages: {
    signIn: "/login",
    signOut: "/",
    error: "/login",
  },
  secret: process.env.NEXTAUTH_SECRET,
  // Only enable debug in development when explicitly set
  debug: process.env.NODE_ENV === 'development' && process.env.NEXTAUTH_DEBUG === 'true',
};