import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/app/lib/db';

// GET /api/products/filters - Get optimized filter data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    // Build base where clause
    const where: any = { isActive: true };
    if (category) {
      where.OR = [
        { category: { slug: category } },
        { productCategories: { some: { category: { slug: category } } } }
      ];
    }

    // Get optimized filter data in parallel
    const [priceRange, categoriesRaw, totalCount] = await Promise.all([
      // Price range
      prisma.product.aggregate({
        where,
        _min: { price: true },
        _max: { price: true },
      }),
      
      // Categories with product counts (optimized)
      prisma.$queryRaw`
        SELECT 
          c.id::text as id, 
          c.name, 
          c.slug, 
          c.description,
          COUNT(DISTINCT p.id)::int as product_count
        FROM "categories" c
        LEFT JOIN "products" p ON (
          p."categoryId" = c.id OR 
          EXISTS (
            SELECT 1 FROM "product_categories" pc 
            WHERE pc."categoryId" = c.id AND pc."productId" = p.id
          )
        ) AND p."isActive" = true
        WHERE c."isActive" = true
        GROUP BY c.id, c.name, c.slug, c.description
        ORDER BY c.name ASC
      `,
      
      // Total count
      prisma.product.count({ where }),
    ]);

    // Convert results
    const processedCategories = (categoriesRaw as any[]).map(cat => ({
      id: cat.id,
      name: cat.name,
      slug: cat.slug,
      description: cat.description,
      product_count: cat.product_count
    }));

    return NextResponse.json({
      success: true,
      data: {
        priceRange: {
          min: Number(priceRange._min.price) || 0,
          max: Number(priceRange._max.price) || 1000,
        },
        categories: processedCategories,
        totalProducts: Number(totalCount),
      },
    });
  } catch (error) {
    console.error('Error fetching filter data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch filter data' },
      { status: 500 }
    );
  }
}