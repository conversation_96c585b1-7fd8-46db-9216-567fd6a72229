import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../lib/auth';
import { prisma } from '../../../lib/db';

// Define valid review status values
const VALID_REVIEW_STATUSES = ['PENDING', 'APPROVED', 'REJECTED'] as const;
type ValidReviewStatus = typeof VALID_REVIEW_STATUSES[number];

function isValidReviewStatus(status: string): status is ValidReviewStatus {
  return VALID_REVIEW_STATUSES.includes(status as ValidReviewStatus);
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Get user from database to verify role and existence
    let user;
    if (session.user.id) {
      user = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { id: true, role: true, email: true }
      });
    } else if (session.user.email) {
      user = await prisma.user.findUnique({
        where: { email: session.user.email },
        select: { id: true, role: true, email: true }
      });
    }

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const statusParam = searchParams.get('status') || 'PENDING';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    
    // Validate the status parameter
    const status: ValidReviewStatus = isValidReviewStatus(statusParam) ? statusParam : 'PENDING';
    
    const skip = (page - 1) * limit;

    const reviews = await prisma.review.findMany({
      where: {
        status: status
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        product: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    });

    const total = await prisma.review.count({
      where: {
        status: status
      }
    });

    return NextResponse.json({
      success: true,
      data: reviews,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching reviews:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch reviews' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Get user from database to verify role and existence  
    let user;
    if (session.user.id) {
      user = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { id: true, role: true, email: true }
      });
    } else if (session.user.email) {
      user = await prisma.user.findUnique({
        where: { email: session.user.email },
        select: { id: true, role: true, email: true }
      });
    }

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 401 });
    }

    const body = await request.json();
    const { reviewId, status: statusParam } = body;

    // Validate the status parameter
    if (!reviewId || !statusParam || !['APPROVED', 'REJECTED'].includes(statusParam)) {
      return NextResponse.json(
        { error: 'Invalid request. Status must be APPROVED or REJECTED' },
        { status: 400 }
      );
    }

    const status: 'APPROVED' | 'REJECTED' = statusParam as 'APPROVED' | 'REJECTED';

    const review = await prisma.review.update({
      where: {
        id: reviewId
      },
      data: {
        status: status
      }
    });

    return NextResponse.json({
      success: true,
      data: review,
      message: `Review ${status.toLowerCase()} successfully`
    });

  } catch (error) {
    console.error('Error updating review:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update review' },
      { status: 500 }
    );
  }
}